(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9662],{6264:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(5155),a=t(3930),c=t(9730);async function n(e){let{params:s}=e,{id:t}=await s;return(0,r.jsx)(a.N,{children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Edit Project"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Update your project information"})]}),(0,r.jsx)(c.o,{projectId:t})]})})}},6755:(e,s,t)=>{Promise.resolve().then(t.bind(t,6264))}},e=>{var s=s=>e(e.s=s);e.O(0,[2108,6321,7658,5425,322,6985,9730,8441,1684,7358],()=>s(6755)),_N_E=e.O()}]);