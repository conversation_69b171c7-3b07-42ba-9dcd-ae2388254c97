(()=>{var e={};e.id=680,e.ids=[680],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4780:(e,t,r)=>{"use strict";r.d(t,{KE:()=>p,_C:()=>u,cn:()=>l,z9:()=>c});var s=r(49384),n=r(82348),i=r(85126),o=r.n(i),a=r(26728),d=r.n(a);function l(...e){return(0,n.QP)((0,s.$)(e))}function c(e){return o()(e,{lower:!0,strict:!0,remove:/[*+~.()'"!:@]/g})}function u(e){return Math.ceil(d()(e).minutes)}function p(e){try{let t=JSON.parse(e);if("doc"===t.type&&t.content)return function e(t){let r="";for(let s of t)"text"===s.type?r+=s.text||"":s.content&&(r+=e(s.content)),["paragraph","heading","listItem"].includes(s.type)&&(r+=" ");return r.trim()}(t.content)}catch{}return e.replace(/<[^>]*>/g," ").replace(/\s+/g," ").trim()}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var s=r(60687);r(43210);var n=r(8730),i=r(24224),o=r(4780);let a=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:t,size:r,asChild:i=!1,...d}){let l=i?n.DX:"button";return(0,s.jsx)(l,{"data-slot":"button",className:(0,o.cn)(a({variant:t,size:r,className:e})),...d})}},33873:e=>{"use strict";e.exports=require("path")},43476:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(60687),n=r(43210),i=r(82136),o=r(16189),a=r(29523),d=r(89667),l=r(80013),c=r(44493);function u(){let[e,t]=(0,n.useState)(""),[r,u]=(0,n.useState)(""),[p,m]=(0,n.useState)(!1),[h,v]=(0,n.useState)(""),f=(0,o.useRouter)(),x=async t=>{t.preventDefault(),m(!0),v("");try{let t=await (0,i.signIn)("credentials",{email:e,password:r,redirect:!1});t?.error?v("Invalid credentials"):await (0,i.getSession)()&&f.push("/dashboard")}catch(e){v("An error occurred. Please try again.")}finally{m(!1)}};return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,s.jsxs)(c.Zp,{className:"w-full max-w-md",children:[(0,s.jsxs)(c.aR,{className:"space-y-1",children:[(0,s.jsx)(c.ZB,{className:"text-2xl text-center",children:"Portfolio CMS"}),(0,s.jsx)(c.BT,{className:"text-center",children:"Sign in to access the content management system"})]}),(0,s.jsx)(c.Wu,{children:(0,s.jsxs)("form",{onSubmit:x,className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"email",children:"Email"}),(0,s.jsx)(d.p,{id:"email",type:"email",placeholder:"<EMAIL>",value:e,onChange:e=>t(e.target.value),required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"password",children:"Password"}),(0,s.jsx)(d.p,{id:"password",type:"password",value:r,onChange:e=>u(e.target.value),required:!0})]}),h&&(0,s.jsx)("div",{className:"text-red-500 text-sm text-center",children:h}),(0,s.jsx)(a.$,{type:"submit",className:"w-full",disabled:p,children:p?"Signing in...":"Sign In"})]})})]})})}},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>a,Zp:()=>i,aR:()=>o});var s=r(60687);r(43210);var n=r(4780);function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function a({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",e),...t})}},46627:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},49633:(e,t,r)=>{Promise.resolve().then(r.bind(r,83305))},50100:(e,t,r)=>{Promise.resolve().then(r.bind(r,87578))},58034:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var s=r(65239),n=r(48088),i=r(88170),o=r.n(i),a=r(30893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);r.d(t,d);let l={children:["",{children:["auth",{children:["signin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,87578)),"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\auth\\signin\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\auth\\signin\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/auth/signin/page",pathname:"/auth/signin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},60699:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},78148:(e,t,r)=>{"use strict";r.d(t,{b:()=>a});var s=r(43210),n=r(14163),i=r(60687),o=s.forwardRef((e,t)=>(0,i.jsx)(n.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var a=o},79551:e=>{"use strict";e.exports=require("url")},80013:(e,t,r)=>{"use strict";r.d(t,{J:()=>o});var s=r(60687);r(43210);var n=r(78148),i=r(4780);function o({className:e,...t}){return(0,s.jsx)(n.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},80415:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>i});var s=r(60687),n=r(82136);function i({children:e}){return(0,s.jsx)(n.SessionProvider,{children:e})}},83305:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\providers\\session-provider.tsx","AuthProvider")},86081:(e,t,r)=>{Promise.resolve().then(r.bind(r,80415))},86484:(e,t,r)=>{Promise.resolve().then(r.bind(r,43476))},87578:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\portfolio\\\\portfolio-cms\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\auth\\signin\\page.tsx","default")},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var s=r(60687);r(43210);var n=r(4780);function i({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>l});var s=r(37413),n=r(22376),i=r.n(n),o=r(68726),a=r.n(o);r(61135);var d=r(83305);let l={title:"Portfolio CMS",description:"Content Management System for Portfolio Website"};function c({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${i().variable} ${a().variable} antialiased`,children:(0,s.jsx)(d.AuthProvider,{children:e})})})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[243,310,934,671],()=>r(58034));module.exports=s})();