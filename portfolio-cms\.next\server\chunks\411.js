"use strict";exports.id=411,exports.ids=[411],exports.modules={6211:(e,t,a)=>{a.d(t,{A0:()=>s,BF:()=>i,Hj:()=>l,XI:()=>o,nA:()=>c,nd:()=>d});var r=a(60687);a(43210);var n=a(4780);function o({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,r.jsx)("table",{"data-slot":"table",className:(0,n.cn)("w-full caption-bottom text-sm",e),...t})})}function s({className:e,...t}){return(0,r.jsx)("thead",{"data-slot":"table-header",className:(0,n.cn)("[&_tr]:border-b",e),...t})}function i({className:e,...t}){return(0,r.jsx)("tbody",{"data-slot":"table-body",className:(0,n.cn)("[&_tr:last-child]:border-0",e),...t})}function l({className:e,...t}){return(0,r.jsx)("tr",{"data-slot":"table-row",className:(0,n.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function d({className:e,...t}){return(0,r.jsx)("th",{"data-slot":"table-head",className:(0,n.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function c({className:e,...t}){return(0,r.jsx)("td",{"data-slot":"table-cell",className:(0,n.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},19312:(e,t,a)=>{a.d(t,{B:()=>p});var r=a(60687),n=a(43210),o=a(29523),s=a(89667),i=a(80013),l=a(11860),d=a(9005),c=a(16023),u=a(30474);function p({value:e,onChange:t,onRemove:a,disabled:p,label:f="Upload Image",className:g=""}){let[m,x]=(0,n.useState)(!1),h=(0,n.useRef)(null),b=async e=>{let a=e.target.files?.[0];if(a){x(!0);try{let e=new FormData;e.append("file",a);let r=await fetch("/api/upload",{method:"POST",body:e});if(!r.ok)throw Error("Upload failed");let n=await r.json();t(n.url)}catch(e){console.error("Error uploading image:",e),alert("Failed to upload image. Please try again.")}finally{x(!1)}}};return(0,r.jsxs)("div",{className:`space-y-2 ${g}`,children:[(0,r.jsx)(i.J,{children:f}),e?(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"relative w-full h-48 rounded-lg overflow-hidden border",children:(0,r.jsx)(u.default,{src:e,alt:"Uploaded image",fill:!0,className:"object-cover"})}),(0,r.jsx)(o.$,{type:"button",variant:"destructive",size:"sm",className:"absolute top-2 right-2",onClick:a,disabled:p,children:(0,r.jsx)(l.A,{className:"h-4 w-4"})})]}):(0,r.jsx)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(d.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsxs)(o.$,{type:"button",variant:"outline",onClick:()=>{h.current?.click()},disabled:p||m,children:[(0,r.jsx)(c.A,{className:"mr-2 h-4 w-4"}),m?"Uploading...":"Choose Image"]})}),(0,r.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"PNG, JPG, GIF up to 10MB"})]})}),(0,r.jsx)(s.p,{ref:h,type:"file",accept:"image/*",onChange:b,className:"hidden",disabled:p||m})]})}},22126:(e,t,a)=>{a.d(t,{lG:()=>ee,Cf:()=>en,rr:()=>ei,c7:()=>eo,L3:()=>es,zM:()=>et});var r=a(60687),n=a(43210),o=a(70569),s=a(98599),i=a(11273),l=a(96963),d=a(65551),c=a(31355),u=a(32547),p=a(25028),f=a(46059),g=a(14163),m=a(1359),x=a(42247),h=a(63376),b=a(8730),v="Dialog",[y,j]=(0,i.A)(v),[w,N]=y(v),k=e=>{let{__scopeDialog:t,children:a,open:o,defaultOpen:s,onOpenChange:i,modal:c=!0}=e,u=n.useRef(null),p=n.useRef(null),[f,g]=(0,d.i)({prop:o,defaultProp:s??!1,onChange:i,caller:v});return(0,r.jsx)(w,{scope:t,triggerRef:u,contentRef:p,contentId:(0,l.B)(),titleId:(0,l.B)(),descriptionId:(0,l.B)(),open:f,onOpenChange:g,onOpenToggle:n.useCallback(()=>g(e=>!e),[g]),modal:c,children:a})};k.displayName=v;var D="DialogTrigger",C=n.forwardRef((e,t)=>{let{__scopeDialog:a,...n}=e,i=N(D,a),l=(0,s.s)(t,i.triggerRef);return(0,r.jsx)(g.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":J(i.open),...n,ref:l,onClick:(0,o.m)(e.onClick,i.onOpenToggle)})});C.displayName=D;var R="DialogPortal",[A,I]=y(R,{forceMount:void 0}),F=e=>{let{__scopeDialog:t,forceMount:a,children:o,container:s}=e,i=N(R,t);return(0,r.jsx)(A,{scope:t,forceMount:a,children:n.Children.map(o,e=>(0,r.jsx)(f.C,{present:a||i.open,children:(0,r.jsx)(p.Z,{asChild:!0,container:s,children:e})}))})};F.displayName=R;var O="DialogOverlay",_=n.forwardRef((e,t)=>{let a=I(O,e.__scopeDialog),{forceMount:n=a.forceMount,...o}=e,s=N(O,e.__scopeDialog);return s.modal?(0,r.jsx)(f.C,{present:n||s.open,children:(0,r.jsx)(M,{...o,ref:t})}):null});_.displayName=O;var E=(0,b.TL)("DialogOverlay.RemoveScroll"),M=n.forwardRef((e,t)=>{let{__scopeDialog:a,...n}=e,o=N(O,a);return(0,r.jsx)(x.A,{as:E,allowPinchZoom:!0,shards:[o.contentRef],children:(0,r.jsx)(g.sG.div,{"data-state":J(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),P="DialogContent",z=n.forwardRef((e,t)=>{let a=I(P,e.__scopeDialog),{forceMount:n=a.forceMount,...o}=e,s=N(P,e.__scopeDialog);return(0,r.jsx)(f.C,{present:n||s.open,children:s.modal?(0,r.jsx)(G,{...o,ref:t}):(0,r.jsx)(B,{...o,ref:t})})});z.displayName=P;var G=n.forwardRef((e,t)=>{let a=N(P,e.__scopeDialog),i=n.useRef(null),l=(0,s.s)(t,a.contentRef,i);return n.useEffect(()=>{let e=i.current;if(e)return(0,h.Eq)(e)},[]),(0,r.jsx)($,{...e,ref:l,trapFocus:a.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),a.triggerRef.current?.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,a=0===t.button&&!0===t.ctrlKey;(2===t.button||a)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),B=n.forwardRef((e,t)=>{let a=N(P,e.__scopeDialog),o=n.useRef(!1),s=n.useRef(!1);return(0,r.jsx)($,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||a.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,s.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(s.current=!0));let r=t.target;a.triggerRef.current?.contains(r)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&s.current&&t.preventDefault()}})}),$=n.forwardRef((e,t)=>{let{__scopeDialog:a,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:l,...d}=e,p=N(P,a),f=n.useRef(null),g=(0,s.s)(t,f);return(0,m.Oh)(),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(u.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:l,children:(0,r.jsx)(c.qW,{role:"dialog",id:p.contentId,"aria-describedby":p.descriptionId,"aria-labelledby":p.titleId,"data-state":J(p.open),...d,ref:g,onDismiss:()=>p.onOpenChange(!1)})}),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(K,{titleId:p.titleId}),(0,r.jsx)(X,{contentRef:f,descriptionId:p.descriptionId})]})]})}),T="DialogTitle",S=n.forwardRef((e,t)=>{let{__scopeDialog:a,...n}=e,o=N(T,a);return(0,r.jsx)(g.sG.h2,{id:o.titleId,...n,ref:t})});S.displayName=T;var q="DialogDescription",U=n.forwardRef((e,t)=>{let{__scopeDialog:a,...n}=e,o=N(q,a);return(0,r.jsx)(g.sG.p,{id:o.descriptionId,...n,ref:t})});U.displayName=q;var H="DialogClose",W=n.forwardRef((e,t)=>{let{__scopeDialog:a,...n}=e,s=N(H,a);return(0,r.jsx)(g.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>s.onOpenChange(!1))})});function J(e){return e?"open":"closed"}W.displayName=H;var L="DialogTitleWarning",[V,Z]=(0,i.q)(L,{contentName:P,titleName:T,docsSlug:"dialog"}),K=({titleId:e})=>{let t=Z(L),a=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return n.useEffect(()=>{e&&(document.getElementById(e)||console.error(a))},[a,e]),null},X=({contentRef:e,descriptionId:t})=>{let a=Z("DialogDescriptionWarning"),r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${a.contentName}}.`;return n.useEffect(()=>{let a=e.current?.getAttribute("aria-describedby");t&&a&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},Q=a(11860),Y=a(4780);function ee({...e}){return(0,r.jsx)(k,{"data-slot":"dialog",...e})}function et({...e}){return(0,r.jsx)(C,{"data-slot":"dialog-trigger",...e})}function ea({...e}){return(0,r.jsx)(F,{"data-slot":"dialog-portal",...e})}function er({className:e,...t}){return(0,r.jsx)(_,{"data-slot":"dialog-overlay",className:(0,Y.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function en({className:e,children:t,showCloseButton:a=!0,...n}){return(0,r.jsxs)(ea,{"data-slot":"dialog-portal",children:[(0,r.jsx)(er,{}),(0,r.jsxs)(z,{"data-slot":"dialog-content",className:(0,Y.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...n,children:[t,a&&(0,r.jsxs)(W,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,r.jsx)(Q.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function eo({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"dialog-header",className:(0,Y.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t})}function es({className:e,...t}){return(0,r.jsx)(S,{"data-slot":"dialog-title",className:(0,Y.cn)("text-lg leading-none font-semibold",e),...t})}function ei({className:e,...t}){return(0,r.jsx)(U,{"data-slot":"dialog-description",className:(0,Y.cn)("text-muted-foreground text-sm",e),...t})}},56896:(e,t,a)=>{a.d(t,{S:()=>i});var r=a(60687);a(43210);var n=a(40211),o=a(13964),s=a(4780);function i({className:e,...t}){return(0,r.jsx)(n.bL,{"data-slot":"checkbox",className:(0,s.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,r.jsx)(n.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,r.jsx)(o.A,{className:"size-3.5"})})})}},63143:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},80013:(e,t,a)=>{a.d(t,{J:()=>s});var r=a(60687);a(43210);var n=a(78148),o=a(4780);function s({className:e,...t}){return(0,r.jsx)(n.b,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},88233:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},89667:(e,t,a)=>{a.d(t,{p:()=>o});var r=a(60687);a(43210);var n=a(4780);function o({className:e,type:t,...a}){return(0,r.jsx)("input",{type:t,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...a})}},96474:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};