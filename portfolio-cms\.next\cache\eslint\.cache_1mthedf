[{"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\api\\auth\\[...nextauth]\\route.ts": "1", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\api\\blog\\route.ts": "2", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\api\\blog\\[id]\\route.ts": "3", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\api\\projects\\route.ts": "4", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\api\\projects\\[id]\\route.ts": "5", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\api\\services\\route.ts": "6", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\api\\services\\[id]\\route.ts": "7", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\api\\tech-stack\\route.ts": "8", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\api\\tech-stack\\[id]\\route.ts": "9", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\api\\testimonials\\route.ts": "10", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\api\\testimonials\\[id]\\route.ts": "11", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\api\\upload\\route.ts": "12", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\auth\\signin\\page.tsx": "13", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\analytics\\page.tsx": "14", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\blog\\new\\page.tsx": "15", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\blog\\page.tsx": "16", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\blog\\[id]\\page.tsx": "17", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\page.tsx": "18", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\projects\\new\\page.tsx": "19", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\projects\\page.tsx": "20", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\projects\\[id]\\page.tsx": "21", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\services\\page.tsx": "22", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\settings\\page.tsx": "23", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\tech-stack\\page.tsx": "24", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\testimonials\\page.tsx": "25", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\layout.tsx": "26", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\page.tsx": "27", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\dashboard\\header.tsx": "28", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\dashboard\\layout.tsx": "29", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\dashboard\\sidebar.tsx": "30", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\forms\\blog-form.tsx": "31", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\forms\\project-form.tsx": "32", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\providers\\session-provider.tsx": "33", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\avatar.tsx": "34", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\badge.tsx": "35", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\button.tsx": "36", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\card.tsx": "37", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\checkbox.tsx": "38", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\dialog.tsx": "39", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\dropdown-menu.tsx": "40", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\form.tsx": "41", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\image-upload.tsx": "42", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\input.tsx": "43", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\label.tsx": "44", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\navigation-menu.tsx": "45", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\rich-text-editor.tsx": "46", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\select.tsx": "47", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\separator.tsx": "48", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\sheet.tsx": "49", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\sidebar.tsx": "50", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\skeleton.tsx": "51", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\table.tsx": "52", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\textarea.tsx": "53", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\tooltip.tsx": "54", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\hooks\\use-mobile.ts": "55", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\lib\\auth.ts": "56", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\lib\\cors.ts": "57", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\lib\\prisma.ts": "58", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\lib\\utils.ts": "59", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\middleware.ts": "60", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\types\\next-auth.d.ts": "61"}, {"size": 157, "mtime": 1751355278258, "results": "62", "hashOfConfig": "63"}, {"size": 3030, "mtime": 1751420624058, "results": "64", "hashOfConfig": "63"}, {"size": 4202, "mtime": 1751420652445, "results": "65", "hashOfConfig": "63"}, {"size": 2152, "mtime": 1751358992881, "results": "66", "hashOfConfig": "63"}, {"size": 2838, "mtime": 1751356932500, "results": "67", "hashOfConfig": "63"}, {"size": 1646, "mtime": 1751359046319, "results": "68", "hashOfConfig": "63"}, {"size": 2386, "mtime": 1751357017884, "results": "69", "hashOfConfig": "63"}, {"size": 1603, "mtime": 1751359104423, "results": "70", "hashOfConfig": "63"}, {"size": 2279, "mtime": 1751357238215, "results": "71", "hashOfConfig": "63"}, {"size": 2093, "mtime": 1751359161754, "results": "72", "hashOfConfig": "63"}, {"size": 2790, "mtime": 1751357100435, "results": "73", "hashOfConfig": "63"}, {"size": 1556, "mtime": 1751355473518, "results": "74", "hashOfConfig": "63"}, {"size": 2773, "mtime": 1751355298131, "results": "75", "hashOfConfig": "63"}, {"size": 8962, "mtime": 1751356181771, "results": "76", "hashOfConfig": "63"}, {"size": 534, "mtime": 1751356714194, "results": "77", "hashOfConfig": "63"}, {"size": 6799, "mtime": 1751356788585, "results": "78", "hashOfConfig": "63"}, {"size": 683, "mtime": 1751420993693, "results": "79", "hashOfConfig": "63"}, {"size": 5667, "mtime": 1751355940310, "results": "80", "hashOfConfig": "63"}, {"size": 542, "mtime": 1751356509035, "results": "81", "hashOfConfig": "63"}, {"size": 6842, "mtime": 1751356568927, "results": "82", "hashOfConfig": "63"}, {"size": 692, "mtime": 1751421164696, "results": "83", "hashOfConfig": "63"}, {"size": 6127, "mtime": 1751356147940, "results": "84", "hashOfConfig": "63"}, {"size": 9462, "mtime": 1751421595807, "results": "85", "hashOfConfig": "63"}, {"size": 11574, "mtime": 1751357355041, "results": "86", "hashOfConfig": "63"}, {"size": 13238, "mtime": 1751356653191, "results": "87", "hashOfConfig": "63"}, {"size": 827, "mtime": 1751355369663, "results": "88", "hashOfConfig": "63"}, {"size": 589, "mtime": 1751355968739, "results": "89", "hashOfConfig": "63"}, {"size": 2289, "mtime": 1751355907825, "results": "90", "hashOfConfig": "63"}, {"size": 1153, "mtime": 1751355918098, "results": "91", "hashOfConfig": "63"}, {"size": 1980, "mtime": 1751355894827, "results": "92", "hashOfConfig": "63"}, {"size": 12573, "mtime": 1751420588588, "results": "93", "hashOfConfig": "63"}, {"size": 10352, "mtime": 1751356496140, "results": "94", "hashOfConfig": "63"}, {"size": 198, "mtime": 1751355333123, "results": "95", "hashOfConfig": "63"}, {"size": 1097, "mtime": 1751355858620, "results": "96", "hashOfConfig": "63"}, {"size": 1631, "mtime": 1751355858614, "results": "97", "hashOfConfig": "63"}, {"size": 2123, "mtime": 1751355311073, "results": "98", "hashOfConfig": "63"}, {"size": 1989, "mtime": 1751355311103, "results": "99", "hashOfConfig": "63"}, {"size": 1226, "mtime": 1751356053280, "results": "100", "hashOfConfig": "63"}, {"size": 3982, "mtime": 1751356053179, "results": "101", "hashOfConfig": "63"}, {"size": 8284, "mtime": 1751355858631, "results": "102", "hashOfConfig": "63"}, {"size": 3759, "mtime": 1751356053213, "results": "103", "hashOfConfig": "63"}, {"size": 3124, "mtime": 1751356460804, "results": "104", "hashOfConfig": "63"}, {"size": 967, "mtime": 1751355311091, "results": "105", "hashOfConfig": "63"}, {"size": 611, "mtime": 1751355311096, "results": "106", "hashOfConfig": "63"}, {"size": 6664, "mtime": 1751355858608, "results": "107", "hashOfConfig": "63"}, {"size": 9394, "mtime": 1751420971389, "results": "108", "hashOfConfig": "63"}, {"size": 6253, "mtime": 1751356053268, "results": "109", "hashOfConfig": "63"}, {"size": 699, "mtime": 1751355858561, "results": "110", "hashOfConfig": "63"}, {"size": 4090, "mtime": 1751355858570, "results": "111", "hashOfConfig": "63"}, {"size": 21633, "mtime": 1751355858535, "results": "112", "hashOfConfig": "63"}, {"size": 276, "mtime": 1751355858590, "results": "113", "hashOfConfig": "63"}, {"size": 2448, "mtime": 1751356053137, "results": "114", "hashOfConfig": "63"}, {"size": 759, "mtime": 1751356053238, "results": "115", "hashOfConfig": "63"}, {"size": 1891, "mtime": 1751355858578, "results": "116", "hashOfConfig": "63"}, {"size": 565, "mtime": 1751355858587, "results": "117", "hashOfConfig": "63"}, {"size": 1616, "mtime": 1751355271959, "results": "118", "hashOfConfig": "63"}, {"size": 592, "mtime": 1751358950506, "results": "119", "hashOfConfig": "63"}, {"size": 279, "mtime": 1751355163329, "results": "120", "hashOfConfig": "63"}, {"size": 2142, "mtime": 1751420396300, "results": "121", "hashOfConfig": "63"}, {"size": 1163, "mtime": 1751359276557, "results": "122", "hashOfConfig": "63"}, {"size": 366, "mtime": 1751355285520, "results": "123", "hashOfConfig": "63"}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "m1k10g", {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\api\\auth\\[...nextauth]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\api\\blog\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\api\\blog\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\api\\projects\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\api\\projects\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\api\\services\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\api\\services\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\api\\tech-stack\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\api\\tech-stack\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\api\\testimonials\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\api\\testimonials\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\api\\upload\\route.ts", ["307", "308"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\auth\\signin\\page.tsx", ["309"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\analytics\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\blog\\new\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\blog\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\blog\\[id]\\page.tsx", ["310"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\page.tsx", ["311", "312", "313"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\projects\\new\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\projects\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\projects\\[id]\\page.tsx", ["314"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\services\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\settings\\page.tsx", ["315", "316"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\tech-stack\\page.tsx", ["317", "318"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\testimonials\\page.tsx", ["319"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\dashboard\\header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\dashboard\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\dashboard\\sidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\forms\\blog-form.tsx", ["320", "321"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\forms\\project-form.tsx", ["322"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\providers\\session-provider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\avatar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\checkbox.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\image-upload.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\navigation-menu.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\rich-text-editor.tsx", ["323", "324", "325", "326", "327", "328", "329"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\select.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\separator.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\sheet.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\sidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\skeleton.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\table.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\ui\\tooltip.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\hooks\\use-mobile.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\lib\\auth.ts", ["330"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\lib\\cors.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\lib\\prisma.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\lib\\utils.ts", ["331"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\middleware.ts", ["332"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\types\\next-auth.d.ts", ["333"], [], {"ruleId": "334", "severity": 2, "message": "335", "line": 51, "column": 23, "nodeType": "336", "messageId": "337", "endLine": 51, "endColumn": 26, "suggestions": "338"}, {"ruleId": "334", "severity": 2, "message": "335", "line": 52, "column": 29, "nodeType": "336", "messageId": "337", "endLine": 52, "endColumn": 32, "suggestions": "339"}, {"ruleId": "340", "severity": 2, "message": "341", "line": 38, "column": 14, "nodeType": null, "messageId": "342", "endLine": 38, "endColumn": 19}, {"ruleId": "343", "severity": 1, "message": "344", "line": 12, "column": 1, "nodeType": "345", "endLine": 28, "endColumn": 2}, {"ruleId": "340", "severity": 2, "message": "346", "line": 12, "column": 3, "nodeType": null, "messageId": "342", "endLine": 12, "endColumn": 6}, {"ruleId": "340", "severity": 2, "message": "347", "line": 13, "column": 3, "nodeType": null, "messageId": "342", "endLine": 13, "endColumn": 8}, {"ruleId": "340", "severity": 2, "message": "348", "line": 14, "column": 3, "nodeType": null, "messageId": "342", "endLine": 14, "endColumn": 13}, {"ruleId": "343", "severity": 1, "message": "344", "line": 12, "column": 1, "nodeType": "345", "endLine": 28, "endColumn": 2}, {"ruleId": "340", "severity": 2, "message": "349", "line": 10, "column": 10, "nodeType": null, "messageId": "342", "endLine": 10, "endColumn": 19}, {"ruleId": "340", "severity": 2, "message": "350", "line": 25, "column": 19, "nodeType": null, "messageId": "342", "endLine": 25, "endColumn": 29}, {"ruleId": "340", "severity": 2, "message": "351", "line": 16, "column": 30, "nodeType": null, "messageId": "342", "endLine": 16, "endColumn": 37}, {"ruleId": "340", "severity": 2, "message": "352", "line": 16, "column": 39, "nodeType": null, "messageId": "342", "endLine": 16, "endColumn": 48}, {"ruleId": "353", "severity": 1, "message": "354", "line": 331, "column": 29, "nodeType": "355", "endLine": 335, "endColumn": 31}, {"ruleId": "356", "severity": 1, "message": "357", "line": 74, "column": 6, "nodeType": "358", "endLine": 74, "endColumn": 14, "suggestions": "359"}, {"ruleId": "356", "severity": 1, "message": "360", "line": 82, "column": 6, "nodeType": "358", "endLine": 82, "endColumn": 48, "suggestions": "361"}, {"ruleId": "356", "severity": 1, "message": "362", "line": 67, "column": 6, "nodeType": "358", "endLine": 67, "endColumn": 17, "suggestions": "363"}, {"ruleId": "340", "severity": 2, "message": "364", "line": 21, "column": 3, "nodeType": null, "messageId": "342", "endLine": 21, "endColumn": 12}, {"ruleId": "340", "severity": 2, "message": "365", "line": 35, "column": 3, "nodeType": null, "messageId": "342", "endLine": 35, "endColumn": 10}, {"ruleId": "340", "severity": 2, "message": "366", "line": 51, "column": 3, "nodeType": null, "messageId": "342", "endLine": 51, "endColumn": 14}, {"ruleId": "340", "severity": 2, "message": "367", "line": 54, "column": 10, "nodeType": null, "messageId": "342", "endLine": 54, "endColumn": 25}, {"ruleId": "340", "severity": 2, "message": "368", "line": 54, "column": 27, "nodeType": null, "messageId": "342", "endLine": 54, "endColumn": 45}, {"ruleId": "340", "severity": 2, "message": "369", "line": 55, "column": 10, "nodeType": null, "messageId": "342", "endLine": 55, "endColumn": 17}, {"ruleId": "340", "severity": 2, "message": "370", "line": 55, "column": 19, "nodeType": null, "messageId": "342", "endLine": 55, "endColumn": 29}, {"ruleId": "334", "severity": 2, "message": "335", "line": 8, "column": 37, "nodeType": "336", "messageId": "337", "endLine": 8, "endColumn": 40, "suggestions": "371"}, {"ruleId": "334", "severity": 2, "message": "335", "line": 57, "column": 38, "nodeType": "336", "messageId": "337", "endLine": 57, "endColumn": 41, "suggestions": "372"}, {"ruleId": "340", "severity": 2, "message": "373", "line": 4, "column": 23, "nodeType": null, "messageId": "342", "endLine": 4, "endColumn": 26}, {"ruleId": "340", "severity": 2, "message": "374", "line": 1, "column": 8, "nodeType": null, "messageId": "342", "endLine": 1, "endColumn": 16}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["375", "376"], ["377", "378"], "@typescript-eslint/no-unused-vars", "'error' is defined but never used.", "unusedVar", "@next/next/no-async-client-component", "Prevent client components from being async functions. See: https://nextjs.org/docs/messages/no-async-client-component", "ExportDefaultDeclaration", "'Eye' is defined but never used.", "'Users' is defined but never used.", "'TrendingUp' is defined but never used.", "'Separator' is defined but never used.", "'setLoading' is assigned a value but never used.", "'ArrowUp' is defined but never used.", "'ArrowDown' is defined but never used.", "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchPost'. Either include it or remove the dependency array.", "ArrayExpression", ["379"], "React Hook useEffect has a missing dependency: 'formData.slug'. Either include it or remove the dependency array.", ["380"], "React Hook useEffect has a missing dependency: 'fetchProject'. Either include it or remove the dependency array.", ["381"], "'Underline' is defined but never used.", "'Palette' is defined but never used.", "'placeholder' is assigned a value but never used.", "'isLinkModalOpen' is assigned a value but never used.", "'setIsLinkModalOpen' is assigned a value but never used.", "'linkUrl' is assigned a value but never used.", "'setLinkUrl' is assigned a value but never used.", ["382", "383"], ["384", "385"], "'req' is defined but never used.", "'NextAuth' is defined but never used.", {"messageId": "386", "fix": "387", "desc": "388"}, {"messageId": "389", "fix": "390", "desc": "391"}, {"messageId": "386", "fix": "392", "desc": "388"}, {"messageId": "389", "fix": "393", "desc": "391"}, {"desc": "394", "fix": "395"}, {"desc": "396", "fix": "397"}, {"desc": "398", "fix": "399"}, {"messageId": "386", "fix": "400", "desc": "388"}, {"messageId": "389", "fix": "401", "desc": "391"}, {"messageId": "386", "fix": "402", "desc": "388"}, {"messageId": "389", "fix": "403", "desc": "391"}, "suggestUnknown", {"range": "404", "text": "405"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "406", "text": "407"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "408", "text": "405"}, {"range": "409", "text": "407"}, "Update the dependencies array to be: [fetchPost, postId]", {"range": "410", "text": "411"}, "Update the dependencies array to be: [formData.title, autoGenerateSlug, postId, formData.slug]", {"range": "412", "text": "413"}, "Update the dependencies array to be: [fetchProject, projectId]", {"range": "414", "text": "415"}, {"range": "416", "text": "405"}, {"range": "417", "text": "407"}, {"range": "418", "text": "405"}, {"range": "419", "text": "407"}, [1312, 1315], "unknown", [1312, 1315], "never", [1357, 1360], [1357, 1360], [1949, 1957], "[fetchPost, postId]", [2204, 2246], "[formData.title, autoGenerateSlug, postId, formData.slug]", [1664, 1675], "[fetchProject, projectId]", [310, 313], [310, 313], [1416, 1419], [1416, 1419]]