{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/portfolio-cms/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/portfolio-cms/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport Credential<PERSON><PERSON>rovider from 'next-auth/providers/credentials'\nimport { PrismaAdapter } from '@auth/prisma-adapter'\nimport { prisma } from './prisma'\nimport bcrypt from 'bcryptjs'\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(prisma) as any,\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          }\n        })\n\n        if (!user) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          name: user.name,\n          role: user.role,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/auth/signin',\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;;;;;AAEO,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,sJAAA,CAAA,gBAAa,AAAD,EAAE,sHAAA,CAAA,SAAM;IAC7B,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;gBACjB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;YACxB;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;YAChC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;IACV;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 242, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/portfolio-cms/src/lib/cors.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\n\nexport function corsHeaders() {\n  return {\n    'Access-Control-Allow-Origin': '*',\n    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n    'Access-Control-Allow-Headers': 'Content-Type, Authorization',\n  }\n}\n\nexport function withCors(response: NextResponse) {\n  const headers = corsHeaders()\n  Object.entries(headers).forEach(([key, value]) => {\n    response.headers.set(key, value)\n  })\n  return response\n}\n\nexport function handleOptions() {\n  return new NextResponse(null, {\n    status: 200,\n    headers: corsHeaders(),\n  })\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEO,SAAS;IACd,OAAO;QACL,+BAA+B;QAC/B,gCAAgC;QAChC,gCAAgC;IAClC;AACF;AAEO,SAAS,SAAS,QAAsB;IAC7C,MAAM,UAAU;IAChB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;QAC3C,SAAS,OAAO,CAAC,GAAG,CAAC,KAAK;IAC5B;IACA,OAAO;AACT;AAEO,SAAS;IACd,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;QAC5B,QAAQ;QACR,SAAS;IACX;AACF", "debugId": null}}, {"offset": {"line": 275, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/portfolio-cms/src/app/api/testimonials/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getServerSession } from 'next-auth'\nimport { authOptions } from '@/lib/auth'\nimport { prisma } from '@/lib/prisma'\nimport { withCors, handleOptions } from '@/lib/cors'\n\nexport async function GET() {\n  try {\n    const testimonials = await prisma.testimonial.findMany({\n      include: {\n        author: {\n          select: {\n            id: true,\n            name: true,\n            email: true,\n          },\n        },\n      },\n      orderBy: {\n        order: 'asc',\n      },\n    })\n\n    return withCors(NextResponse.json(testimonials))\n  } catch (error) {\n    console.error('Error fetching testimonials:', error)\n    return withCors(NextResponse.json(\n      { error: 'Failed to fetch testimonials' },\n      { status: 500 }\n    ))\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session?.user) {\n      return NextResponse.json(\n        { error: 'Unauthorized' },\n        { status: 401 }\n      )\n    }\n\n    const body = await request.json()\n    const {\n      name,\n      role,\n      company,\n      content,\n      avatar,\n      rating,\n      featured,\n      published,\n      order,\n    } = body\n\n    const testimonial = await prisma.testimonial.create({\n      data: {\n        name,\n        role,\n        company,\n        content,\n        avatar,\n        rating: rating || 5,\n        featured: featured || false,\n        published: published !== undefined ? published : true,\n        order: order || 0,\n        authorId: session.user.id,\n      },\n      include: {\n        author: {\n          select: {\n            id: true,\n            name: true,\n            email: true,\n          },\n        },\n      },\n    })\n\n    return withCors(NextResponse.json(testimonial, { status: 201 }))\n  } catch (error) {\n    console.error('Error creating testimonial:', error)\n    return withCors(NextResponse.json(\n      { error: 'Failed to create testimonial' },\n      { status: 500 }\n    ))\n  }\n}\n\nexport async function OPTIONS() {\n  return handleOptions()\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;YACrD,SAAS;gBACP,QAAQ;oBACN,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,OAAO;oBACT;gBACF;YACF;YACA,SAAS;gBACP,OAAO;YACT;QACF;QAEA,OAAO,CAAA,GAAA,oHAAA,CAAA,WAAQ,AAAD,EAAE,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IACpC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO,CAAA,GAAA,oHAAA,CAAA,WAAQ,AAAD,EAAE,gIAAA,CAAA,eAAY,CAAC,IAAI,CAC/B;YAAE,OAAO;QAA+B,GACxC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,MAAM;YAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAe,GACxB;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,IAAI,EACJ,IAAI,EACJ,OAAO,EACP,OAAO,EACP,MAAM,EACN,MAAM,EACN,QAAQ,EACR,SAAS,EACT,KAAK,EACN,GAAG;QAEJ,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,MAAM,CAAC;YAClD,MAAM;gBACJ;gBACA;gBACA;gBACA;gBACA;gBACA,QAAQ,UAAU;gBAClB,UAAU,YAAY;gBACtB,WAAW,cAAc,YAAY,YAAY;gBACjD,OAAO,SAAS;gBAChB,UAAU,QAAQ,IAAI,CAAC,EAAE;YAC3B;YACA,SAAS;gBACP,QAAQ;oBACN,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,OAAO;oBACT;gBACF;YACF;QACF;QAEA,OAAO,CAAA,GAAA,oHAAA,CAAA,WAAQ,AAAD,EAAE,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,aAAa;YAAE,QAAQ;QAAI;IAC/D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO,CAAA,GAAA,oHAAA,CAAA,WAAQ,AAAD,EAAE,gIAAA,CAAA,eAAY,CAAC,IAAI,CAC/B;YAAE,OAAO;QAA+B,GACxC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe;IACpB,OAAO,CAAA,GAAA,oHAAA,CAAA,gBAAa,AAAD;AACrB", "debugId": null}}]}