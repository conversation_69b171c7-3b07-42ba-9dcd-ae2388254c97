{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/portfolio-cms/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/portfolio-cms/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport Credential<PERSON><PERSON>rovider from 'next-auth/providers/credentials'\nimport { PrismaAdapter } from '@auth/prisma-adapter'\nimport { prisma } from './prisma'\nimport bcrypt from 'bcryptjs'\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(prisma) as any,\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          }\n        })\n\n        if (!user) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          name: user.name,\n          role: user.role,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/auth/signin',\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;;;;;AAEO,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,sJAAA,CAAA,gBAAa,AAAD,EAAE,sHAAA,CAAA,SAAM;IAC7B,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;gBACjB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;YACxB;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;YAChC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;IACV;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 250, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/portfolio-cms/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\nimport slugify from \"slugify\"\nimport readingTime from \"reading-time\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n/**\n * Generate a URL-friendly slug from a title\n */\nexport function generateSlug(title: string): string {\n  return slugify(title, {\n    lower: true,\n    strict: true,\n    remove: /[*+~.()'\"!:@]/g,\n  })\n}\n\n/**\n * Calculate reading time from content (in minutes)\n */\nexport function calculateReadingTime(content: string): number {\n  const stats = readingTime(content)\n  return Math.ceil(stats.minutes)\n}\n\n/**\n * Strip HTML tags from content for reading time calculation\n */\nexport function stripHtml(html: string): string {\n  return html.replace(/<[^>]*>/g, ' ').replace(/\\s+/g, ' ').trim()\n}\n\n/**\n * Extract plain text from rich text content for reading time calculation\n */\nexport function extractTextFromRichContent(content: string): string {\n  // If content is JSON (TipTap format), extract text\n  try {\n    const parsed = JSON.parse(content)\n    if (parsed.type === 'doc' && parsed.content) {\n      return extractTextFromNodes(parsed.content)\n    }\n  } catch {\n    // If not JSON, treat as HTML/markdown and strip tags\n    return stripHtml(content)\n  }\n\n  return stripHtml(content)\n}\n\n/**\n * Recursively extract text from TipTap nodes\n */\nfunction extractTextFromNodes(nodes: any[]): string {\n  let text = ''\n\n  for (const node of nodes) {\n    if (node.type === 'text') {\n      text += node.text || ''\n    } else if (node.content) {\n      text += extractTextFromNodes(node.content)\n    }\n\n    // Add space after block elements\n    if (['paragraph', 'heading', 'listItem'].includes(node.type)) {\n      text += ' '\n    }\n  }\n\n  return text.trim()\n}\n\n/**\n * Validate and sanitize slug\n */\nexport function validateSlug(slug: string): string {\n  if (!slug || slug.trim() === '') {\n    throw new Error('Slug cannot be empty')\n  }\n\n  const sanitized = slugify(slug, {\n    lower: true,\n    strict: true,\n    remove: /[*+~.()'\"!:@]/g,\n  })\n\n  if (sanitized !== slug) {\n    return sanitized\n  }\n\n  return slug\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;;;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,uIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAKO,SAAS,aAAa,KAAa;IACxC,OAAO,CAAA,GAAA,oIAAA,CAAA,UAAO,AAAD,EAAE,OAAO;QACpB,OAAO;QACP,QAAQ;QACR,QAAQ;IACV;AACF;AAKO,SAAS,qBAAqB,OAAe;IAClD,MAAM,QAAQ,CAAA,GAAA,0IAAA,CAAA,UAAW,AAAD,EAAE;IAC1B,OAAO,KAAK,IAAI,CAAC,MAAM,OAAO;AAChC;AAKO,SAAS,UAAU,IAAY;IACpC,OAAO,KAAK,OAAO,CAAC,YAAY,KAAK,OAAO,CAAC,QAAQ,KAAK,IAAI;AAChE;AAKO,SAAS,2BAA2B,OAAe;IACxD,mDAAmD;IACnD,IAAI;QACF,MAAM,SAAS,KAAK,KAAK,CAAC;QAC1B,IAAI,OAAO,IAAI,KAAK,SAAS,OAAO,OAAO,EAAE;YAC3C,OAAO,qBAAqB,OAAO,OAAO;QAC5C;IACF,EAAE,OAAM;QACN,qDAAqD;QACrD,OAAO,UAAU;IACnB;IAEA,OAAO,UAAU;AACnB;AAEA;;CAEC,GACD,SAAS,qBAAqB,KAAY;IACxC,IAAI,OAAO;IAEX,KAAK,MAAM,QAAQ,MAAO;QACxB,IAAI,KAAK,IAAI,KAAK,QAAQ;YACxB,QAAQ,KAAK,IAAI,IAAI;QACvB,OAAO,IAAI,KAAK,OAAO,EAAE;YACvB,QAAQ,qBAAqB,KAAK,OAAO;QAC3C;QAEA,iCAAiC;QACjC,IAAI;YAAC;YAAa;YAAW;SAAW,CAAC,QAAQ,CAAC,KAAK,IAAI,GAAG;YAC5D,QAAQ;QACV;IACF;IAEA,OAAO,KAAK,IAAI;AAClB;AAKO,SAAS,aAAa,IAAY;IACvC,IAAI,CAAC,QAAQ,KAAK,IAAI,OAAO,IAAI;QAC/B,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,YAAY,CAAA,GAAA,oIAAA,CAAA,UAAO,AAAD,EAAE,MAAM;QAC9B,OAAO;QACP,QAAQ;QACR,QAAQ;IACV;IAEA,IAAI,cAAc,MAAM;QACtB,OAAO;IACT;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 337, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/portfolio-cms/src/lib/cors.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\n\nexport function corsHeaders() {\n  return {\n    'Access-Control-Allow-Origin': '*',\n    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n    'Access-Control-Allow-Headers': 'Content-Type, Authorization',\n  }\n}\n\nexport function withCors(response: NextResponse) {\n  const headers = corsHeaders()\n  Object.entries(headers).forEach(([key, value]) => {\n    response.headers.set(key, value)\n  })\n  return response\n}\n\nexport function handleOptions() {\n  return new NextResponse(null, {\n    status: 200,\n    headers: corsHeaders(),\n  })\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEO,SAAS;IACd,OAAO;QACL,+BAA+B;QAC/B,gCAAgC;QAChC,gCAAgC;IAClC;AACF;AAEO,SAAS,SAAS,QAAsB;IAC7C,MAAM,UAAU;IAChB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;QAC3C,SAAS,OAAO,CAAC,GAAG,CAAC,KAAK;IAC5B;IACA,OAAO;AACT;AAEO,SAAS;IACd,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;QAC5B,QAAQ;QACR,SAAS;IACX;AACF", "debugId": null}}, {"offset": {"line": 370, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/portfolio-cms/src/app/api/blog/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getServerSession } from 'next-auth'\nimport { authOptions } from '@/lib/auth'\nimport { prisma } from '@/lib/prisma'\nimport { generateSlug, calculateReadingTime, extractTextFromRichContent, validateSlug } from '@/lib/utils'\nimport { withCors, handleOptions } from '@/lib/cors'\n\nexport async function GET() {\n  try {\n    const blogPosts = await prisma.blogPost.findMany({\n      include: {\n        author: {\n          select: {\n            id: true,\n            name: true,\n            email: true,\n          },\n        },\n      },\n      orderBy: {\n        createdAt: 'desc',\n      },\n    })\n\n    return withCors(NextResponse.json(blogPosts))\n  } catch (error) {\n    console.error('Error fetching blog posts:', error)\n    return withCors(NextResponse.json(\n      { error: 'Failed to fetch blog posts' },\n      { status: 500 }\n    ))\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session?.user) {\n      return NextResponse.json(\n        { error: 'Unauthorized' },\n        { status: 401 }\n      )\n    }\n\n    const body = await request.json()\n    const {\n      title,\n      slug: providedSlug,\n      excerpt,\n      content,\n      image,\n      category,\n      tags,\n      published,\n      featured,\n      readTime: providedReadTime,\n    } = body\n\n    // Auto-generate slug if not provided or empty\n    let finalSlug = providedSlug\n    if (!finalSlug || finalSlug.trim() === '') {\n      finalSlug = generateSlug(title)\n    } else {\n      finalSlug = validateSlug(finalSlug)\n    }\n\n    // Check for slug uniqueness\n    const existingPost = await prisma.blogPost.findUnique({\n      where: { slug: finalSlug }\n    })\n\n    if (existingPost) {\n      // Append timestamp to make it unique\n      finalSlug = `${finalSlug}-${Date.now()}`\n    }\n\n    // Auto-calculate reading time if not provided\n    let finalReadTime = providedReadTime\n    if (!finalReadTime || finalReadTime <= 0) {\n      const textContent = extractTextFromRichContent(content)\n      finalReadTime = calculateReadingTime(textContent)\n    }\n\n    const blogPost = await prisma.blogPost.create({\n      data: {\n        title,\n        slug: finalSlug,\n        excerpt,\n        content,\n        image,\n        category,\n        tags,\n        published: published || false,\n        featured: featured || false,\n        readTime: finalReadTime,\n        publishedAt: published ? new Date() : null,\n        authorId: session.user.id,\n      },\n      include: {\n        author: {\n          select: {\n            id: true,\n            name: true,\n            email: true,\n          },\n        },\n      },\n    })\n\n    return withCors(NextResponse.json(blogPost, { status: 201 }))\n  } catch (error) {\n    console.error('Error creating blog post:', error)\n    return withCors(NextResponse.json(\n      { error: 'Failed to create blog post' },\n      { status: 500 }\n    ))\n  }\n}\n\nexport async function OPTIONS() {\n  return handleOptions()\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,YAAY,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAC/C,SAAS;gBACP,QAAQ;oBACN,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,OAAO;oBACT;gBACF;YACF;YACA,SAAS;gBACP,WAAW;YACb;QACF;QAEA,OAAO,CAAA,GAAA,oHAAA,CAAA,WAAQ,AAAD,EAAE,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IACpC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,CAAA,GAAA,oHAAA,CAAA,WAAQ,AAAD,EAAE,gIAAA,CAAA,eAAY,CAAC,IAAI,CAC/B;YAAE,OAAO;QAA6B,GACtC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,MAAM;YAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAe,GACxB;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,KAAK,EACL,MAAM,YAAY,EAClB,OAAO,EACP,OAAO,EACP,KAAK,EACL,QAAQ,EACR,IAAI,EACJ,SAAS,EACT,QAAQ,EACR,UAAU,gBAAgB,EAC3B,GAAG;QAEJ,8CAA8C;QAC9C,IAAI,YAAY;QAChB,IAAI,CAAC,aAAa,UAAU,IAAI,OAAO,IAAI;YACzC,YAAY,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD,EAAE;QAC3B,OAAO;YACL,YAAY,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD,EAAE;QAC3B;QAEA,4BAA4B;QAC5B,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;YACpD,OAAO;gBAAE,MAAM;YAAU;QAC3B;QAEA,IAAI,cAAc;YAChB,qCAAqC;YACrC,YAAY,GAAG,UAAU,CAAC,EAAE,KAAK,GAAG,IAAI;QAC1C;QAEA,8CAA8C;QAC9C,IAAI,gBAAgB;QACpB,IAAI,CAAC,iBAAiB,iBAAiB,GAAG;YACxC,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,6BAA0B,AAAD,EAAE;YAC/C,gBAAgB,CAAA,GAAA,qHAAA,CAAA,uBAAoB,AAAD,EAAE;QACvC;QAEA,MAAM,WAAW,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC5C,MAAM;gBACJ;gBACA,MAAM;gBACN;gBACA;gBACA;gBACA;gBACA;gBACA,WAAW,aAAa;gBACxB,UAAU,YAAY;gBACtB,UAAU;gBACV,aAAa,YAAY,IAAI,SAAS;gBACtC,UAAU,QAAQ,IAAI,CAAC,EAAE;YAC3B;YACA,SAAS;gBACP,QAAQ;oBACN,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,OAAO;oBACT;gBACF;YACF;QACF;QAEA,OAAO,CAAA,GAAA,oHAAA,CAAA,WAAQ,AAAD,EAAE,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,UAAU;YAAE,QAAQ;QAAI;IAC5D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,CAAA,GAAA,oHAAA,CAAA,WAAQ,AAAD,EAAE,gIAAA,CAAA,eAAY,CAAC,IAAI,CAC/B;YAAE,OAAO;QAA6B,GACtC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe;IACpB,OAAO,CAAA,GAAA,oHAAA,CAAA,gBAAa,AAAD;AACrB", "debugId": null}}]}