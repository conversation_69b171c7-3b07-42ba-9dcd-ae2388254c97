{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/dashboard/:path*{(\\\\.json)}?", "originalSource": "/dashboard/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/api/:path*{(\\\\.json)}?", "originalSource": "/api/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "GmR490q78K+bBIm3nmrLlRQO8rHHbnjcdhaBgWmTT64=", "__NEXT_PREVIEW_MODE_ID": "5ae3d93e61f7fbb83d368f24cafe879a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "3f209341ddf2babe9f021817573176ab0b2d5d82fd85a7db2af1695fbb46e756", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c465b58ee4e7c1034abedcaf3f035564391e2bd86e43b616fbe43911bb96f615"}}}, "instrumentation": null, "functions": {}}