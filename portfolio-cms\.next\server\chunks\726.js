exports.id=726,exports.ids=[726],exports.modules={4780:(e,t,r)=>{"use strict";r.d(t,{KE:()=>m,_C:()=>u,cn:()=>c,z9:()=>l});var s=r(49384),n=r(82348),a=r(85126),i=r.n(a),o=r(26728),d=r.n(o);function c(...e){return(0,n.QP)((0,s.$)(e))}function l(e){return i()(e,{lower:!0,strict:!0,remove:/[*+~.()'"!:@]/g})}function u(e){return Math.ceil(d()(e).minutes)}function m(e){try{let t=JSON.parse(e);if("doc"===t.type&&t.content)return function e(t){let r="";for(let s of t)"text"===s.type?r+=s.text||"":s.content&&(r+=e(s.content)),["paragraph","heading","listItem"].includes(s.type)&&(r+=" ");return r.trim()}(t.content)}catch{}return e.replace(/<[^>]*>/g," ").replace(/\s+/g," ").trim()}},29523:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var s=r(60687);r(43210);var n=r(8730),a=r(24224),i=r(4780);let o=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:t,size:r,asChild:a=!1,...d}){let c=a?n.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,i.cn)(o({variant:t,size:r,className:e})),...d})}},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>a,aR:()=>i});var s=r(60687);r(43210);var n=r(4780);function a({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...t})}function c({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",e),...t})}},46627:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},49633:(e,t,r)=>{Promise.resolve().then(r.bind(r,83305))},60699:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},61135:()=>{},62280:(e,t,r)=>{"use strict";r.d(t,{N:()=>L});var s=r(60687),n=r(82136),a=r(16189);r(43210);var i=r(85814),o=r.n(i),d=r(4780),c=r(49625),l=r(18179),u=r(10022),m=r(57800),v=r(80375),h=r(58887),f=r(53411),x=r(84027);let g=[{name:"Dashboard",href:"/dashboard",icon:c.A},{name:"Projects",href:"/dashboard/projects",icon:l.A},{name:"Blog Posts",href:"/dashboard/blog",icon:u.A},{name:"Services",href:"/dashboard/services",icon:m.A},{name:"Tech Stack",href:"/dashboard/tech-stack",icon:v.A},{name:"Testimonials",href:"/dashboard/testimonials",icon:h.A},{name:"Analytics",href:"/dashboard/analytics",icon:f.A},{name:"Settings",href:"/dashboard/settings",icon:x.A}];function b(){let e=(0,a.usePathname)();return(0,s.jsxs)("div",{className:"flex h-full w-64 flex-col bg-gray-50 dark:bg-gray-900",children:[(0,s.jsx)("div",{className:"flex h-16 items-center px-6",children:(0,s.jsx)("h1",{className:"text-xl font-bold",children:"Portfolio CMS"})}),(0,s.jsx)("nav",{className:"flex-1 space-y-1 px-3 py-4",children:g.map(t=>{let r=e===t.href;return(0,s.jsxs)(o(),{href:t.href,className:(0,d.cn)("flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors",r?"bg-primary text-primary-foreground":"text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800"),children:[(0,s.jsx)(t.icon,{className:"mr-3 h-5 w-5"}),t.name]},t.name)})})]})}var p=r(29523),j=r(29398);function y({...e}){return(0,s.jsx)(j.bL,{"data-slot":"dropdown-menu",...e})}function w({...e}){return(0,s.jsx)(j.l9,{"data-slot":"dropdown-menu-trigger",...e})}function N({className:e,sideOffset:t=4,...r}){return(0,s.jsx)(j.ZL,{children:(0,s.jsx)(j.UC,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...r})})}function P({className:e,inset:t,variant:r="default",...n}){return(0,s.jsx)(j.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":r,className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n})}function k({className:e,inset:t,...r}){return(0,s.jsx)(j.JU,{"data-slot":"dropdown-menu-label","data-inset":t,className:(0,d.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",e),...r})}function A({className:e,...t}){return(0,s.jsx)(j.wv,{"data-slot":"dropdown-menu-separator",className:(0,d.cn)("bg-border -mx-1 my-1 h-px",e),...t})}var z=r(11096);function C({className:e,...t}){return(0,s.jsx)(z.bL,{"data-slot":"avatar",className:(0,d.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function S({className:e,...t}){return(0,s.jsx)(z.H4,{"data-slot":"avatar-fallback",className:(0,d.cn)("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}var _=r(58869),M=r(40083);function U(){let{data:e}=(0,n.useSession)();return(0,s.jsxs)("header",{className:"flex h-16 items-center justify-between border-b bg-white px-6 dark:bg-gray-950",children:[(0,s.jsx)("div",{className:"flex items-center space-x-4",children:(0,s.jsx)("h2",{className:"text-lg font-semibold",children:"Content Management"})}),(0,s.jsx)("div",{className:"flex items-center space-x-4",children:(0,s.jsxs)(y,{children:[(0,s.jsx)(w,{asChild:!0,children:(0,s.jsx)(p.$,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,s.jsx)(C,{className:"h-8 w-8",children:(0,s.jsx)(S,{children:e?.user?.name?.charAt(0)||e?.user?.email?.charAt(0)||"U"})})})}),(0,s.jsxs)(N,{className:"w-56",align:"end",forceMount:!0,children:[(0,s.jsx)(k,{className:"font-normal",children:(0,s.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,s.jsx)("p",{className:"text-sm font-medium leading-none",children:e?.user?.name||"User"}),(0,s.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:e?.user?.email})]})}),(0,s.jsx)(A,{}),(0,s.jsxs)(P,{children:[(0,s.jsx)(_.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Profile"})]}),(0,s.jsx)(A,{}),(0,s.jsxs)(P,{onClick:()=>{(0,n.signOut)({callbackUrl:"/auth/signin"})},children:[(0,s.jsx)(M.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Log out"})]})]})]})})]})}function L({children:e}){let{data:t,status:r}=(0,n.useSession)();return((0,a.useRouter)(),"loading"===r)?(0,s.jsx)("div",{className:"flex h-screen items-center justify-center",children:(0,s.jsx)("div",{className:"text-lg",children:"Loading..."})}):t?(0,s.jsxs)("div",{className:"flex h-screen bg-gray-100 dark:bg-gray-900",children:[(0,s.jsx)(b,{}),(0,s.jsxs)("div",{className:"flex flex-1 flex-col overflow-hidden",children:[(0,s.jsx)(U,{}),(0,s.jsx)("main",{className:"flex-1 overflow-auto p-6",children:e})]})]}):null}},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},80415:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>a});var s=r(60687),n=r(82136);function a({children:e}){return(0,s.jsx)(n.SessionProvider,{children:e})}},83305:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\providers\\session-provider.tsx","AuthProvider")},86081:(e,t,r)=>{Promise.resolve().then(r.bind(r,80415))},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,metadata:()=>c});var s=r(37413),n=r(22376),a=r.n(n),i=r(68726),o=r.n(i);r(61135);var d=r(83305);let c={title:"Portfolio CMS",description:"Content Management System for Portfolio Website"};function l({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${a().variable} ${o().variable} antialiased`,children:(0,s.jsx)(d.AuthProvider,{children:e})})})}},96834:(e,t,r)=>{"use strict";r.d(t,{E:()=>d});var s=r(60687);r(43210);var n=r(8730),a=r(24224),i=r(4780);let o=(0,a.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,asChild:r=!1,...a}){let d=r?n.DX:"span";return(0,s.jsx)(d,{"data-slot":"badge",className:(0,i.cn)(o({variant:t}),e),...a})}}};