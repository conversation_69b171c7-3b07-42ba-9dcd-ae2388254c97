(()=>{var e={};e.id=438,e.ids=[438],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,s)=>{"use strict";s.d(r,{N:()=>n});var t=s(13581),i=s(16467),o=s(94747),a=s(85663);let n={adapter:(0,i.y)(o.z),providers:[(0,t.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let r=await o.z.user.findUnique({where:{email:e.email}});return r&&await a.Ay.compare(e.password,r.password)?{id:r.id,email:r.email,name:r.name,role:r.role}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(e.role=r.role),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role),e)},pages:{signIn:"/auth/signin"},secret:process.env.NEXTAUTH_SECRET}},19689:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>y,routeModule:()=>h,serverHooks:()=>g,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>w});var t={};s.r(t),s.d(t,{GET:()=>d,OPTIONS:()=>v,POST:()=>x});var i=s(96559),o=s(48088),a=s(37719),n=s(32190),u=s(19854),c=s(12909),p=s(94747),l=s(27746);async function d(){try{let e=await p.z.service.findMany({orderBy:{order:"asc"}});return(0,l.gx)(n.NextResponse.json(e))}catch(e){return console.error("Error fetching services:",e),(0,l.gx)(n.NextResponse.json({error:"Failed to fetch services"},{status:500}))}}async function x(e){try{let r=await (0,u.getServerSession)(c.N);if(!r?.user)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{title:s,description:t,features:i,icon:o,color:a,bgColor:d,order:x,published:v}=await e.json(),h=await p.z.service.create({data:{title:s,description:t,features:i,icon:o,color:a,bgColor:d,order:x||0,published:void 0===v||v}});return(0,l.gx)(n.NextResponse.json(h,{status:201}))}catch(e){return console.error("Error creating service:",e),(0,l.gx)(n.NextResponse.json({error:"Failed to create service"},{status:500}))}}async function v(){return(0,l.JB)()}let h=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/services/route",pathname:"/api/services",filename:"route",bundlePath:"app/api/services/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\api\\services\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:m,workUnitAsyncStorage:w,serverHooks:g}=h;function y(){return(0,a.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:w})}},27746:(e,r,s)=>{"use strict";s.d(r,{JB:()=>a,gx:()=>o});var t=s(32190);function i(){return{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, POST, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}}function o(e){return Object.entries(i()).forEach(([r,s])=>{e.headers.set(r,s)}),e}function a(){return new t.NextResponse(null,{status:200,headers:i()})}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},94747:(e,r,s)=>{"use strict";s.d(r,{z:()=>i});let t=require("@prisma/client"),i=globalThis.prisma??new t.PrismaClient},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[243,542,190],()=>s(19689));module.exports=t})();