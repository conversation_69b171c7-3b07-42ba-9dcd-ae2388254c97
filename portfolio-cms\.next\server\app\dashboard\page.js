(()=>{var e={};e.id=105,e.ids=[105],e.modules={16:(e,s,t)=>{Promise.resolve().then(t.bind(t,58061))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14278:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>a.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=t(65239),i=t(48088),o=t(88170),a=t.n(o),n=t(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);t.d(s,l);let d={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,80559)),"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},41872:(e,s,t)=>{Promise.resolve().then(t.bind(t,80559))},58061:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var r=t(60687),i=t(43210),o=t(44493),a=t(96834),n=t(62280),l=t(18179),d=t(10022),c=t(57800),p=t(58887);function x(){let[e,s]=(0,i.useState)({projects:0,blogPosts:0,services:0,testimonials:0}),[t,x]=(0,i.useState)(!0),u=[{title:"Projects",value:e.projects,description:"Total portfolio projects",icon:l.A,color:"text-blue-600"},{title:"Blog Posts",value:e.blogPosts,description:"Published articles",icon:d.A,color:"text-green-600"},{title:"Services",value:e.services,description:"Available services",icon:c.A,color:"text-purple-600"},{title:"Testimonials",value:e.testimonials,description:"Client reviews",icon:p.A,color:"text-orange-600"}];return(0,r.jsx)(n.N,{children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Dashboard"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Welcome to your portfolio content management system"})]}),(0,r.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:u.map(e=>(0,r.jsxs)(o.Zp,{children:[(0,r.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(o.ZB,{className:"text-sm font-medium",children:e.title}),(0,r.jsx)(e.icon,{className:`h-4 w-4 ${e.color}`})]}),(0,r.jsxs)(o.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:t?"...":e.value}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:e.description})]})]},e.title))}),(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,r.jsxs)(o.Zp,{children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsx)(o.ZB,{children:"Quick Actions"}),(0,r.jsx)(o.BT,{children:"Common tasks to manage your portfolio content"})]}),(0,r.jsxs)(o.Wu,{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{children:"Add New Project"}),(0,r.jsx)(a.E,{variant:"secondary",children:"Create"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{children:"Write Blog Post"}),(0,r.jsx)(a.E,{variant:"secondary",children:"Write"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{children:"Update Services"}),(0,r.jsx)(a.E,{variant:"secondary",children:"Edit"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{children:"Manage Testimonials"}),(0,r.jsx)(a.E,{variant:"secondary",children:"Review"})]})]})]}),(0,r.jsxs)(o.Zp,{children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsx)(o.ZB,{children:"Recent Activity"}),(0,r.jsx)(o.BT,{children:"Latest changes to your portfolio content"})]}),(0,r.jsx)(o.Wu,{children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center text-sm",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full mr-2"}),"Database initialized successfully"]}),(0,r.jsxs)("div",{className:"flex items-center text-sm",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full mr-2"}),"Sample data seeded"]}),(0,r.jsxs)("div",{className:"flex items-center text-sm",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-purple-500 rounded-full mr-2"}),"CMS dashboard ready"]})]})})]})]})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},80559:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\portfolio\\\\portfolio-cms\\\\src\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\page.tsx","default")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[243,310,934,671,360,726],()=>t(14278));module.exports=r})();