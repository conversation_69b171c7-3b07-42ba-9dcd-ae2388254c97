(()=>{var e={};e.id=210,e.ids=[210],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{N:()=>n});var s=t(13581),i=t(16467),o=t(94747),a=t(85663);let n={adapter:(0,i.y)(o.z),providers:[(0,s.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let r=await o.z.user.findUnique({where:{email:e.email}});return r&&await a.Ay.compare(e.password,r.password)?{id:r.id,email:r.email,name:r.name,role:r.role}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(e.role=r.role),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role),e)},pages:{signIn:"/auth/signin"},secret:process.env.NEXTAUTH_SECRET}},27746:(e,r,t)=>{"use strict";t.d(r,{JB:()=>a,gx:()=>o});var s=t(32190);function i(){return{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, POST, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}}function o(e){return Object.entries(i()).forEach(([r,t])=>{e.headers.set(r,t)}),e}function a(){return new s.NextResponse(null,{status:200,headers:i()})}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},94747:(e,r,t)=>{"use strict";t.d(r,{z:()=>i});let s=require("@prisma/client"),i=globalThis.prisma??new s.PrismaClient},96465:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>y,routeModule:()=>h,serverHooks:()=>f,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{GET:()=>d,OPTIONS:()=>x,POST:()=>m});var i=t(96559),o=t(48088),a=t(37719),n=t(32190),u=t(19854),l=t(12909),c=t(94747),p=t(27746);async function d(){try{let e=await c.z.testimonial.findMany({include:{author:{select:{id:!0,name:!0,email:!0}}},orderBy:{order:"asc"}});return(0,p.gx)(n.NextResponse.json(e))}catch(e){return console.error("Error fetching testimonials:",e),(0,p.gx)(n.NextResponse.json({error:"Failed to fetch testimonials"},{status:500}))}}async function m(e){try{let r=await (0,u.getServerSession)(l.N);if(!r?.user)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{name:t,role:s,company:i,content:o,avatar:a,rating:d,featured:m,published:x,order:h}=await e.json(),w=await c.z.testimonial.create({data:{name:t,role:s,company:i,content:o,avatar:a,rating:d||5,featured:m||!1,published:void 0===x||x,order:h||0,authorId:r.user.id},include:{author:{select:{id:!0,name:!0,email:!0}}}});return(0,p.gx)(n.NextResponse.json(w,{status:201}))}catch(e){return console.error("Error creating testimonial:",e),(0,p.gx)(n.NextResponse.json({error:"Failed to create testimonial"},{status:500}))}}async function x(){return(0,p.JB)()}let h=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/testimonials/route",pathname:"/api/testimonials",filename:"route",bundlePath:"app/api/testimonials/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\api\\testimonials\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:w,workUnitAsyncStorage:g,serverHooks:f}=h;function y(){return(0,a.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:g})}},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,542,190],()=>t(96465));module.exports=s})();