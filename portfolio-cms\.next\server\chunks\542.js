exports.id=542,exports.ids=[542],exports.modules={767:(e,t,r)=>{var n=r(57502),o=r(30994),i=r(44726),a=r(9684);function s(t){var r="function"==typeof Map?new Map:void 0;return e.exports=s=function(e){if(null===e||!i(e))return e;if("function"!=typeof e)throw TypeError("Super expression must either be null or a function");if(void 0!==r){if(r.has(e))return r.get(e);r.set(e,t)}function t(){return a(e,arguments,n(this).constructor)}return t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),o(t,e)},e.exports.__esModule=!0,e.exports.default=e.exports,s(t)}e.exports=s,e.exports.__esModule=!0,e.exports.default=e.exports},913:(e,t,r)=>{let n=r(70885);e.exports=e=>{if("string"!=typeof e||!e)throw TypeError("JWT must be a string");let{0:t,1:r,2:o,length:i}=e.split(".");if(5===i)throw TypeError("encrypted JWTs cannot be decoded");if(3!==i)throw Error("JWTs must have three components");try{return{header:JSON.parse(n.decode(t)),payload:JSON.parse(n.decode(r)),signature:o}}catch(e){throw Error("JWT is malformed")}}},1149:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.EmbeddedJWK=void 0;let n=r(97920),o=r(25110),i=r(99938);t.EmbeddedJWK=async function(e,t){let r={...e,...null==t?void 0:t.header};if(!(0,o.default)(r.jwk))throw new i.JWSInvalid('"jwk" (JSON Web Key) Header Parameter must be a JSON object');let a=await (0,n.importJWK)({...r.jwk,ext:!0},r.alg,!0);if(a instanceof Uint8Array||"public"!==a.type)throw new i.JWSInvalid('"jwk" (JSON Web Key) Header Parameter must be a public key');return a}},1441:(e,t)=>{var r,n,o,i,a,s,c,l,u,d,f,p={},h=[],y=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,_=Array.isArray;function g(e,t){for(var r in t)e[r]=t[r];return e}function b(e){e&&e.parentNode&&e.parentNode.removeChild(e)}function m(e,t,n){var o,i,a,s={};for(a in t)"key"==a?o=t[a]:"ref"==a?i=t[a]:s[a]=t[a];if(arguments.length>2&&(s.children=arguments.length>3?r.call(arguments,2):n),"function"==typeof e&&null!=e.defaultProps)for(a in e.defaultProps)void 0===s[a]&&(s[a]=e.defaultProps[a]);return v(e,s,o,i,null)}function v(e,t,r,i,a){var s={type:e,props:t,key:r,ref:i,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:null==a?++o:a,__i:-1,__u:0};return null==a&&null!=n.vnode&&n.vnode(s),s}function w(e){return e.children}function x(e,t){this.props=e,this.context=t}function k(e,t){if(null==t)return e.__?k(e.__,e.__i+1):null;for(var r;t<e.__k.length;t++)if(null!=(r=e.__k[t])&&null!=r.__e)return r.__e;return"function"==typeof e.type?k(e):null}function E(e){(!e.__d&&(e.__d=!0)&&i.push(e)&&!S.__r++||a!==n.debounceRendering)&&((a=n.debounceRendering)||s)(S)}function S(){var e,t,r,o,a,s,l,u;for(i.sort(c);e=i.shift();)e.__d&&(t=i.length,o=void 0,s=(a=(r=e).__v).__e,l=[],u=[],r.__P&&((o=g({},a)).__v=a.__v+1,n.vnode&&n.vnode(o),C(r.__P,o,a,r.__n,r.__P.namespaceURI,32&a.__u?[s]:null,l,null==s?k(a):s,!!(32&a.__u),u),o.__v=a.__v,o.__.__k[o.__i]=o,j(l,o,u),o.__e!=s&&function e(t){var r,n;if(null!=(t=t.__)&&null!=t.__c){for(t.__e=t.__c.base=null,r=0;r<t.__k.length;r++)if(null!=(n=t.__k[r])&&null!=n.__e){t.__e=t.__c.base=n.__e;break}return e(t)}}(o)),i.length>t&&i.sort(c));S.__r=0}function A(e,t,r,o,i,a,s,c,l,u,d){var f,y,g,m,x,E=o&&o.__k||h,S=t.length;for(r.__d=l,function(e,t,r){var o,i,a,s,c,l=t.length,u=r.length,d=u,f=0;for(e.__k=[],o=0;o<l;o++)null!=(i=t[o])&&"boolean"!=typeof i&&"function"!=typeof i?(s=o+f,(i=e.__k[o]="string"==typeof i||"number"==typeof i||"bigint"==typeof i||i.constructor==String?v(null,i,null,null,null):_(i)?v(w,{children:i},null,null,null):void 0===i.constructor&&i.__b>0?v(i.type,i.props,i.key,i.ref?i.ref:null,i.__v):i).__=e,i.__b=e.__b+1,a=null,-1!==(c=i.__i=function(e,t,r,n){var o=e.key,i=e.type,a=r-1,s=r+1,c=t[r];if(null===c||c&&o==c.key&&i===c.type&&0==(131072&c.__u))return r;if(n>+(null!=c&&0==(131072&c.__u)))for(;a>=0||s<t.length;){if(a>=0){if((c=t[a])&&0==(131072&c.__u)&&o==c.key&&i===c.type)return a;a--}if(s<t.length){if((c=t[s])&&0==(131072&c.__u)&&o==c.key&&i===c.type)return s;s++}}return -1}(i,r,s,d))&&(d--,(a=r[c])&&(a.__u|=131072)),null==a||null===a.__v?(-1==c&&f--,"function"!=typeof i.type&&(i.__u|=65536)):c!==s&&(c==s-1?f--:c==s+1?f++:(c>s?f--:f++,i.__u|=65536))):i=e.__k[o]=null;if(d)for(o=0;o<u;o++)null!=(a=r[o])&&0==(131072&a.__u)&&(a.__e==e.__d&&(e.__d=k(a)),function e(t,r,o){var i,a;if(n.unmount&&n.unmount(t),(i=t.ref)&&(i.current&&i.current!==t.__e||R(i,null,r)),null!=(i=t.__c)){if(i.componentWillUnmount)try{i.componentWillUnmount()}catch(e){n.__e(e,r)}i.base=i.__P=null}if(i=t.__k)for(a=0;a<i.length;a++)i[a]&&e(i[a],r,o||"function"!=typeof t.type);o||b(t.__e),t.__c=t.__=t.__e=t.__d=void 0}(a,a))}(r,t,E),l=r.__d,f=0;f<S;f++)null!=(g=r.__k[f])&&(y=-1===g.__i?p:E[g.__i]||p,g.__i=f,C(e,g,y,i,a,s,c,l,u,d),m=g.__e,g.ref&&y.ref!=g.ref&&(y.ref&&R(y.ref,null,g),d.push(g.ref,g.__c||m,g)),null==x&&null!=m&&(x=m),65536&g.__u||y.__k===g.__k?l=function e(t,r,n){var o,i;if("function"==typeof t.type){for(o=t.__k,i=0;o&&i<o.length;i++)o[i]&&(o[i].__=t,r=e(o[i],r,n));return r}t.__e!=r&&(r&&t.type&&!n.contains(r)&&(r=k(t)),n.insertBefore(t.__e,r||null),r=t.__e);do r=r&&r.nextSibling;while(null!=r&&8===r.nodeType);return r}(g,l,e):"function"==typeof g.type&&void 0!==g.__d?l=g.__d:m&&(l=m.nextSibling),g.__d=void 0,g.__u&=-196609);r.__d=l,r.__e=x}function O(e,t,r){"-"===t[0]?e.setProperty(t,null==r?"":r):e[t]=null==r?"":"number"!=typeof r||y.test(t)?r:r+"px"}function P(e,t,r,n,o){var i;e:if("style"===t)if("string"==typeof r)e.style.cssText=r;else{if("string"==typeof n&&(e.style.cssText=n=""),n)for(t in n)r&&t in r||O(e.style,t,"");if(r)for(t in r)n&&r[t]===n[t]||O(e.style,t,r[t])}else if("o"===t[0]&&"n"===t[1])i=t!==(t=t.replace(/(PointerCapture)$|Capture$/i,"$1")),t=t.toLowerCase()in e||"onFocusOut"===t||"onFocusIn"===t?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+i]=r,r?n?r.t=n.t:(r.t=l,e.addEventListener(t,i?d:u,i)):e.removeEventListener(t,i?d:u,i);else{if("http://www.w3.org/2000/svg"==o)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=t&&"height"!=t&&"href"!=t&&"list"!=t&&"form"!=t&&"tabIndex"!=t&&"download"!=t&&"rowSpan"!=t&&"colSpan"!=t&&"role"!=t&&"popover"!=t&&t in e)try{e[t]=null==r?"":r;break e}catch(e){}"function"==typeof r||(null==r||!1===r&&"-"!==t[4]?e.removeAttribute(t):e.setAttribute(t,"popover"==t&&1==r?"":r))}}function T(e){return function(t){if(this.l){var r=this.l[t.type+e];if(null==t.u)t.u=l++;else if(t.u<r.t)return;return r(n.event?n.event(t):t)}}}function C(e,t,o,i,a,s,c,l,u,d){var f,h,y,m,v,E,S,O,T,C,j,R,J,W,H,M,K=t.type;if(void 0!==t.constructor)return null;128&o.__u&&(u=!!(32&o.__u),s=[l=t.__e=o.__e]),(f=n.__b)&&f(t);e:if("function"==typeof K)try{if(O=t.props,T="prototype"in K&&K.prototype.render,C=(f=K.contextType)&&i[f.__c],j=f?C?C.props.value:f.__:i,o.__c?S=(h=t.__c=o.__c).__=h.__E:(T?t.__c=h=new K(O,j):(t.__c=h=new x(O,j),h.constructor=K,h.render=I),C&&C.sub(h),h.props=O,h.state||(h.state={}),h.context=j,h.__n=i,y=h.__d=!0,h.__h=[],h._sb=[]),T&&null==h.__s&&(h.__s=h.state),T&&null!=K.getDerivedStateFromProps&&(h.__s==h.state&&(h.__s=g({},h.__s)),g(h.__s,K.getDerivedStateFromProps(O,h.__s))),m=h.props,v=h.state,h.__v=t,y)T&&null==K.getDerivedStateFromProps&&null!=h.componentWillMount&&h.componentWillMount(),T&&null!=h.componentDidMount&&h.__h.push(h.componentDidMount);else{if(T&&null==K.getDerivedStateFromProps&&O!==m&&null!=h.componentWillReceiveProps&&h.componentWillReceiveProps(O,j),!h.__e&&(null!=h.shouldComponentUpdate&&!1===h.shouldComponentUpdate(O,h.__s,j)||t.__v===o.__v)){for(t.__v!==o.__v&&(h.props=O,h.state=h.__s,h.__d=!1),t.__e=o.__e,t.__k=o.__k,t.__k.some(function(e){e&&(e.__=t)}),R=0;R<h._sb.length;R++)h.__h.push(h._sb[R]);h._sb=[],h.__h.length&&c.push(h);break e}null!=h.componentWillUpdate&&h.componentWillUpdate(O,h.__s,j),T&&null!=h.componentDidUpdate&&h.__h.push(function(){h.componentDidUpdate(m,v,E)})}if(h.context=j,h.props=O,h.__P=e,h.__e=!1,J=n.__r,W=0,T){for(h.state=h.__s,h.__d=!1,J&&J(t),f=h.render(h.props,h.state,h.context),H=0;H<h._sb.length;H++)h.__h.push(h._sb[H]);h._sb=[]}else do h.__d=!1,J&&J(t),f=h.render(h.props,h.state,h.context),h.state=h.__s;while(h.__d&&++W<25);h.state=h.__s,null!=h.getChildContext&&(i=g(g({},i),h.getChildContext())),T&&!y&&null!=h.getSnapshotBeforeUpdate&&(E=h.getSnapshotBeforeUpdate(m,v)),A(e,_(M=null!=f&&f.type===w&&null==f.key?f.props.children:f)?M:[M],t,o,i,a,s,c,l,u,d),h.base=t.__e,t.__u&=-161,h.__h.length&&c.push(h),S&&(h.__E=h.__=null)}catch(e){if(t.__v=null,u||null!=s){for(t.__u|=u?160:128;l&&8===l.nodeType&&l.nextSibling;)l=l.nextSibling;s[s.indexOf(l)]=null,t.__e=l}else t.__e=o.__e,t.__k=o.__k;n.__e(e,t,o)}else null==s&&t.__v===o.__v?(t.__k=o.__k,t.__e=o.__e):t.__e=function(e,t,o,i,a,s,c,l,u){var d,f,h,y,g,m,v,w=o.props,x=t.props,E=t.type;if("svg"===E?a="http://www.w3.org/2000/svg":"math"===E?a="http://www.w3.org/1998/Math/MathML":a||(a="http://www.w3.org/1999/xhtml"),null!=s){for(d=0;d<s.length;d++)if((g=s[d])&&"setAttribute"in g==!!E&&(E?g.localName===E:3===g.nodeType)){e=g,s[d]=null;break}}if(null==e){if(null===E)return document.createTextNode(x);e=document.createElementNS(a,E,x.is&&x),l&&(n.__m&&n.__m(t,s),l=!1),s=null}if(null===E)w===x||l&&e.data===x||(e.data=x);else{if(s=s&&r.call(e.childNodes),w=o.props||p,!l&&null!=s)for(w={},d=0;d<e.attributes.length;d++)w[(g=e.attributes[d]).name]=g.value;for(d in w)if(g=w[d],"children"==d);else if("dangerouslySetInnerHTML"==d)h=g;else if(!(d in x)){if("value"==d&&"defaultValue"in x||"checked"==d&&"defaultChecked"in x)continue;P(e,d,null,g,a)}for(d in x)g=x[d],"children"==d?y=g:"dangerouslySetInnerHTML"==d?f=g:"value"==d?m=g:"checked"==d?v=g:l&&"function"!=typeof g||w[d]===g||P(e,d,g,w[d],a);if(f)l||h&&(f.__html===h.__html||f.__html===e.innerHTML)||(e.innerHTML=f.__html),t.__k=[];else if(h&&(e.innerHTML=""),A(e,_(y)?y:[y],t,o,i,"foreignObject"===E?"http://www.w3.org/1999/xhtml":a,s,c,s?s[0]:o.__k&&k(o,0),l,u),null!=s)for(d=s.length;d--;)b(s[d]);l||(d="value","progress"===E&&null==m?e.removeAttribute("value"):void 0===m||m===e[d]&&("progress"!==E||m)&&("option"!==E||m===w[d])||P(e,d,m,w[d],a),d="checked",void 0!==v&&v!==e[d]&&P(e,d,v,w[d],a))}return e}(o.__e,t,o,i,a,s,c,u,d);(f=n.diffed)&&f(t)}function j(e,t,r){t.__d=void 0;for(var o=0;o<r.length;o++)R(r[o],r[++o],r[++o]);n.__c&&n.__c(t,e),e.some(function(t){try{e=t.__h,t.__h=[],e.some(function(e){e.call(t)})}catch(e){n.__e(e,t.__v)}})}function R(e,t,r){try{if("function"==typeof e){var o="function"==typeof e.__u;o&&e.__u(),o&&null==t||(e.__u=e(t))}else e.current=t}catch(e){n.__e(e,r)}}function I(e,t,r){return this.constructor(e,r)}function J(e,t,o){var i,a,s,c;n.__&&n.__(e,t),a=(i="function"==typeof o)?null:o&&o.__k||t.__k,s=[],c=[],C(t,e=(!i&&o||t).__k=m(w,null,[e]),a||p,p,t.namespaceURI,!i&&o?[o]:a?null:t.firstChild?r.call(t.childNodes):null,s,!i&&o?o:a?a.__e:t.firstChild,i,c),j(s,e,c)}r=h.slice,n={__e:function(e,t,r,n){for(var o,i,a;t=t.__;)if((o=t.__c)&&!o.__)try{if((i=o.constructor)&&null!=i.getDerivedStateFromError&&(o.setState(i.getDerivedStateFromError(e)),a=o.__d),null!=o.componentDidCatch&&(o.componentDidCatch(e,n||{}),a=o.__d),a)return o.__E=o}catch(t){e=t}throw e}},o=0,x.prototype.setState=function(e,t){var r;r=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=g({},this.state),"function"==typeof e&&(e=e(g({},r),this.props)),e&&g(r,e),null!=e&&this.__v&&(t&&this._sb.push(t),E(this))},x.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),E(this))},x.prototype.render=w,i=[],s="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,c=function(e,t){return e.__v.__b-t.__v.__b},S.__r=0,l=0,u=T(!1),d=T(!0),f=0,t.Component=x,t.Fragment=w,t.cloneElement=function(e,t,n){var o,i,a,s,c=g({},e.props);for(a in e.type&&e.type.defaultProps&&(s=e.type.defaultProps),t)"key"==a?o=t[a]:"ref"==a?i=t[a]:c[a]=void 0===t[a]&&void 0!==s?s[a]:t[a];return arguments.length>2&&(c.children=arguments.length>3?r.call(arguments,2):n),v(e.type,c,o||e.key,i||e.ref,null)},t.createContext=function(e,t){var r={__c:t="__cC"+f++,__:e,Consumer:function(e,t){return e.children(t)},Provider:function(e){var r,n;return this.getChildContext||(r=new Set,(n={})[t]=this,this.getChildContext=function(){return n},this.componentWillUnmount=function(){r=null},this.shouldComponentUpdate=function(e){this.props.value!==e.value&&r.forEach(function(e){e.__e=!0,E(e)})},this.sub=function(e){r.add(e);var t=e.componentWillUnmount;e.componentWillUnmount=function(){r&&r.delete(e),t&&t.call(e)}}),e.children}};return r.Provider.__=r.Consumer.contextType=r},t.createElement=m,t.createRef=function(){return{current:null}},t.h=m,t.hydrate=function e(t,r){J(t,r,e)},t.isValidElement=function(e){return null!=e&&null==e.constructor},t.options=n,t.render=J,t.toChildArray=function e(t,r){return r=r||[],null==t||"boolean"==typeof t||(_(t)?t.some(function(t){e(t,r)}):r.push(t)),r}},1889:function(e,t,r){(function(e,t){var r=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|^--/i,n=/^(area|base|br|col|embed|hr|img|input|link|meta|param|source|track|wbr)$/,o=/[\s\n\\/='"\0<>]/,i=/^xlink:?./,a=/["&<]/;function s(e){if(!1===a.test(e+=""))return e;for(var t=0,r=0,n="",o="";r<e.length;r++){switch(e.charCodeAt(r)){case 34:o="&quot;";break;case 38:o="&amp;";break;case 60:o="&lt;";break;default:continue}r!==t&&(n+=e.slice(t,r)),n+=o,t=r+1}return r!==t&&(n+=e.slice(t,r)),n}var c=function(e,t){return String(e).replace(/(\n+)/g,"$1"+(t||"	"))},l=function(e,t,r){return String(e).length>(t||40)||!r&&-1!==String(e).indexOf("\n")||-1!==String(e).indexOf("<")},u={},d=/([A-Z])/g;function f(e){var t="";for(var n in e){var o=e[n];null!=o&&""!==o&&(t&&(t+=" "),t+="-"==n[0]?n:u[n]||(u[n]=n.replace(d,"-$1").toLowerCase()),t="number"==typeof o&&!1===r.test(n)?t+": "+o+"px;":t+": "+o+";")}return t||void 0}function p(e,t){return Array.isArray(t)?t.reduce(p,e):null!=t&&!1!==t&&e.push(t),e}function h(){this.__d=!0}function y(e,t){return{__v:e,context:t,props:e.props,setState:h,forceUpdate:h,__d:!0,__h:[]}}function _(e,t){var r=e.contextType,n=r&&t[r.__c];return null!=r?n?n.props.value:r.__:t}var g=[],b={shallow:!0};w.render=w;var m=function(e,t){return w(e,t,b)},v=[];function w(e,r,a){r=r||{};var u=t.options.__s;t.options.__s=!0;var d,h=t.h(t.Fragment,null);return h.__k=[e],d=a&&(a.pretty||a.voidElements||a.sortAttributes||a.shallow||a.allAttributes||a.xml||a.attributeHook)?function e(r,a,u,d,h,b){if(null==r||"boolean"==typeof r)return"";if("object"!=typeof r)return"function"==typeof r?"":s(r);var m=u.pretty,v=m&&"string"==typeof m?m:"	";if(Array.isArray(r)){for(var w="",x=0;x<r.length;x++)m&&x>0&&(w+="\n"),w+=e(r[x],a,u,d,h,b);return w}if(void 0!==r.constructor)return"";var k,E=r.type,S=r.props,A=!1;if("function"==typeof E){if(A=!0,!u.shallow||!d&&!1!==u.renderRootComponent){if(E===t.Fragment){var O=[];return p(O,r.props.children),e(O,a,u,!1!==u.shallowHighOrder,h,b)}var P,T=r.__c=y(r,a);t.options.__b&&t.options.__b(r);var C=t.options.__r;if(E.prototype&&"function"==typeof E.prototype.render){var j=_(E,a);(T=r.__c=new E(S,j)).__v=r,T._dirty=T.__d=!0,T.props=S,null==T.state&&(T.state={}),null==T._nextState&&null==T.__s&&(T._nextState=T.__s=T.state),T.context=j,E.getDerivedStateFromProps?T.state=Object.assign({},T.state,E.getDerivedStateFromProps(T.props,T.state)):T.componentWillMount&&(T.componentWillMount(),T.state=T._nextState!==T.state?T._nextState:T.__s!==T.state?T.__s:T.state),C&&C(r),P=T.render(T.props,T.state,T.context)}else for(var R=_(E,a),I=0;T.__d&&I++<25;)T.__d=!1,C&&C(r),P=E.call(r.__c,S,R);return T.getChildContext&&(a=Object.assign({},a,T.getChildContext())),t.options.diffed&&t.options.diffed(r),e(P,a,u,!1!==u.shallowHighOrder,h,b)}E=(k=E).displayName||k!==Function&&k.name||function(e){var t=(Function.prototype.toString.call(e).match(/^\s*function\s+([^( ]+)/)||"")[1];if(!t){for(var r=-1,n=g.length;n--;)if(g[n]===e){r=n;break}r<0&&(r=g.push(e)-1),t="UnnamedComponent"+r}return t}(k)}var J,W,H="<"+E;if(S){var M=Object.keys(S);u&&!0===u.sortAttributes&&M.sort();for(var K=0;K<M.length;K++){var U=M[K],$=S[U];if("children"!==U){if(!o.test(U)&&(u&&u.allAttributes||"key"!==U&&"ref"!==U&&"__self"!==U&&"__source"!==U)){if("defaultValue"===U)U="value";else if("defaultChecked"===U)U="checked";else if("defaultSelected"===U)U="selected";else if("className"===U){if(void 0!==S.class)continue;U="class"}else h&&i.test(U)&&(U=U.toLowerCase().replace(/^xlink:?/,"xlink:"));if("htmlFor"===U){if(S.for)continue;U="for"}"style"===U&&$&&"object"==typeof $&&($=f($)),"a"===U[0]&&"r"===U[1]&&"boolean"==typeof $&&($=String($));var D=u.attributeHook&&u.attributeHook(U,$,a,u,A);if(D||""===D)H+=D;else if("dangerouslySetInnerHTML"===U)W=$&&$.__html;else if("textarea"===E&&"value"===U)J=$;else if(($||0===$||""===$)&&"function"!=typeof $){if(!(!0!==$&&""!==$||($=U,u&&u.xml))){H=H+" "+U;continue}if("value"===U){if("select"===E){b=$;continue}"option"===E&&b==$&&void 0===S.selected&&(H+=" selected")}H=H+" "+U+'="'+s($)+'"'}}}else J=$}}if(m){var N=H.replace(/\n\s*/," ");N===H||~N.indexOf("\n")?m&&~H.indexOf("\n")&&(H+="\n"):H=N}if(H+=">",o.test(E))throw Error(E+" is not a valid HTML tag name in "+H);var L,B=n.test(E)||u.voidElements&&u.voidElements.test(E),q=[];if(W)m&&l(W)&&(W="\n"+v+c(W,v)),H+=W;else if(null!=J&&p(L=[],J).length){for(var z=m&&~H.indexOf("\n"),F=!1,G=0;G<L.length;G++){var V=L[G];if(null!=V&&!1!==V){var X=e(V,a,u,!0,"svg"===E||"foreignObject"!==E&&h,b);if(m&&!z&&l(X)&&(z=!0),X)if(m){var Y=X.length>0&&"<"!=X[0];F&&Y?q[q.length-1]+=X:q.push(X),F=Y}else q.push(X)}}if(m&&z)for(var Z=q.length;Z--;)q[Z]="\n"+v+c(q[Z],v)}if(q.length||W)H+=q.join("");else if(u&&u.xml)return H.substring(0,H.length-1)+" />";return!B||L||W?(m&&~H.indexOf("\n")&&(H+="\n"),H=H+"</"+E+">"):H=H.replace(/>$/," />"),H}(e,r,a):function e(r,a,c,l,u){if(null==r||!0===r||!1===r||""===r)return"";if("object"!=typeof r)return"function"==typeof r?"":s(r);if(k(r)){var d="";u.__k=r;for(var p=0;p<r.length;p++)d+=e(r[p],a,c,l,u),r[p]=x(r[p]);return d}if(void 0!==r.constructor)return"";r.__=u,t.options.__b&&t.options.__b(r);var h=r.type,g=r.props;if("function"==typeof h){if(h===t.Fragment)A=g.children;else{var b,m,v,w,S,A=h.prototype&&"function"==typeof h.prototype.render?(b=a,v=_(m=r.type,b),w=new m(r.props,v),r.__c=w,w.__v=r,w.__d=!0,w.props=r.props,null==w.state&&(w.state={}),null==w.__s&&(w.__s=w.state),w.context=v,m.getDerivedStateFromProps?w.state=E({},w.state,m.getDerivedStateFromProps(w.props,w.state)):w.componentWillMount&&(w.componentWillMount(),w.state=w.__s!==w.state?w.__s:w.state),(S=t.options.__r)&&S(r),w.render(w.props,w.state,w.context)):function(e,r){var n,o=y(e,r),i=_(e.type,r);e.__c=o;for(var a=t.options.__r,s=0;o.__d&&s++<25;)o.__d=!1,a&&a(e),n=e.type.call(o,e.props,i);return n}(r,a),O=r.__c;O.getChildContext&&(a=E({},a,O.getChildContext()))}var P=e(A=null!=A&&A.type===t.Fragment&&null==A.key?A.props.children:A,a,c,l,r);return t.options.diffed&&t.options.diffed(r),r.__=void 0,t.options.unmount&&t.options.unmount(r),P}var T,C,j="<";if(j+=h,g)for(var R in T=g.children,g){var I,J,W,H=g[R];if(!("key"===R||"ref"===R||"__self"===R||"__source"===R||"children"===R||"className"===R&&"class"in g||"htmlFor"===R&&"for"in g||o.test(R))){if(J=R="className"===(I=R)?"class":"htmlFor"===I?"for":"defaultValue"===I?"value":"defaultChecked"===I?"checked":"defaultSelected"===I?"selected":c&&i.test(I)?I.toLowerCase().replace(/^xlink:?/,"xlink:"):I,W=H,H="style"===J&&null!=W&&"object"==typeof W?f(W):"a"===J[0]&&"r"===J[1]&&"boolean"==typeof W?String(W):W,"dangerouslySetInnerHTML"===R)C=H&&H.__html;else if("textarea"===h&&"value"===R)T=H;else if((H||0===H||""===H)&&"function"!=typeof H){if(!0===H||""===H){H=R,j=j+" "+R;continue}if("value"===R){if("select"===h){l=H;continue}"option"!==h||l!=H||"selected"in g||(j+=" selected")}j=j+" "+R+'="'+s(H)+'"'}}}var M=j;if(j+=">",o.test(h))throw Error(h+" is not a valid HTML tag name in "+j);var K="",U=!1;if(C)K+=C,U=!0;else if("string"==typeof T)K+=s(T),U=!0;else if(k(T)){r.__k=T;for(var $=0;$<T.length;$++){var D=T[$];if(T[$]=x(D),null!=D&&!1!==D){var N=e(D,a,"svg"===h||"foreignObject"!==h&&c,l,r);N&&(K+=N,U=!0)}}}else if(null!=T&&!1!==T&&!0!==T){r.__k=[x(T)];var L=e(T,a,"svg"===h||"foreignObject"!==h&&c,l,r);L&&(K+=L,U=!0)}if(t.options.diffed&&t.options.diffed(r),r.__=void 0,t.options.unmount&&t.options.unmount(r),U)j+=K;else if(n.test(h))return M+" />";return j+"</"+h+">"}(e,r,!1,void 0,h),t.options.__c&&t.options.__c(e,v),t.options.__s=u,v.length=0,d}function x(e){return null==e||"boolean"==typeof e?null:"string"==typeof e||"number"==typeof e||"bigint"==typeof e?t.h(null,null,e):e}var k=Array.isArray,E=Object.assign;w.shallowRender=m,e.default=w,e.render=w,e.renderToStaticMarkup=w,e.renderToString=w,e.shallowRender=m})(t,r(1441))},2055:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createLocalJWKSet=t.LocalJWKSet=t.isJWKSLike=void 0;let n=r(97920),o=r(99938),i=r(25110);function a(e){return e&&"object"==typeof e&&Array.isArray(e.keys)&&e.keys.every(s)}function s(e){return(0,i.default)(e)}t.isJWKSLike=a;class c{constructor(e){if(this._cached=new WeakMap,!a(e))throw new o.JWKSInvalid("JSON Web Key Set malformed");this._jwks=function(e){return"function"==typeof structuredClone?structuredClone(e):JSON.parse(JSON.stringify(e))}(e)}async getKey(e,t){let{alg:r,kid:n}={...e,...null==t?void 0:t.header},i=function(e){switch("string"==typeof e&&e.slice(0,2)){case"RS":case"PS":return"RSA";case"ES":return"EC";case"Ed":return"OKP";default:throw new o.JOSENotSupported('Unsupported "alg" value for a JSON Web Key Set')}}(r),a=this._jwks.keys.filter(e=>{let t=i===e.kty;if(t&&"string"==typeof n&&(t=n===e.kid),t&&"string"==typeof e.alg&&(t=r===e.alg),t&&"string"==typeof e.use&&(t="sig"===e.use),t&&Array.isArray(e.key_ops)&&(t=e.key_ops.includes("verify")),t&&"EdDSA"===r&&(t="Ed25519"===e.crv||"Ed448"===e.crv),t)switch(r){case"ES256":t="P-256"===e.crv;break;case"ES256K":t="secp256k1"===e.crv;break;case"ES384":t="P-384"===e.crv;break;case"ES512":t="P-521"===e.crv}return t}),{0:s,length:c}=a;if(0===c)throw new o.JWKSNoMatchingKey;if(1!==c){let e=new o.JWKSMultipleMatchingKeys,{_cached:t}=this;throw e[Symbol.asyncIterator]=async function*(){for(let e of a)try{yield await l(t,e,r)}catch{continue}},e}return l(this._cached,s,r)}}async function l(e,t,r){let i=e.get(t)||e.set(t,{}).get(t);if(void 0===i[r]){let e=await (0,n.importJWK)({...t,ext:!0},r);if(e instanceof Uint8Array||"public"!==e.type)throw new o.JWKSInvalid("JSON Web Key Set members must be public keys");i[r]=e}return i[r]}t.LocalJWKSet=c,t.createLocalJWKSet=function(e){let t=new c(e);return async function(e,r){return t.getKey(e,r)}}},2279:e=>{e.exports={assertSigningAlgValuesSupport:function(e,t,r){if(!t[`${e}_endpoint`])return;let n=`${e}_endpoint_auth_method`,o=`${e}_endpoint_auth_signing_alg`,i=`${e}_endpoint_auth_signing_alg_values_supported`;if(r[n]&&r[n].endsWith("_jwt")&&!r[o]&&!t[i])throw TypeError(`${i} must be configured on the issuer if ${o} is not defined on a client`)},assertIssuerConfiguration:function(e,t){if(!e[t])throw TypeError(`${t} must be configured on the issuer`)}}},2594:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.concatKdf=t.lengthAndInput=t.uint32be=t.uint64be=t.p2s=t.concat=t.decoder=t.encoder=void 0;let n=r(37375);function o(...e){let t=new Uint8Array(e.reduce((e,{length:t})=>e+t,0)),r=0;return e.forEach(e=>{t.set(e,r),r+=e.length}),t}function i(e,t,r){if(t<0||t>=0x100000000)throw RangeError(`value must be >= 0 and <= ${0x100000000-1}. Received ${t}`);e.set([t>>>24,t>>>16,t>>>8,255&t],r)}function a(e){let t=new Uint8Array(4);return i(t,e),t}t.encoder=new TextEncoder,t.decoder=new TextDecoder,t.concat=o,t.p2s=function(e,r){return o(t.encoder.encode(e),new Uint8Array([0]),r)},t.uint64be=function(e){let t=Math.floor(e/0x100000000),r=new Uint8Array(8);return i(r,t,0),i(r,e%0x100000000,4),r},t.uint32be=a,t.lengthAndInput=function(e){return o(a(e.length),e)},t.concatKdf=async function(e,t,r){let o=Math.ceil((t>>3)/32),i=new Uint8Array(32*o);for(let t=0;t<o;t++){let o=new Uint8Array(4+e.length+r.length);o.set(a(t+1)),o.set(e,4),o.set(r,4+e.length),i.set(await (0,n.default)("sha256",o),32*t)}return i.slice(0,t>>3)}},2754:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ProduceJWT=void 0;let n=r(69717),o=r(25110),i=r(86852);class a{constructor(e){if(!(0,o.default)(e))throw TypeError("JWT Claims Set MUST be an object");this._payload=e}setIssuer(e){return this._payload={...this._payload,iss:e},this}setSubject(e){return this._payload={...this._payload,sub:e},this}setAudience(e){return this._payload={...this._payload,aud:e},this}setJti(e){return this._payload={...this._payload,jti:e},this}setNotBefore(e){return"number"==typeof e?this._payload={...this._payload,nbf:e}:this._payload={...this._payload,nbf:(0,n.default)(new Date)+(0,i.default)(e)},this}setExpirationTime(e){return"number"==typeof e?this._payload={...this._payload,exp:e}:this._payload={...this._payload,exp:(0,n.default)(new Date)+(0,i.default)(e)},this}setIssuedAt(e){return void 0===e?this._payload={...this._payload,iat:(0,n.default)(new Date)}:this._payload={...this._payload,iat:e},this}}t.ProduceJWT=a},3038:(e,t,r)=>{var n=r(24956).default,o=r(7251);e.exports=function(e){var t=o(e,"string");return"symbol"==n(t)?t:t+""},e.exports.__esModule=!0,e.exports.default=e.exports},3399:(e,t,r)=>{var n=r(3038);e.exports=function(e,t,r){return(t=n(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e},e.exports.__esModule=!0,e.exports.default=e.exports},4275:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(79428),o=r(99938),i=n.Buffer.from([0]),a=n.Buffer.from([2]),s=n.Buffer.from([3]),c=n.Buffer.from([48]),l=n.Buffer.from([4]),u=e=>{if(e<128)return n.Buffer.from([e]);let t=n.Buffer.alloc(5);t.writeUInt32BE(e,1);let r=1;for(;0===t[r];)r++;return t[r-1]=128|5-r,t.slice(r-1)},d=new Map([["P-256",n.Buffer.from("06 08 2A 86 48 CE 3D 03 01 07".replace(/ /g,""),"hex")],["secp256k1",n.Buffer.from("06 05 2B 81 04 00 0A".replace(/ /g,""),"hex")],["P-384",n.Buffer.from("06 05 2B 81 04 00 22".replace(/ /g,""),"hex")],["P-521",n.Buffer.from("06 05 2B 81 04 00 23".replace(/ /g,""),"hex")],["ecPublicKey",n.Buffer.from("06 07 2A 86 48 CE 3D 02 01".replace(/ /g,""),"hex")],["X25519",n.Buffer.from("06 03 2B 65 6E".replace(/ /g,""),"hex")],["X448",n.Buffer.from("06 03 2B 65 6F".replace(/ /g,""),"hex")],["Ed25519",n.Buffer.from("06 03 2B 65 70".replace(/ /g,""),"hex")],["Ed448",n.Buffer.from("06 03 2B 65 71".replace(/ /g,""),"hex")]]);class f{constructor(){this.length=0,this.elements=[]}oidFor(e){let t=d.get(e);if(!t)throw new o.JOSENotSupported("Invalid or unsupported OID");this.elements.push(t),this.length+=t.length}zero(){this.elements.push(a,n.Buffer.from([1]),i),this.length+=3}one(){this.elements.push(a,n.Buffer.from([1]),n.Buffer.from([1])),this.length+=3}unsignedInteger(e){if(128&e[0]){let t=u(e.length+1);this.elements.push(a,t,i,e),this.length+=2+t.length+e.length}else{let t=0;for(;0===e[t]&&(128&e[t+1])==0;)t++;let r=u(e.length-t);this.elements.push(a,u(e.length-t),e.slice(t)),this.length+=1+r.length+e.length-t}}octStr(e){let t=u(e.length);this.elements.push(l,u(e.length),e),this.length+=1+t.length+e.length}bitStr(e){let t=u(e.length+1);this.elements.push(s,u(e.length+1),i,e),this.length+=1+t.length+e.length+1}add(e){this.elements.push(e),this.length+=e.length}end(e=c){let t=u(this.length);return n.Buffer.concat([e,t,...this.elements],1+t.length+this.length)}}t.default=f},6680:(e,t)=>{"use strict";function r(e){return e&&"object"==typeof e&&!Array.isArray(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.merge=function e(t,...n){if(!n.length)return t;let o=n.shift();if(r(t)&&r(o))for(let n in o)r(o[n])?(t[n]||Object.assign(t,{[n]:{}}),e(t[n],o[n])):Object.assign(t,{[n]:o[n]});return e(t,...n)}},6702:(e,t)=>{function r(e){for(var t,r,n="",o=-1;++o<e.length;)t=e.charCodeAt(o),r=o+1<e.length?e.charCodeAt(o+1):0,55296<=t&&t<=56319&&56320<=r&&r<=57343&&(t=65536+((1023&t)<<10)+(1023&r),o++),t<=127?n+=String.fromCharCode(t):t<=2047?n+=String.fromCharCode(192|t>>>6&31,128|63&t):t<=65535?n+=String.fromCharCode(224|t>>>12&15,128|t>>>6&63,128|63&t):t<=2097151&&(n+=String.fromCharCode(240|t>>>18&7,128|t>>>12&63,128|t>>>6&63,128|63&t));return n}function n(e){for(var t=Array(e.length>>2),r=0;r<t.length;r++)t[r]=0;for(var r=0;r<8*e.length;r+=8)t[r>>5]|=(255&e.charCodeAt(r/8))<<24-r%32;return t}function o(e){for(var t="",r=0;r<32*e.length;r+=8)t+=String.fromCharCode(e[r>>5]>>>24-r%32&255);return t}function i(e,t){e[t>>5]|=128<<24-t%32,e[(t+64>>9<<4)+15]=t;for(var r=Array(80),n=0x67452301,o=-0x10325477,i=-0x67452302,c=0x10325476,l=-0x3c2d1e10,u=0;u<e.length;u+=16){for(var d=n,f=o,p=i,h=c,y=l,_=0;_<80;_++){_<16?r[_]=e[u+_]:r[_]=s(r[_-3]^r[_-8]^r[_-14]^r[_-16],1);var g,b,m,v,w,x=a(a(s(n,5),(g=_,b=o,m=i,v=c,g<20?b&m|~b&v:g<40?b^m^v:g<60?b&m|b&v|m&v:b^m^v)),a(a(l,r[_]),(w=_)<20?0x5a827999:w<40?0x6ed9eba1:w<60?-0x70e44324:-0x359d3e2a));l=c,c=i,i=s(o,30),o=n,n=x}n=a(n,d),o=a(o,f),i=a(i,p),c=a(c,h),l=a(l,y)}return[n,o,i,c,l]}function a(e,t){var r=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(r>>16)<<16|65535&r}function s(e,t){return e<<t|e>>>32-t}t.HMACSHA1=function(e,t){return function(e){for(var t="",r=e.length,n=0;n<r;n+=3)for(var o=e.charCodeAt(n)<<16|(n+1<r?e.charCodeAt(n+1)<<8:0)|(n+2<r?e.charCodeAt(n+2):0),i=0;i<4;i++)8*n+6*i>8*e.length?t+="=":t+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(o>>>6*(3-i)&63);return t}(function(e,t){var r=n(e);r.length>16&&(r=i(r,8*e.length));for(var a=Array(16),s=Array(16),c=0;c<16;c++)a[c]=0x36363636^r[c],s[c]=0x5c5c5c5c^r[c];var l=i(a.concat(n(t)),512+8*t.length);return o(i(s.concat(l),672))}(r(e),r(t)))}},7161:(e,t,r)=>{let n=r(55511),[o,i]=process.version.substring(1).split(".").map(e=>parseInt(e,10));e.exports=(o>12||12===o&&i>=8)&&n.getHashes().includes("shake256")},7251:(e,t,r)=>{var n=r(24956).default;e.exports=function(e,t){if("object"!=n(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var o=r.call(e,t||"default");if("object"!=n(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports},7552:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(55511);Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n.randomFillSync}})},8157:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=r(67131).default},8365:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.oAuth1Client=function(e){var t,r;let o=e.provider,i=new n.OAuth(o.requestTokenUrl,o.accessTokenUrl,o.clientId,o.clientSecret,null!=(t=o.version)?t:"1.0",o.callbackUrl,null!=(r=o.encoding)?r:"HMAC-SHA1"),a=i.get.bind(i);i.get=async(...e)=>await new Promise((t,r)=>{a(...e,(e,n)=>{if(e)return r(e);t(n)})});let s=i.getOAuthAccessToken.bind(i);i.getOAuthAccessToken=async(...e)=>await new Promise((t,r)=>{s(...e,(e,n,o)=>{if(e)return r(e);t({oauth_token:n,oauth_token_secret:o})})});let c=i.getOAuthRequestToken.bind(i);return i.getOAuthRequestToken=async(e={})=>await new Promise((t,r)=>{c(e,(e,n,o,i)=>{if(e)return r(e);t({oauth_token:n,oauth_token_secret:o,params:i})})}),i},t.oAuth1TokenStore=void 0;var n=r(11819);t.oAuth1TokenStore=new Map},8535:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(99938);t.default=function(e,t,r,o,i){let a;if(void 0!==i.crit&&void 0===o.crit)throw new e('"crit" (Critical) Header Parameter MUST be integrity protected');if(!o||void 0===o.crit)return new Set;if(!Array.isArray(o.crit)||0===o.crit.length||o.crit.some(e=>"string"!=typeof e||0===e.length))throw new e('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let s of(a=void 0!==r?new Map([...Object.entries(r),...t.entries()]):t,o.crit)){if(!a.has(s))throw new n.JOSENotSupported(`Extension Header Parameter "${s}" is not recognized`);if(void 0===i[s])throw new e(`Extension Header Parameter "${s}" is missing`);if(a.get(s)&&void 0===o[s])throw new e(`Extension Header Parameter "${s}" MUST be integrity protected`)}return new Set(o.crit)}},8557:(e,t,r)=>{"use strict";var n=r(9168);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t;let{csrfToken:r,providers:n,callbackUrl:s,theme:c,email:l,error:u}=e,d=n.filter(e=>"oauth"===e.type||"email"===e.type||"credentials"===e.type&&!!e.credentials);"undefined"!=typeof document&&c.buttonText&&document.documentElement.style.setProperty("--button-text-color",c.buttonText),"undefined"!=typeof document&&c.brandColor&&document.documentElement.style.setProperty("--brand-color",c.brandColor);let f={Signin:"Try signing in with a different account.",OAuthSignin:"Try signing in with a different account.",OAuthCallback:"Try signing in with a different account.",OAuthCreateAccount:"Try signing in with a different account.",EmailCreateAccount:"Try signing in with a different account.",Callback:"Try signing in with a different account.",OAuthAccountNotLinked:"To confirm your identity, sign in with the same account you used originally.",EmailSignin:"The e-mail could not be sent.",CredentialsSignin:"Sign in failed. Check the details you provided are correct.",SessionRequired:"Please sign in to access this page.",default:"Unable to sign in."},p=u&&(null!=(t=f[u])?t:f.default),h="https://authjs.dev/img/providers";return(0,o.h)("div",{className:"signin"},c.brandColor&&(0,o.h)("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${c.brandColor}
        }
      `}}),c.buttonText&&(0,o.h)("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${c.buttonText}
        }
      `}}),(0,o.h)("div",{className:"card"},c.logo&&(0,o.h)("img",{src:c.logo,alt:"Logo",className:"logo"}),p&&(0,o.h)("div",{className:"error"},(0,o.h)("p",null,p)),d.map((e,t)=>{let n,c,u,f,p,y;if("oauth"===e.type){var _;({bg:n="",text:c="",logo:u="",bgDark:p=n,textDark:y=c,logoDark:f=""}=null!=(_=e.style)?_:{}),u=u.startsWith("/")?`${h}${u}`:u,(f=f.startsWith("/")?`${h}${f}`:f||u)||(f=u)}return(0,o.h)("div",{key:e.id,className:"provider"},"oauth"===e.type&&(0,o.h)("form",{action:e.signinUrl,method:"POST"},(0,o.h)("input",{type:"hidden",name:"csrfToken",value:r}),s&&(0,o.h)("input",{type:"hidden",name:"callbackUrl",value:s}),(0,o.h)("button",{type:"submit",className:"button",style:{"--provider-bg":n,"--provider-dark-bg":p,"--provider-color":c,"--provider-dark-color":y,"--provider-bg-hover":a(n,.8),"--provider-dark-bg-hover":a(p,.8)}},u&&(0,o.h)("img",{loading:"lazy",height:24,width:24,id:"provider-logo",src:`${u.startsWith("/")?h:""}${u}`}),f&&(0,o.h)("img",{loading:"lazy",height:24,width:24,id:"provider-logo-dark",src:`${u.startsWith("/")?h:""}${f}`}),(0,o.h)("span",null,"Sign in with ",e.name))),("email"===e.type||"credentials"===e.type)&&t>0&&"email"!==d[t-1].type&&"credentials"!==d[t-1].type&&(0,o.h)("hr",null),"email"===e.type&&(0,o.h)("form",{action:e.signinUrl,method:"POST"},(0,o.h)("input",{type:"hidden",name:"csrfToken",value:r}),(0,o.h)("label",{className:"section-header",htmlFor:`input-email-for-${e.id}-provider`},"Email"),(0,o.h)("input",{id:`input-email-for-${e.id}-provider`,autoFocus:!0,type:"email",name:"email",value:l,placeholder:"<EMAIL>",required:!0}),(0,o.h)("button",{id:"submitButton",type:"submit"},"Sign in with ",e.name)),"credentials"===e.type&&(0,o.h)("form",{action:e.callbackUrl,method:"POST"},(0,o.h)("input",{type:"hidden",name:"csrfToken",value:r}),Object.keys(e.credentials).map(t=>{var r,n,a;return(0,o.h)("div",{key:`input-group-${e.id}`},(0,o.h)("label",{className:"section-header",htmlFor:`input-${t}-for-${e.id}-provider`},null!=(r=e.credentials[t].label)?r:t),(0,o.h)("input",(0,i.default)({name:t,id:`input-${t}-for-${e.id}-provider`,type:null!=(n=e.credentials[t].type)?n:"text",placeholder:null!=(a=e.credentials[t].placeholder)?a:""},e.credentials[t])))}),(0,o.h)("button",{type:"submit"},"Sign in with ",e.name)),("email"===e.type||"credentials"===e.type)&&t+1<d.length&&(0,o.h)("hr",null))})))};var o=r(1441),i=n(r(17696));function a(e,t=1){if(!e)return;3===(e=e.replace(/^#/,"")).length&&(e=e[0]+e[0]+e[1]+e[1]+e[2]+e[2]);let r=parseInt(e,16);return t=Math.min(Math.max(t,0),1),`rgba(${r>>16&255}, ${r>>8&255}, ${255&r}, ${t})`}},9168:e=>{e.exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},9534:(e,t,r)=>{"use strict";let n;Object.defineProperty(t,"__esModule",{value:!0});let o=r(55511),i=r(28354),a=r(63898),s=r(42567),c=r(92562),l=r(15055),u=r(80028);n=o.verify.length>4&&u.oneShotCallback?(0,i.promisify)(o.verify):o.verify,t.default=async(e,t,r,i)=>{let u=(0,l.default)(e,t,"verify");if(e.startsWith("HS")){let t=await (0,c.default)(e,u,i);try{return o.timingSafeEqual(r,t)}catch{return!1}}let d=(0,a.default)(e),f=(0,s.default)(e,u);try{return await n(d,i,f,r)}catch{return!1}}},9545:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.openidClient=o;var n=r(12107);async function o(e){let t,r=e.provider;if(r.httpOptions&&n.custom.setHttpOptionsDefaults(r.httpOptions),r.wellKnown)t=await n.Issuer.discover(r.wellKnown);else{var o,i,a;t=new n.Issuer({issuer:r.issuer,authorization_endpoint:null==(o=r.authorization)?void 0:o.url,token_endpoint:null==(i=r.token)?void 0:i.url,userinfo_endpoint:null==(a=r.userinfo)?void 0:a.url,jwks_uri:r.jwks_endpoint})}let s=new t.Client({client_id:r.clientId,client_secret:r.clientSecret,redirect_uris:[r.callbackUrl],...r.client},r.jwks);return s[n.custom.clock_tolerance]=10,s}},9552:(e,t,r)=>{let n,o=r(12412),i=r(11723),a=r(81630),s=r(55591),{once:c}=r(94735),{URL:l}=r(79551),u=r(42142),d=r(63667),{RPError:f}=r(61408),p=r(24740),{deep:h}=r(72115),{HTTP_OPTIONS:y}=r(43325),_=/^[\x21\x23-\x5B\x5D-\x7E]+$/,g=["agent","ca","cert","crl","headers","key","lookup","passphrase","pfx","timeout"],b=(e,t)=>{n=h({},e.length?p(t,...e):t,n)};function m(e,t,r){r&&(e.removeHeader("content-type"),e.setHeader("content-type",r)),t&&(e.removeHeader("content-length"),e.setHeader("content-length",Buffer.byteLength(t)),e.write(t)),e.end()}b([],{headers:{"User-Agent":`${d.name}/${d.version} (${d.homepage})`,"Accept-Encoding":"identity"},timeout:3500});let v=new u({max:100});e.exports=async function(e,{accessToken:t,mTLS:r=!1,DPoP:u}={}){let d,b,w,x,k,E,S;try{d=new l(e.url),delete e.url,o(/^(https?:)$/.test(d.protocol))}catch(e){throw TypeError("only valid absolute URLs can be requested")}let A=this[y],O=e,P=`${d.origin}${d.pathname}`;if(u&&"dpopProof"in this&&(O.headers=O.headers||{},O.headers.DPoP=await this.dpopProof({htu:`${d.origin}${d.pathname}`,htm:e.method||"GET",nonce:v.get(P)},u,t)),A&&(b=p(A.call(this,d,h({},O,n)),...g)),O=h({},b,O,n),r&&!O.pfx&&!(O.key&&O.cert))throw TypeError("mutual-TLS certificate and key not set");if(O.searchParams)for(let[e,t]of Object.entries(O.searchParams))d.searchParams.delete(e),d.searchParams.set(e,t);for(let[e,t]of({form:x,responseType:w,json:k,body:E,...O}=O,Object.entries(O.headers||{})))void 0===t&&delete O.headers[e];let T=("https:"===d.protocol?s.request:a.request)(d.href,O);return(async()=>{if(k?m(T,JSON.stringify(k),"application/json"):x?m(T,i.stringify(x),"application/x-www-form-urlencoded"):E?m(T,E):m(T),[S]=await Promise.race([c(T,"response"),c(T,"timeout")]),!S)throw T.destroy(),new f(`outgoing request timed out after ${O.timeout}ms`);let e=[];for await(let t of S)e.push(t);if(e.length)switch(w){case"json":Object.defineProperty(S,"body",{get(){let t=Buffer.concat(e);try{t=JSON.parse(t)}catch(e){throw Object.defineProperty(e,"response",{value:S}),e}finally{Object.defineProperty(S,"body",{value:t,configurable:!0})}return t},configurable:!0});break;case void 0:case"buffer":Object.defineProperty(S,"body",{get(){let t=Buffer.concat(e);return Object.defineProperty(S,"body",{value:t,configurable:!0}),t},configurable:!0});break;default:throw TypeError("unsupported responseType request option")}return S})().catch(e=>{throw S&&Object.defineProperty(e,"response",{value:S}),e}).finally(()=>{let e=S&&S.headers["dpop-nonce"];e&&_.test(e)&&v.set(P,e)})},e.exports.setDefaults=b.bind(void 0,g)},9684:(e,t,r)=>{var n=r(96376),o=r(30994);e.exports=function(e,t,r){if(n())return Reflect.construct.apply(null,arguments);var i=[null];i.push.apply(i,t);var a=new(e.bind.apply(e,i));return r&&o(a,r.prototype),a},e.exports.__esModule=!0,e.exports.default=e.exports},10028:(e,t,r)=>{e.exports=new(r(42142))({max:100})},11714:(e,t)=>{"use strict";async function r({email:e,adapter:t}){let{getUserByEmail:r}=t,n=e?await r(e):null;return n||{id:e,email:e,emailVerified:null}}Object.defineProperty(t,"__esModule",{value:!0}),t.default=r},11819:(e,t,r)=>{t.OAuth=r(28328).OAuth,t.OAuthEcho=r(28328).OAuthEcho,t.OAuth2=r(36720).OAuth2},12107:(e,t,r)=>{let n=r(23884),{OPError:o,RPError:i}=r(61408),a=r(58513),s=r(24453),{CLOCK_TOLERANCE:c,HTTP_OPTIONS:l}=r(43325),u=r(35571),{setDefaults:d}=r(9552);e.exports={Issuer:n,Strategy:a,TokenSet:s,errors:{OPError:o,RPError:i},custom:{setHttpOptionsDefaults:d,http_options:l,clock_tolerance:c},generators:u}},12269:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},13581:(e,t)=>{"use strict";t.A=function(e){return{id:"credentials",name:"Credentials",type:"credentials",credentials:{},authorize:()=>null,options:e}}},13668:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.generateKeyPair=void 0;let n=r(51688);t.generateKeyPair=async function(e,t){return(0,n.generateKeyPair)(e,t)}},14988:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.FlattenedEncrypt=t.unprotected=void 0;let n=r(68327),o=r(22122),i=r(66318),a=r(35749),s=r(22599),c=r(99938),l=r(65597),u=r(2594),d=r(8535);t.unprotected=Symbol();class f{constructor(e){if(!(e instanceof Uint8Array))throw TypeError("plaintext must be an instance of Uint8Array");this._plaintext=e}setKeyManagementParameters(e){if(this._keyManagementParameters)throw TypeError("setKeyManagementParameters can only be called once");return this._keyManagementParameters=e,this}setProtectedHeader(e){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=e,this}setSharedUnprotectedHeader(e){if(this._sharedUnprotectedHeader)throw TypeError("setSharedUnprotectedHeader can only be called once");return this._sharedUnprotectedHeader=e,this}setUnprotectedHeader(e){if(this._unprotectedHeader)throw TypeError("setUnprotectedHeader can only be called once");return this._unprotectedHeader=e,this}setAdditionalAuthenticatedData(e){return this._aad=e,this}setContentEncryptionKey(e){if(this._cek)throw TypeError("setContentEncryptionKey can only be called once");return this._cek=e,this}setInitializationVector(e){if(this._iv)throw TypeError("setInitializationVector can only be called once");return this._iv=e,this}async encrypt(e,r){let f,p,h,y,_,g,b;if(!this._protectedHeader&&!this._unprotectedHeader&&!this._sharedUnprotectedHeader)throw new c.JWEInvalid("either setProtectedHeader, setUnprotectedHeader, or sharedUnprotectedHeader must be called before #encrypt()");if(!(0,l.default)(this._protectedHeader,this._unprotectedHeader,this._sharedUnprotectedHeader))throw new c.JWEInvalid("JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint");let m={...this._protectedHeader,...this._unprotectedHeader,...this._sharedUnprotectedHeader};if((0,d.default)(c.JWEInvalid,new Map,null==r?void 0:r.crit,this._protectedHeader,m),void 0!==m.zip){if(!this._protectedHeader||!this._protectedHeader.zip)throw new c.JWEInvalid('JWE "zip" (Compression Algorithm) Header MUST be integrity protected');if("DEF"!==m.zip)throw new c.JOSENotSupported('Unsupported JWE "zip" (Compression Algorithm) Header Parameter value')}let{alg:v,enc:w}=m;if("string"!=typeof v||!v)throw new c.JWEInvalid('JWE "alg" (Algorithm) Header Parameter missing or invalid');if("string"!=typeof w||!w)throw new c.JWEInvalid('JWE "enc" (Encryption Algorithm) Header Parameter missing or invalid');if("dir"===v){if(this._cek)throw TypeError("setContentEncryptionKey cannot be called when using Direct Encryption")}else if("ECDH-ES"===v&&this._cek)throw TypeError("setContentEncryptionKey cannot be called when using Direct Key Agreement");{let n;({cek:p,encryptedKey:f,parameters:n}=await (0,s.default)(v,w,e,this._cek,this._keyManagementParameters)),n&&(r&&t.unprotected in r?this._unprotectedHeader?this._unprotectedHeader={...this._unprotectedHeader,...n}:this.setUnprotectedHeader(n):this._protectedHeader?this._protectedHeader={...this._protectedHeader,...n}:this.setProtectedHeader(n))}if(this._iv||(this._iv=(0,a.default)(w)),y=this._protectedHeader?u.encoder.encode((0,n.encode)(JSON.stringify(this._protectedHeader))):u.encoder.encode(""),this._aad?(_=(0,n.encode)(this._aad),h=(0,u.concat)(y,u.encoder.encode("."),u.encoder.encode(_))):h=y,"DEF"===m.zip){let e=await ((null==r?void 0:r.deflateRaw)||i.deflate)(this._plaintext);({ciphertext:g,tag:b}=await (0,o.default)(w,e,p,this._iv,h))}else({ciphertext:g,tag:b}=await (0,o.default)(w,this._plaintext,p,this._iv,h));let x={ciphertext:(0,n.encode)(g),iv:(0,n.encode)(this._iv),tag:(0,n.encode)(b)};return f&&(x.encrypted_key=(0,n.encode)(f)),_&&(x.aad=_),this._protectedHeader&&(x.protected=u.decoder.decode(y)),this._sharedUnprotectedHeader&&(x.unprotected=this._sharedUnprotectedHeader),this._unprotectedHeader&&(x.header=this._unprotectedHeader),x}}t.FlattenedEncrypt=f},15055:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(55511),o=r(17996),i=r(50003),a=r(78680),s=r(37265);t.default=function(e,t,r){if(t instanceof Uint8Array){if(!e.startsWith("HS"))throw TypeError((0,a.default)(t,...s.types));return(0,n.createSecretKey)(t)}if(t instanceof n.KeyObject)return t;if((0,o.isCryptoKey)(t))return(0,i.checkSigCryptoKey)(t,e,r),n.KeyObject.from(t);throw TypeError((0,a.default)(t,...s.types,"Uint8Array"))}},15912:(e,t,r)=>{"use strict";var n=r(9168);Object.defineProperty(t,"__esModule",{value:!0});var o={encode:!0,decode:!0,getToken:!0};t.decode=f,t.encode=d,t.getToken=p;var i=r(37544),a=n(r(94387)),s=r(72921),c=r(99632),l=r(90253);Object.keys(l).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in t&&t[e]===l[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return l[e]}}))});let u=()=>Date.now()/1e3|0;async function d(e){let{token:t={},secret:r,maxAge:n=2592e3,salt:o=""}=e,a=await h(r,o);return await new i.EncryptJWT(t).setProtectedHeader({alg:"dir",enc:"A256GCM"}).setIssuedAt().setExpirationTime(u()+n).setJti((0,s.v4)()).encrypt(a)}async function f(e){let{token:t,secret:r,salt:n=""}=e;if(!t)return null;let o=await h(r,n),{payload:a}=await (0,i.jwtDecrypt)(t,o,{clockTolerance:15});return a}async function p(e){var t,r,n,o;let{req:i,secureCookie:a=null!=(t=null==(r=process.env.NEXTAUTH_URL)?void 0:r.startsWith("https://"))?t:!!process.env.VERCEL,cookieName:s=a?"__Secure-next-auth.session-token":"next-auth.session-token",raw:l,decode:u=f,logger:d=console,secret:p=null!=(n=process.env.NEXTAUTH_SECRET)?n:process.env.AUTH_SECRET}=e;if(!i)throw Error("Must pass `req` to JWT getToken()");let h=new c.SessionStore({name:s,options:{secure:a}},{cookies:i.cookies,headers:i.headers},d).value,y=i.headers instanceof Headers?i.headers.get("authorization"):null==(o=i.headers)?void 0:o.authorization;if(h||(null==y?void 0:y.split(" ")[0])!=="Bearer"||(h=decodeURIComponent(y.split(" ")[1])),!h)return null;if(l)return h;try{return await u({token:h,secret:p})}catch(e){return null}}async function h(e,t){return await (0,a.default)("sha256",e,t,`NextAuth.js Generated Encryption Key${t?` (${t})`:""}`,32)}},16156:e=>{e.exports=()=>Math.floor(Date.now()/1e3)},16467:(e,t,r)=>{"use strict";function n(e){return{createUser:({id:t,...r})=>e.user.create(o(r)),getUser:t=>e.user.findUnique({where:{id:t}}),getUserByEmail:t=>e.user.findUnique({where:{email:t}}),async getUserByAccount(t){let r=await e.account.findUnique({where:{provider_providerAccountId:t},include:{user:!0}});return r?.user??null},updateUser:({id:t,...r})=>e.user.update({where:{id:t},...o(r)}),deleteUser:t=>e.user.delete({where:{id:t}}),linkAccount:t=>e.account.create({data:t}),unlinkAccount:t=>e.account.delete({where:{provider_providerAccountId:t}}),async getSessionAndUser(t){let r=await e.session.findUnique({where:{sessionToken:t},include:{user:!0}});if(!r)return null;let{user:n,...o}=r;return{user:n,session:o}},createSession:t=>e.session.create(o(t)),updateSession:t=>e.session.update({where:{sessionToken:t.sessionToken},...o(t)}),deleteSession:t=>e.session.delete({where:{sessionToken:t}}),async createVerificationToken(t){let r=await e.verificationToken.create(o(t));return"id"in r&&r.id&&delete r.id,r},async useVerificationToken(t){try{let r=await e.verificationToken.delete({where:{identifier_token:t}});return"id"in r&&r.id&&delete r.id,r}catch(e){if(e&&"object"==typeof e&&"code"in e&&"P2025"===e.code)return null;throw e}},getAccount:async(t,r)=>e.account.findFirst({where:{providerAccountId:t,provider:r}}),createAuthenticator:async t=>e.authenticator.create(o(t)),getAuthenticator:async t=>e.authenticator.findUnique({where:{credentialID:t}}),listAuthenticatorsByUserId:async t=>e.authenticator.findMany({where:{userId:t}}),updateAuthenticatorCounter:async(t,r)=>e.authenticator.update({where:{credentialID:t},data:{counter:r}})}}function o(e){let t={};for(let r in e)void 0!==e[r]&&(t[r]=e[r]);return{data:t}}r.d(t,{y:()=>n})},16851:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t;let r=new URL("http://localhost:3000/api/auth");e&&!e.startsWith("http")&&(e=`https://${e}`);let n=new URL(null!=(t=e)?t:r),o=("/"===n.pathname?r.pathname:n.pathname).replace(/\/$/,""),i=`${n.origin}${o}`;return{origin:n.origin,host:n.host,path:o,base:i,toString:()=>i}}},16974:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){let{url:t,theme:r}=e;return(0,n.h)("div",{className:"verify-request"},r.brandColor&&(0,n.h)("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${r.brandColor}
        }
      `}}),(0,n.h)("div",{className:"card"},r.logo&&(0,n.h)("img",{src:r.logo,alt:"Logo",className:"logo"}),(0,n.h)("h1",null,"Check your email"),(0,n.h)("p",null,"A sign in link has been sent to your email address."),(0,n.h)("p",null,(0,n.h)("a",{className:"site",href:t.origin},t.host))))};var n=r(1441)},17696:e=>{function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},17996:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isCryptoKey=void 0;let n=r(55511),o=r(28354);t.default=n.webcrypto,t.isCryptoKey=o.types.isCryptoKey?e=>o.types.isCryptoKey(e):e=>!1},18040:e=>{function t(r,n,o,i){var a=Object.defineProperty;try{a({},"",{})}catch(e){a=0}e.exports=t=function(e,r,n,o){if(r)a?a(e,r,{value:n,enumerable:!o,configurable:!o,writable:!o}):e[r]=n;else{var i=function(r,n){t(e,r,function(e){return this._invoke(r,n,e)})};i("next",0),i("throw",1),i("return",2)}},e.exports.__esModule=!0,e.exports.default=e.exports,t(r,n,o,i)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},18490:(e,t,r)=>{var n=r(99607)();e.exports=n;try{regeneratorRuntime=n}catch(e){"object"==typeof globalThis?globalThis.regeneratorRuntime=n:Function("r","regeneratorRuntime = r")(n)}},18652:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.calculateJwkThumbprintUri=t.calculateJwkThumbprint=void 0;let n=r(37375),o=r(68327),i=r(99938),a=r(2594),s=r(25110),c=(e,t)=>{if("string"!=typeof e||!e)throw new i.JWKInvalid(`${t} missing or invalid`)};async function l(e,t){let r;if(!(0,s.default)(e))throw TypeError("JWK must be an object");if(null!=t||(t="sha256"),"sha256"!==t&&"sha384"!==t&&"sha512"!==t)throw TypeError('digestAlgorithm must one of "sha256", "sha384", or "sha512"');switch(e.kty){case"EC":c(e.crv,'"crv" (Curve) Parameter'),c(e.x,'"x" (X Coordinate) Parameter'),c(e.y,'"y" (Y Coordinate) Parameter'),r={crv:e.crv,kty:e.kty,x:e.x,y:e.y};break;case"OKP":c(e.crv,'"crv" (Subtype of Key Pair) Parameter'),c(e.x,'"x" (Public Key) Parameter'),r={crv:e.crv,kty:e.kty,x:e.x};break;case"RSA":c(e.e,'"e" (Exponent) Parameter'),c(e.n,'"n" (Modulus) Parameter'),r={e:e.e,kty:e.kty,n:e.n};break;case"oct":c(e.k,'"k" (Key Value) Parameter'),r={k:e.k,kty:e.kty};break;default:throw new i.JOSENotSupported('"kty" (Key Type) Parameter missing or unsupported')}let l=a.encoder.encode(JSON.stringify(r));return(0,o.encode)(await (0,n.default)(t,l))}t.calculateJwkThumbprint=l,t.calculateJwkThumbprintUri=async function(e,t){null!=t||(t="sha256");let r=await l(e,t);return`urn:ietf:params:oauth:jwk-thumbprint:sha-${t.slice(-3)}:${r}`}},19854:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i.default}});var o=r(12269);Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(n,e))&&(e in t&&t[e]===o[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return o[e]}}))});var i=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=a(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&({}).hasOwnProperty.call(e,i)){var s=o?Object.getOwnPropertyDescriptor(e,i):null;s&&(s.get||s.set)?Object.defineProperty(n,i,s):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}(r(35426));function a(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(a=function(e){return e?r:t})(e)}Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(n,e))&&(e in t&&t[e]===i[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return i[e]}}))})},20113:(e,t,r)=>{"use strict";var n=r(9168);Object.defineProperty(t,"__esModule",{value:!0}),t.UnsupportedStrategy=t.UnknownError=t.OAuthCallbackError=t.MissingSecret=t.MissingAuthorize=t.MissingAdapterMethods=t.MissingAdapter=t.MissingAPIRoute=t.InvalidCallbackUrl=t.AccountNotLinkedError=void 0,t.adapterErrorHandler=function(e,t){if(e)return Object.keys(e).reduce(function(r,n){return r[n]=(0,i.default)(o.default.mark(function r(){var i,a,s,c,l,u=arguments;return o.default.wrap(function(r){for(;;)switch(r.prev=r.next){case 0:for(r.prev=0,a=Array(i=u.length),s=0;s<i;s++)a[s]=u[s];return t.debug("adapter_".concat(n),{args:a}),c=e[n],r.next=6,c.apply(void 0,a);case 6:return r.abrupt("return",r.sent);case 9:throw r.prev=9,r.t0=r.catch(0),t.error("adapter_error_".concat(n),r.t0),(l=new h(r.t0)).name="".concat(_(n),"Error"),l;case 15:case"end":return r.stop()}},r,null,[[0,9]])})),r},{})},t.capitalize=_,t.eventsErrorHandler=function(e,t){return Object.keys(e).reduce(function(r,n){return r[n]=(0,i.default)(o.default.mark(function r(){var i,a=arguments;return o.default.wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,i=e[n],r.next=4,i.apply(void 0,a);case 4:return r.abrupt("return",r.sent);case 7:r.prev=7,r.t0=r.catch(0),t.error("".concat(y(n),"_EVENT_ERROR"),r.t0);case 10:case"end":return r.stop()}},r,null,[[0,7]])})),r},{})},t.upperSnake=y;var o=n(r(18490)),i=n(r(48807)),a=n(r(3399)),s=n(r(55437)),c=n(r(24365)),l=n(r(75722)),u=n(r(57502)),d=n(r(70997));function f(e,t,r){return t=(0,u.default)(t),(0,l.default)(e,p()?Reflect.construct(t,r||[],(0,u.default)(e).constructor):t.apply(e,r))}function p(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(p=function(){return!!e})()}var h=t.UnknownError=function(e){function t(e){var r,n;return(0,s.default)(this,t),(n=f(this,t,[null!=(r=null==e?void 0:e.message)?r:e])).name="UnknownError",n.code=e.code,e instanceof Error&&(n.stack=e.stack),n}return(0,d.default)(t,e),(0,c.default)(t,[{key:"toJSON",value:function(){return{name:this.name,message:this.message,stack:this.stack}}}])}((0,n(r(767)).default)(Error));function y(e){return e.replace(/([A-Z])/g,"_$1").toUpperCase()}function _(e){return"".concat(e[0].toUpperCase()).concat(e.slice(1))}t.OAuthCallbackError=function(e){function t(){var e;(0,s.default)(this,t);for(var r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];return e=f(this,t,[].concat(n)),(0,a.default)(e,"name","OAuthCallbackError"),e}return(0,d.default)(t,e),(0,c.default)(t)}(h),t.AccountNotLinkedError=function(e){function t(){var e;(0,s.default)(this,t);for(var r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];return e=f(this,t,[].concat(n)),(0,a.default)(e,"name","AccountNotLinkedError"),e}return(0,d.default)(t,e),(0,c.default)(t)}(h),t.MissingAPIRoute=function(e){function t(){var e;(0,s.default)(this,t);for(var r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];return e=f(this,t,[].concat(n)),(0,a.default)(e,"name","MissingAPIRouteError"),(0,a.default)(e,"code","MISSING_NEXTAUTH_API_ROUTE_ERROR"),e}return(0,d.default)(t,e),(0,c.default)(t)}(h),t.MissingSecret=function(e){function t(){var e;(0,s.default)(this,t);for(var r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];return e=f(this,t,[].concat(n)),(0,a.default)(e,"name","MissingSecretError"),(0,a.default)(e,"code","NO_SECRET"),e}return(0,d.default)(t,e),(0,c.default)(t)}(h),t.MissingAuthorize=function(e){function t(){var e;(0,s.default)(this,t);for(var r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];return e=f(this,t,[].concat(n)),(0,a.default)(e,"name","MissingAuthorizeError"),(0,a.default)(e,"code","CALLBACK_CREDENTIALS_HANDLER_ERROR"),e}return(0,d.default)(t,e),(0,c.default)(t)}(h),t.MissingAdapter=function(e){function t(){var e;(0,s.default)(this,t);for(var r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];return e=f(this,t,[].concat(n)),(0,a.default)(e,"name","MissingAdapterError"),(0,a.default)(e,"code","EMAIL_REQUIRES_ADAPTER_ERROR"),e}return(0,d.default)(t,e),(0,c.default)(t)}(h),t.MissingAdapterMethods=function(e){function t(){var e;(0,s.default)(this,t);for(var r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];return e=f(this,t,[].concat(n)),(0,a.default)(e,"name","MissingAdapterMethodsError"),(0,a.default)(e,"code","MISSING_ADAPTER_METHODS_ERROR"),e}return(0,d.default)(t,e),(0,c.default)(t)}(h),t.UnsupportedStrategy=function(e){function t(){var e;(0,s.default)(this,t);for(var r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];return e=f(this,t,[].concat(n)),(0,a.default)(e,"name","UnsupportedStrategyError"),(0,a.default)(e,"code","CALLBACK_CREDENTIALS_JWT_ERROR"),e}return(0,d.default)(t,e),(0,c.default)(t)}(h),t.InvalidCallbackUrl=function(e){function t(){var e;(0,s.default)(this,t);for(var r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];return e=f(this,t,[].concat(n)),(0,a.default)(e,"name","InvalidCallbackUrl"),(0,a.default)(e,"code","INVALID_CALLBACK_URL_ERROR"),e}return(0,d.default)(t,e),(0,c.default)(t)}(h)},21606:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=r(55511).timingSafeEqual},22122:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(55511),o=r(42891),i=r(51800),a=r(2594),s=r(34010),c=r(17996),l=r(50003),u=r(90061),d=r(78680),f=r(99938),p=r(79963),h=r(37265);t.default=(e,t,r,y,_)=>{let g;if((0,c.isCryptoKey)(r))(0,l.checkEncCryptoKey)(r,e,"encrypt"),g=n.KeyObject.from(r);else if(r instanceof Uint8Array||(0,u.default)(r))g=r;else throw TypeError((0,d.default)(r,...h.types,"Uint8Array"));switch((0,i.default)(e,g),(0,o.default)(e,y),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return function(e,t,r,o,i){let c=parseInt(e.slice(1,4),10);(0,u.default)(r)&&(r=r.export());let l=r.subarray(c>>3),d=r.subarray(0,c>>3),h=`aes-${c}-cbc`;if(!(0,p.default)(h))throw new f.JOSENotSupported(`alg ${e} is not supported by your javascript runtime`);let y=(0,n.createCipheriv)(h,l,o),_=(0,a.concat)(y.update(t),y.final()),g=parseInt(e.slice(-3),10),b=(0,s.default)(i,o,_,g,d,c);return{ciphertext:_,tag:b}}(e,t,g,y,_);case"A128GCM":case"A192GCM":case"A256GCM":return function(e,t,r,o,i){let a=parseInt(e.slice(1,4),10),s=`aes-${a}-gcm`;if(!(0,p.default)(s))throw new f.JOSENotSupported(`alg ${e} is not supported by your javascript runtime`);let c=(0,n.createCipheriv)(s,r,o,{authTagLength:16});i.byteLength&&c.setAAD(i,{plaintextLength:t.length});let l=c.update(t);return c.final(),{ciphertext:l,tag:c.getAuthTag()}}(e,t,g,y,_);default:throw new f.JOSENotSupported("Unsupported JWE Content Encryption Algorithm")}}},22165:e=>{e.exports.isAnEarlyCloseHost=function(e){return e&&e.match(".*google(apis)?.com$")}},22429:(e,t,r)=>{"use strict";var n=r(9168);Object.defineProperty(t,"__esModule",{value:!0}),t.default=s;var o=n(r(66837)),i=n(r(58985)),a=n(r(11714));async function s(e){let{options:t,query:r,body:n}=e,{url:s,callbacks:c,logger:l,provider:u}=t;if(!u.type)return{status:500,text:`Error: Type not specified for ${u.name}`};if("oauth"===u.type)try{return await (0,o.default)({options:t,query:r})}catch(e){return l.error("SIGNIN_OAUTH_ERROR",{error:e,providerId:u.id}),{redirect:`${s}/error?error=OAuthSignin`}}if("email"===u.type){var d;let e=null==n?void 0:n.email;if(!e)return{redirect:`${s}/error?error=EmailSignin`};let r=null!=(d=u.normalizeIdentifier)?d:e=>{let[t,r]=e.toLowerCase().trim().split("@");return r=r.split(",")[0],`${t}@${r}`};try{e=r(null==n?void 0:n.email)}catch(e){return l.error("SIGNIN_EMAIL_ERROR",{error:e,providerId:u.id}),{redirect:`${s}/error?error=EmailSignin`}}let o=await (0,a.default)({email:e,adapter:t.adapter}),f={providerAccountId:e,userId:e,type:"email",provider:u.id};try{let e=await c.signIn({user:o,account:f,email:{verificationRequest:!0}});if(!e)return{redirect:`${s}/error?error=AccessDenied`};if("string"==typeof e)return{redirect:e}}catch(e){return{redirect:`${s}/error?${new URLSearchParams({error:e})}`}}try{return{redirect:await (0,i.default)(e,t)}}catch(e){return l.error("SIGNIN_EMAIL_ERROR",{error:e,providerId:u.id}),{redirect:`${s}/error?error=EmailSignin`}}}return{redirect:`${s}/signin`}}},22599:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(88062),o=r(52131),i=r(62197),a=r(56913),s=r(68327),c=r(61689),l=r(99938),u=r(65764),d=r(80335),f=r(97528);t.default=async function(e,t,r,p,h={}){let y,_,g;switch((0,d.default)(e,r,"encrypt"),e){case"dir":g=r;break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{if(!o.ecdhAllowed(r))throw new l.JOSENotSupported("ECDH with the provided key is not allowed or not supported by your javascript runtime");let{apu:i,apv:a}=h,{epk:d}=h;d||(d=(await o.generateEpk(r)).privateKey);let{x:f,y:b,crv:m,kty:v}=await (0,u.exportJWK)(d),w=await o.deriveKey(r,d,"ECDH-ES"===e?t:e,"ECDH-ES"===e?(0,c.bitLength)(t):parseInt(e.slice(-5,-2),10),i,a);if(_={epk:{x:f,crv:m,kty:v}},"EC"===v&&(_.epk.y=b),i&&(_.apu=(0,s.encode)(i)),a&&(_.apv=(0,s.encode)(a)),"ECDH-ES"===e){g=w;break}g=p||(0,c.default)(t);let x=e.slice(-6);y=await (0,n.wrap)(x,w,g);break}case"RSA1_5":case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":g=p||(0,c.default)(t),y=await (0,a.encrypt)(e,r,g);break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{g=p||(0,c.default)(t);let{p2c:n,p2s:o}=h;({encryptedKey:y,..._}=await (0,i.encrypt)(e,r,g,n,o));break}case"A128KW":case"A192KW":case"A256KW":g=p||(0,c.default)(t),y=await (0,n.wrap)(e,r,g);break;case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{g=p||(0,c.default)(t);let{iv:n}=h;({encryptedKey:y,..._}=await (0,f.wrap)(e,r,g,n));break}default:throw new l.JOSENotSupported('Invalid or unsupported "alg" (JWE Algorithm) header value')}return{cek:g,encryptedKey:y,parameters:_}}},23884:(e,t,r)=>{let{inspect:n}=r(28354),o=r(79551),{RPError:i}=r(61408),a=r(83872),s=r(10028),c=r(48160),l=r(64150),u=r(9552),d=r(93929),{keystore:f}=r(73602),p=["https://login.microsoftonline.com/common/.well-known/openid-configuration","https://login.microsoftonline.com/common/v2.0/.well-known/openid-configuration","https://login.microsoftonline.com/organizations/v2.0/.well-known/openid-configuration","https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration"],h=Symbol(),y={claim_types_supported:["normal"],claims_parameter_supported:!1,grant_types_supported:["authorization_code","implicit"],request_parameter_supported:!1,request_uri_parameter_supported:!0,require_request_uri_registration:!1,response_modes_supported:["query","fragment"],token_endpoint_auth_methods_supported:["client_secret_basic"]};class _{#e;constructor(e={}){let t=e[h];delete e[h],["introspection","revocation"].forEach(t=>{e[`${t}_endpoint`]&&void 0===e[`${t}_endpoint_auth_methods_supported`]&&void 0===e[`${t}_endpoint_auth_signing_alg_values_supported`]&&(e.token_endpoint_auth_methods_supported&&(e[`${t}_endpoint_auth_methods_supported`]=e.token_endpoint_auth_methods_supported),e.token_endpoint_auth_signing_alg_values_supported&&(e[`${t}_endpoint_auth_signing_alg_values_supported`]=e.token_endpoint_auth_signing_alg_values_supported))}),this.#e=new Map,Object.entries(e).forEach(([e,t])=>{this.#e.set(e,t),this[e]||Object.defineProperty(this,e,{get(){return this.#e.get(e)},enumerable:!0})}),s.set(this.issuer,this);let r=a(this,t);Object.defineProperties(this,{Client:{value:r,enumerable:!0},FAPI1Client:{value:class extends r{},enumerable:!0},FAPI2Client:{value:class extends r{},enumerable:!0}})}get metadata(){return d(Object.fromEntries(this.#e.entries()))}static async webfinger(e){let t=l(e),{host:r}=o.parse(t),n=`https://${r}/.well-known/webfinger`,a=c(await u.call(this,{method:"GET",url:n,responseType:"json",searchParams:{resource:t,rel:"http://openid.net/specs/connect/1.0/issuer"},headers:{Accept:"application/json"}})),d=Array.isArray(a.links)&&a.links.find(e=>"object"==typeof e&&"http://openid.net/specs/connect/1.0/issuer"===e.rel&&e.href);if(!d)throw new i({message:"no issuer found in webfinger response",body:a});if("string"!=typeof d.href||!d.href.startsWith("https://"))throw new i({printf:["invalid issuer location %s",d.href],body:a});let f=d.href;if(s.has(f))return s.get(f);let p=await this.discover(f);if(p.issuer!==f)throw s.del(p.issuer),new i("discovered issuer mismatch, expected %s, got: %s",f,p.issuer);return p}static async discover(e){let t=function(e){let t=o.parse(e);if(t.pathname.includes("/.well-known/"))return e;{let e;return e=t.pathname.endsWith("/")?`${t.pathname}.well-known/openid-configuration`:`${t.pathname}/.well-known/openid-configuration`,o.format({...t,pathname:e})}}(e),r=c(await u.call(this,{method:"GET",responseType:"json",url:t,headers:{Accept:"application/json"}}));return new _({...y,...r,[h]:!!p.find(e=>t.startsWith(e))})}async reloadJwksUri(){await f.call(this,!0)}[n.custom](){return`${this.constructor.name} ${n(this.metadata,{depth:1/0,colors:process.stdout.isTTY,compact:!1,sorted:!0})}`}}e.exports=_},24365:(e,t,r)=>{var n=r(3038);function o(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,n(o.key),o)}}e.exports=function(e,t,r){return t&&o(e.prototype,t),r&&o(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports.default=e.exports},24453:(e,t,r)=>{let n=r(70885),o=r(16156);class i{constructor(e){Object.assign(this,e);let{constructor:t,...r}=Object.getOwnPropertyDescriptors(this.constructor.prototype);Object.defineProperties(this,r)}set expires_in(e){this.expires_at=o()+Number(e)}get expires_in(){return Math.max.apply(null,[this.expires_at-o(),0])}expired(){return 0===this.expires_in}claims(){if(!this.id_token)throw TypeError("id_token not present in TokenSet");return JSON.parse(n.decode(this.id_token.split(".")[1]))}}e.exports=i},24575:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.generalVerify=void 0;let n=r(71746),o=r(99938),i=r(25110);t.generalVerify=async function(e,t,r){if(!(0,i.default)(e))throw new o.JWSInvalid("General JWS must be an object");if(!Array.isArray(e.signatures)||!e.signatures.every(i.default))throw new o.JWSInvalid("JWS Signatures missing or incorrect type");for(let o of e.signatures)try{return await (0,n.flattenedVerify)({header:o.header,payload:e.payload,protected:o.protected,signature:o.signature},t,r)}catch{}throw new o.JWSSignatureVerificationFailed}},24740:e=>{e.exports=function(e,...t){let r={};for(let n of t)void 0!==e[n]&&(r[n]=e[n]);return r}},24956:e=>{function t(r){return e.exports=t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,t(r)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},25110:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){if("object"!=typeof e||null===e||"[object Object]"!==Object.prototype.toString.call(e))return!1;if(null===Object.getPrototypeOf(e))return!0;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}},25538:(e,t)=>{"use strict";t.parse=function(e,t){if("string"!=typeof e)throw TypeError("argument str must be a string");var r={},o=e.length;if(o<2)return r;var i=t&&t.decode||u,a=0,s=0,d=0;do{if(-1===(s=e.indexOf("=",a)))break;if(-1===(d=e.indexOf(";",a)))d=o;else if(s>d){a=e.lastIndexOf(";",s-1)+1;continue}var f=c(e,a,s),p=l(e,s,f),h=e.slice(f,p);if(!n.call(r,h)){var y=c(e,s+1,d),_=l(e,d,y);34===e.charCodeAt(y)&&34===e.charCodeAt(_-1)&&(y++,_--);var g=e.slice(y,_);r[h]=function(e,t){try{return t(e)}catch(t){return e}}(g,i)}a=d+1}while(a<o);return r},t.serialize=function(e,t,n){var c=n&&n.encode||encodeURIComponent;if("function"!=typeof c)throw TypeError("option encode is invalid");if(!o.test(e))throw TypeError("argument name is invalid");var l=c(t);if(!i.test(l))throw TypeError("argument val is invalid");var u=e+"="+l;if(!n)return u;if(null!=n.maxAge){var d=Math.floor(n.maxAge);if(!isFinite(d))throw TypeError("option maxAge is invalid");u+="; Max-Age="+d}if(n.domain){if(!a.test(n.domain))throw TypeError("option domain is invalid");u+="; Domain="+n.domain}if(n.path){if(!s.test(n.path))throw TypeError("option path is invalid");u+="; Path="+n.path}if(n.expires){var f,p=n.expires;if(f=p,"[object Date]"!==r.call(f)||isNaN(p.valueOf()))throw TypeError("option expires is invalid");u+="; Expires="+p.toUTCString()}if(n.httpOnly&&(u+="; HttpOnly"),n.secure&&(u+="; Secure"),n.partitioned&&(u+="; Partitioned"),n.priority)switch("string"==typeof n.priority?n.priority.toLowerCase():n.priority){case"low":u+="; Priority=Low";break;case"medium":u+="; Priority=Medium";break;case"high":u+="; Priority=High";break;default:throw TypeError("option priority is invalid")}if(n.sameSite)switch("string"==typeof n.sameSite?n.sameSite.toLowerCase():n.sameSite){case!0:case"strict":u+="; SameSite=Strict";break;case"lax":u+="; SameSite=Lax";break;case"none":u+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return u};var r=Object.prototype.toString,n=Object.prototype.hasOwnProperty,o=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/,i=/^("?)[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]*\1$/,a=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,s=/^[\u0020-\u003A\u003D-\u007E]*$/;function c(e,t,r){do{var n=e.charCodeAt(t);if(32!==n&&9!==n)return t}while(++t<r);return r}function l(e,t,r){for(;t>r;){var n=e.charCodeAt(--t);if(32!==n&&9!==n)return t+1}return r}function u(e){return -1!==e.indexOf("%")?decodeURIComponent(e):e}},27969:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t;let{url:r,error:o="default",theme:i}=e,a=`${r}/signin`,s={default:{status:200,heading:"Error",message:(0,n.h)("p",null,(0,n.h)("a",{className:"site",href:null==r?void 0:r.origin},null==r?void 0:r.host))},configuration:{status:500,heading:"Server error",message:(0,n.h)("div",null,(0,n.h)("p",null,"There is a problem with the server configuration."),(0,n.h)("p",null,"Check the server logs for more information."))},accessdenied:{status:403,heading:"Access Denied",message:(0,n.h)("div",null,(0,n.h)("p",null,"You do not have permission to sign in."),(0,n.h)("p",null,(0,n.h)("a",{className:"button",href:a},"Sign in")))},verification:{status:403,heading:"Unable to sign in",message:(0,n.h)("div",null,(0,n.h)("p",null,"The sign in link is no longer valid."),(0,n.h)("p",null,"It may have been used already or it may have expired.")),signin:(0,n.h)("a",{className:"button",href:a},"Sign in")}},{status:c,heading:l,message:u,signin:d}=null!=(t=s[o.toLowerCase()])?t:s.default;return{status:c,html:(0,n.h)("div",{className:"error"},(null==i?void 0:i.brandColor)&&(0,n.h)("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${null==i?void 0:i.brandColor}
        }
      `}}),(0,n.h)("div",{className:"card"},(null==i?void 0:i.logo)&&(0,n.h)("img",{src:i.logo,alt:"Logo",className:"logo"}),(0,n.h)("h1",null,l),(0,n.h)("div",{className:"message"},u),d))}};var n=r(1441)},28247:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.jwtDecrypt=void 0;let n=r(81454),o=r(81822),i=r(99938);t.jwtDecrypt=async function(e,t,r){let a=await (0,n.compactDecrypt)(e,t,r),s=(0,o.default)(a.protectedHeader,a.plaintext,r),{protectedHeader:c}=a;if(void 0!==c.iss&&c.iss!==s.iss)throw new i.JWTClaimValidationFailed('replicated "iss" claim header parameter mismatch',"iss","mismatch");if(void 0!==c.sub&&c.sub!==s.sub)throw new i.JWTClaimValidationFailed('replicated "sub" claim header parameter mismatch',"sub","mismatch");if(void 0!==c.aud&&JSON.stringify(c.aud)!==JSON.stringify(s.aud))throw new i.JWTClaimValidationFailed('replicated "aud" claim header parameter mismatch',"aud","mismatch");let l={payload:s,protectedHeader:c};return"function"==typeof t?{...l,key:a.key}:l}},28328:(e,t,r)=>{var n=r(55511),o=r(6702),i=r(81630),a=r(55591),s=r(79551),c=r(11723),l=r(22165);t.OAuth=function(e,t,r,n,o,i,a,s,c){if(this._isEcho=!1,this._requestUrl=e,this._accessUrl=t,this._consumerKey=r,this._consumerSecret=this._encodeData(n),"RSA-SHA1"==a&&(this._privateKey=n),this._version=o,void 0===i?this._authorize_callback="oob":this._authorize_callback=i,"PLAINTEXT"!=a&&"HMAC-SHA1"!=a&&"RSA-SHA1"!=a)throw Error("Un-supported signature method: "+a);this._signatureMethod=a,this._nonceSize=s||32,this._headers=c||{Accept:"*/*",Connection:"close","User-Agent":"Node authentication"},this._clientOptions=this._defaultClientOptions={requestTokenHttpMethod:"POST",accessTokenHttpMethod:"POST",followRedirects:!0},this._oauthParameterSeperator=","},t.OAuthEcho=function(e,t,r,n,o,i,a,s){if(this._isEcho=!0,this._realm=e,this._verifyCredentials=t,this._consumerKey=r,this._consumerSecret=this._encodeData(n),"RSA-SHA1"==i&&(this._privateKey=n),this._version=o,"PLAINTEXT"!=i&&"HMAC-SHA1"!=i&&"RSA-SHA1"!=i)throw Error("Un-supported signature method: "+i);this._signatureMethod=i,this._nonceSize=a||32,this._headers=s||{Accept:"*/*",Connection:"close","User-Agent":"Node authentication"},this._oauthParameterSeperator=","},t.OAuthEcho.prototype=t.OAuth.prototype,t.OAuth.prototype._getTimestamp=function(){return Math.floor(new Date().getTime()/1e3)},t.OAuth.prototype._encodeData=function(e){return null==e||""==e?"":encodeURIComponent(e).replace(/\!/g,"%21").replace(/\'/g,"%27").replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/\*/g,"%2A")},t.OAuth.prototype._decodeData=function(e){return null!=e&&(e=e.replace(/\+/g," ")),decodeURIComponent(e)},t.OAuth.prototype._getSignature=function(e,t,r,n){var o=this._createSignatureBase(e,t,r);return this._createSignature(o,n)},t.OAuth.prototype._normalizeUrl=function(e){var t=s.parse(e,!0),r="";return t.port&&("http:"==t.protocol&&"80"!=t.port||"https:"==t.protocol&&"443"!=t.port)&&(r=":"+t.port),t.pathname&&""!=t.pathname||(t.pathname="/"),t.protocol+"//"+t.hostname+r+t.pathname},t.OAuth.prototype._isParameterNameAnOAuthParameter=function(e){var t=e.match("^oauth_");return!!t&&"oauth_"===t[0]},t.OAuth.prototype._buildAuthorizationHeaders=function(e){var t="OAuth ";this._isEcho&&(t+='realm="'+this._realm+'",');for(var r=0;r<e.length;r++)this._isParameterNameAnOAuthParameter(e[r][0])&&(t+=""+this._encodeData(e[r][0])+'="'+this._encodeData(e[r][1])+'"'+this._oauthParameterSeperator);return t.substring(0,t.length-this._oauthParameterSeperator.length)},t.OAuth.prototype._makeArrayOfArgumentsHash=function(e){var t=[];for(var r in e)if(e.hasOwnProperty(r)){var n=e[r];if(Array.isArray(n))for(var o=0;o<n.length;o++)t[t.length]=[r,n[o]];else t[t.length]=[r,n]}return t},t.OAuth.prototype._sortRequestParams=function(e){return e.sort(function(e,t){return e[0]==t[0]?e[1]<t[1]?-1:1:e[0]<t[0]?-1:1}),e},t.OAuth.prototype._normaliseRequestParams=function(e){for(var t=this._makeArrayOfArgumentsHash(e),r=0;r<t.length;r++)t[r][0]=this._encodeData(t[r][0]),t[r][1]=this._encodeData(t[r][1]);t=this._sortRequestParams(t);for(var e="",r=0;r<t.length;r++)e+=t[r][0],e+="=",e+=t[r][1],r<t.length-1&&(e+="&");return e},t.OAuth.prototype._createSignatureBase=function(e,t,r){return t=this._encodeData(this._normalizeUrl(t)),r=this._encodeData(r),e.toUpperCase()+"&"+t+"&"+r},t.OAuth.prototype._createSignature=function(e,t){if(void 0===t)var t="";else t=this._encodeData(t);var r=this._consumerSecret+"&"+t,i="";return"PLAINTEXT"==this._signatureMethod?i=r:"RSA-SHA1"==this._signatureMethod?(r=this._privateKey||"",i=n.createSign("RSA-SHA1").update(e).sign(r,"base64")):i=n.Hmac?n.createHmac("sha1",r).update(e).digest("base64"):o.HMACSHA1(r,e),i},t.OAuth.prototype.NONCE_CHARS=["a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","0","1","2","3","4","5","6","7","8","9"],t.OAuth.prototype._getNonce=function(e){for(var t,r=[],n=this.NONCE_CHARS,o=n.length,i=0;i<e;i++)t=Math.floor(Math.random()*o),r[i]=n[t];return r.join("")},t.OAuth.prototype._createClient=function(e,t,r,n,o,s){return(s?a:i).request({host:t,port:e,path:n,method:r,headers:o})},t.OAuth.prototype._prepareParameters=function(e,t,r,n,o){var i={oauth_timestamp:this._getTimestamp(),oauth_nonce:this._getNonce(this._nonceSize),oauth_version:this._version,oauth_signature_method:this._signatureMethod,oauth_consumer_key:this._consumerKey};if(e&&(i.oauth_token=e),this._isEcho)u=this._getSignature("GET",this._verifyCredentials,this._normaliseRequestParams(i),t);else{if(o)for(var a in o)o.hasOwnProperty(a)&&(i[a]=o[a]);var l=s.parse(n,!1);if(l.query){var u,d,f=c.parse(l.query);for(var a in f){var p=f[a];if("object"==typeof p)for(d in p)i[a+"["+d+"]"]=p[d];else i[a]=p}}u=this._getSignature(r,n,this._normaliseRequestParams(i),t)}var h=this._sortRequestParams(this._makeArrayOfArgumentsHash(i));return h[h.length]=["oauth_signature",u],h},t.OAuth.prototype._performSecureRequest=function(e,t,r,n,o,i,a,u){var d,f,p=this._prepareParameters(e,t,r,n,o);a||(a="application/x-www-form-urlencoded");var h=s.parse(n,!1);"http:"!=h.protocol||h.port||(h.port=80),"https:"!=h.protocol||h.port||(h.port=443);var y={},_=this._buildAuthorizationHeaders(p);for(var g in this._isEcho?y["X-Verify-Credentials-Authorization"]=_:y.Authorization=_,y.Host=h.host,this._headers)this._headers.hasOwnProperty(g)&&(y[g]=this._headers[g]);for(var g in o)this._isParameterNameAnOAuthParameter(g)&&delete o[g];("POST"==r||"PUT"==r)&&null==i&&null!=o&&(i=c.stringify(o).replace(/\!/g,"%21").replace(/\'/g,"%27").replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/\*/g,"%2A")),i?Buffer.isBuffer(i)?y["Content-length"]=i.length:y["Content-length"]=Buffer.byteLength(i):y["Content-length"]=0,y["Content-Type"]=a,h.pathname&&""!=h.pathname||(h.pathname="/"),d=h.query?h.pathname+"?"+h.query:h.pathname,f="https:"==h.protocol?this._createClient(h.port,h.hostname,r,d,y,!0):this._createClient(h.port,h.hostname,r,d,y);var b=this._clientOptions;if(!u)return("POST"==r||"PUT"==r)&&null!=i&&""!=i&&f.write(i),f;var m="",v=this,w=l.isAnEarlyCloseHost(h.hostname),x=!1,k=function(n){x||(x=!0,n.statusCode>=200&&n.statusCode<=299?u(null,m,n):(301==n.statusCode||302==n.statusCode)&&b.followRedirects&&n.headers&&n.headers.location?v._performSecureRequest(e,t,r,n.headers.location,o,i,a,u):u({statusCode:n.statusCode,data:m},m,n))};f.on("response",function(e){e.setEncoding("utf8"),e.on("data",function(e){m+=e}),e.on("end",function(){k(e)}),e.on("close",function(){w&&k(e)})}),f.on("error",function(e){x||(x=!0,u(e))}),("POST"==r||"PUT"==r)&&null!=i&&""!=i&&f.write(i),f.end()},t.OAuth.prototype.setClientOptions=function(e){var t,r={},n=Object.prototype.hasOwnProperty;for(t in this._defaultClientOptions)n.call(e,t)?r[t]=e[t]:r[t]=this._defaultClientOptions[t];this._clientOptions=r},t.OAuth.prototype.getOAuthAccessToken=function(e,t,r,n){var o={};"function"==typeof r?n=r:o.oauth_verifier=r,this._performSecureRequest(e,t,this._clientOptions.accessTokenHttpMethod,this._accessUrl,o,null,null,function(e,t,r){if(e)n(e);else{var o=c.parse(t),i=o.oauth_token;delete o.oauth_token;var a=o.oauth_token_secret;delete o.oauth_token_secret,n(null,i,a,o)}})},t.OAuth.prototype.getProtectedResource=function(e,t,r,n,o){this._performSecureRequest(r,n,t,e,null,"",null,o)},t.OAuth.prototype.delete=function(e,t,r,n){return this._performSecureRequest(t,r,"DELETE",e,null,"",null,n)},t.OAuth.prototype.get=function(e,t,r,n){return this._performSecureRequest(t,r,"GET",e,null,"",null,n)},t.OAuth.prototype._putOrPost=function(e,t,r,n,o,i,a){var s=null;return"function"==typeof i&&(a=i,i=null),"string"==typeof o||Buffer.isBuffer(o)||(i="application/x-www-form-urlencoded",s=o,o=null),this._performSecureRequest(r,n,e,t,s,o,i,a)},t.OAuth.prototype.put=function(e,t,r,n,o,i){return this._putOrPost("PUT",e,t,r,n,o,i)},t.OAuth.prototype.post=function(e,t,r,n,o,i){return this._putOrPost("POST",e,t,r,n,o,i)},t.OAuth.prototype.getOAuthRequestToken=function(e,t){"function"==typeof e&&(t=e,e={}),this._authorize_callback&&(e.oauth_callback=this._authorize_callback),this._performSecureRequest(null,null,this._clientOptions.requestTokenHttpMethod,this._requestUrl,e,null,null,function(e,r,n){if(e)t(e);else{var o=c.parse(r),i=o.oauth_token,a=o.oauth_token_secret;delete o.oauth_token,delete o.oauth_token_secret,t(null,i,a,o)}})},t.OAuth.prototype.signUrl=function(e,t,r,n){if(void 0===n)var n="GET";for(var o=this._prepareParameters(t,r,n,e,{}),i=s.parse(e,!1),a="",c=0;c<o.length;c++)a+=o[c][0]+"="+this._encodeData(o[c][1])+"&";return a=a.substring(0,a.length-1),i.protocol+"//"+i.host+i.pathname+"?"+a},t.OAuth.prototype.authHeader=function(e,t,r,n){if(void 0===n)var n="GET";var o=this._prepareParameters(t,r,n,e,{});return this._buildAuthorizationHeaders(o)}},28339:(e,t,r)=>{var n=r(18040);function o(){var t,r,i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",s=i.toStringTag||"@@toStringTag";function c(e,o,i,a){var s=Object.create((o&&o.prototype instanceof u?o:u).prototype);return n(s,"_invoke",function(e,n,o){var i,a,s,c=0,u=o||[],d=!1,f={p:0,n:0,v:t,a:p,f:p.bind(t,4),d:function(e,r){return i=e,a=0,s=t,f.n=r,l}};function p(e,n){for(a=e,s=n,r=0;!d&&c&&!o&&r<u.length;r++){var o,i=u[r],p=f.p,h=i[2];e>3?(o=h===n)&&(s=i[(a=i[4])?5:(a=3,3)],i[4]=i[5]=t):i[0]<=p&&((o=e<2&&p<i[1])?(a=0,f.v=n,f.n=i[1]):p<h&&(o=e<3||i[0]>n||n>h)&&(i[4]=e,i[5]=n,f.n=h,a=0))}if(o||e>1)return l;throw d=!0,n}return function(o,u,h){if(c>1)throw TypeError("Generator is already running");for(d&&1===u&&p(u,h),a=u,s=h;(r=a<2?t:s)||!d;){i||(a?a<3?(a>1&&(f.n=-1),p(a,s)):f.n=s:f.v=s);try{if(c=2,i){if(a||(o="next"),r=i[o]){if(!(r=r.call(i,s)))throw TypeError("iterator result is not an object");if(!r.done)return r;s=r.value,a<2&&(a=0)}else 1===a&&(r=i.return)&&r.call(i),a<2&&(s=TypeError("The iterator does not provide a '"+o+"' method"),a=1);i=t}else if((r=(d=f.n<0)?s:e.call(n,f))!==l)break}catch(e){i=t,a=1,s=e}finally{c=1}}return{value:r,done:d}}}(e,i,a),!0),s}var l={};function u(){}function d(){}function f(){}r=Object.getPrototypeOf;var p=f.prototype=u.prototype=Object.create([][a]?r(r([][a]())):(n(r={},a,function(){return this}),r));function h(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,n(e,s,"GeneratorFunction")),e.prototype=Object.create(p),e}return d.prototype=f,n(p,"constructor",f),n(f,"constructor",d),d.displayName="GeneratorFunction",n(f,s,"GeneratorFunction"),n(p),n(p,s,"Generator"),n(p,a,function(){return this}),n(p,"toString",function(){return"[object Generator]"}),(e.exports=o=function(){return{w:c,m:h}},e.exports.__esModule=!0,e.exports.default=e.exports)()}e.exports=o,e.exports.__esModule=!0,e.exports.default=e.exports},28510:(e,t,r)=>{"use strict";var n=r(9168);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,t.proxyLogger=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:u,t=arguments.length>1?arguments[1]:void 0;try{if("undefined"==typeof window)return e;var r={},n=function(e){var n;r[e]=(n=(0,a.default)(o.default.mark(function r(n,a){var s,d;return o.default.wrap(function(r){for(;;)switch(r.prev=r.next){case 0:if(u[e](n,a),"error"===e&&(a=l(a)),a.client=!0,s="".concat(t,"/_log"),d=new URLSearchParams(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach(function(t){(0,i.default)(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({level:e,code:n},a)),!navigator.sendBeacon){r.next=8;break}return r.abrupt("return",navigator.sendBeacon(s,d));case 8:return r.next=10,fetch(s,{method:"POST",body:d,keepalive:!0});case 10:return r.abrupt("return",r.sent);case 11:case"end":return r.stop()}},r)})),function(e,t){return n.apply(this,arguments)})};for(var s in e)n(s);return r}catch(e){return u}},t.setLogger=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;t||(u.debug=function(){}),e.error&&(u.error=e.error),e.warn&&(u.warn=e.warn),e.debug&&(u.debug=e.debug)};var o=n(r(18490)),i=n(r(3399)),a=n(r(48807)),s=r(20113);function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function l(e){var t,r;if(e instanceof Error&&!(e instanceof s.UnknownError))return{message:e.message,stack:e.stack,name:e.name};if(null!=(t=e)&&t.error){e.error=l(e.error),e.message=null!=(r=e.message)?r:e.error.message}return e}var u={error:function(e,t){t=l(t),console.error("[next-auth][error][".concat(e,"]"),"\nhttps://next-auth.js.org/errors#".concat(e.toLowerCase()),t.message,t)},warn:function(e){console.warn("[next-auth][warn][".concat(e,"]"),"\nhttps://next-auth.js.org/warnings#".concat(e.toLowerCase()))},debug:function(e,t){console.log("[next-auth][debug][".concat(e,"]"),t)}};t.default=u},28577:(e,t,r)=>{let n=r(37544),o=r(93929),i=r(78830),a=Symbol(),s=(e,{alg:t,use:r})=>{let n=0;return t&&e.alg&&n++,r&&e.use&&n++,n};e.exports=class{#t;constructor(e,t){if(e!==a)throw Error("invalid constructor call");this.#t=t}toJWKS(){return{keys:this.map(({jwk:{d:e,p:t,q:r,dp:n,dq:o,qi:i,...a}})=>a)}}all({alg:e,kid:t,use:r}={}){if(!r||!e)throw Error();let n=function(e){switch("string"==typeof e&&e.slice(0,2)){case"RS":case"PS":return"RSA";case"ES":return"EC";case"Ed":return"OKP";default:return}}(e),o={alg:e,use:r};return this.filter(o=>{let i=!0;return void 0!==n&&o.jwk.kty!==n&&(i=!1),i&&void 0!==t&&o.jwk.kid!==t&&(i=!1),i&&void 0!==r&&void 0!==o.jwk.use&&o.jwk.use!==r&&(i=!1),i&&o.jwk.alg&&o.jwk.alg!==e?i=!1:o.algorithms.has(e)||(i=!1),i}).sort((e,t)=>s(t,o)-s(e,o))}get(...e){return this.all(...e)[0]}static async fromJWKS(e,{onlyPublic:t=!1,onlyPrivate:r=!1}={}){if(!i(e)||!Array.isArray(e.keys)||e.keys.some(e=>!i(e)||!("kty"in e)))throw TypeError("jwks must be a JSON Web Key Set formatted object");let s=[];for(let i of e.keys){let{kty:e,kid:a,crv:c}=i=o(i),{alg:l,use:u}=i;if("string"==typeof e&&e&&(void 0===u||"sig"===u||"enc"===u)&&("string"==typeof l||void 0===l)&&("string"==typeof a||void 0===a)){if("EC"===e&&"sig"===u)switch(c){case"P-256":l="ES256";break;case"P-384":l="ES384";break;case"P-521":l="ES512"}if("secp256k1"===c&&(u="sig",l="ES256K"),"OKP"===e)switch(c){case"Ed25519":case"Ed448":u="sig",l="EdDSA";break;case"X25519":case"X448":u="enc"}if(l&&!u)switch(!0){case l.startsWith("ECDH"):case l.startsWith("RSA"):u="enc"}if(r&&("oct"===i.kty||!i.d))throw Error("jwks must only contain private keys");t&&(i.d||i.k)||s.push({jwk:{...i,alg:l,use:u},async keyObject(e){if(this[e])return this[e];let t=await n.importJWK(this.jwk,e);return this[e]=t,t},get algorithms(){return Object.defineProperty(this,"algorithms",{value:function(e,t,r,o){if(t)return new Set([t]);switch(r){case"EC":{let t=[];if(("enc"===e||void 0===e)&&(t=t.concat(["ECDH-ES","ECDH-ES+A128KW","ECDH-ES+A192KW","ECDH-ES+A256KW"])),"sig"===e||void 0===e)switch(o){case"P-256":case"P-384":t=t.concat([`ES${o.slice(-3)}`]);break;case"P-521":t=t.concat(["ES512"]);break;case"secp256k1":"node:crypto"===n.cryptoRuntime&&(t=t.concat(["ES256K"]))}return new Set(t)}case"OKP":return new Set(["ECDH-ES","ECDH-ES+A128KW","ECDH-ES+A192KW","ECDH-ES+A256KW"]);case"RSA":{let t=[];return("enc"===e||void 0===e)&&(t=t.concat(["RSA-OAEP","RSA-OAEP-256","RSA-OAEP-384","RSA-OAEP-512"]),"node:crypto"===n.cryptoRuntime&&(t=t.concat(["RSA1_5"]))),("sig"===e||void 0===e)&&(t=t.concat(["PS256","PS384","PS512","RS256","RS384","RS512"])),new Set(t)}default:throw Error("unreachable")}}(this.jwk.use,this.jwk.alg,this.jwk.kty,this.jwk.crv),enumerable:!0,configurable:!1}),this.algorithms}})}}return new this(a,s)}filter(...e){return this.#t.filter(...e)}find(...e){return this.#t.find(...e)}every(...e){return this.#t.every(...e)}some(...e){return this.#t.some(...e)}map(...e){return this.#t.map(...e)}forEach(...e){return this.#t.forEach(...e)}reduce(...e){return this.#t.reduce(...e)}sort(...e){return this.#t.sort(...e)}*[Symbol.iterator](){for(let e of this.#t)yield e}}},29132:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.compactVerify=void 0;let n=r(71746),o=r(99938),i=r(2594);t.compactVerify=async function(e,t,r){if(e instanceof Uint8Array&&(e=i.decoder.decode(e)),"string"!=typeof e)throw new o.JWSInvalid("Compact JWS must be a string or Uint8Array");let{0:a,1:s,2:c,length:l}=e.split(".");if(3!==l)throw new o.JWSInvalid("Invalid Compact JWS");let u=await (0,n.flattenedVerify)({payload:s,protected:a,signature:c},t,r),d={payload:u.payload,protectedHeader:u.protectedHeader};return"function"==typeof t?{...d,key:u.key}:d}},29837:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createSecret=function(e){var t;let{authOptions:r,url:o}=e;return null!=(t=r.secret)?t:(0,n.createHash)("sha256").update(JSON.stringify({...o,...r})).digest("hex")},t.fromDate=function(e,t=Date.now()){return new Date(t+1e3*e)},t.hashToken=function(e,t){var r;let{provider:o,secret:i}=t;return(0,n.createHash)("sha256").update(`${e}${null!=(r=o.secret)?r:i}`).digest("hex")};var n=r(55511)},30641:(e,t,r)=>{let{inspect:n}=r(28354),{RPError:o,OPError:i}=r(61408),a=r(16156);class s{#r;#n;#o;#i;#a;#s;#c;#l;#u;constructor({client:e,exchangeBody:t,clientAssertionPayload:r,response:n,maxAge:i,DPoP:s}){if(["verification_uri","user_code","device_code"].forEach(e=>{if("string"!=typeof n[e]||!n[e])throw new o(`expected ${e} string to be returned by Device Authorization Response, got %j`,n[e])}),!Number.isSafeInteger(n.expires_in))throw new o("expected expires_in number to be returned by Device Authorization Response, got %j",n.expires_in);this.#s=a()+n.expires_in,this.#n=e,this.#i=s,this.#l=i,this.#a=t,this.#o=r,this.#u=n,this.#c=1e3*n.interval||5e3}abort(){this.#r=!0}async poll({signal:e}={}){let t;if(e&&e.aborted||this.#r)throw new o("polling aborted");if(this.expired())throw new o("the device code %j has expired and the device authorization session has concluded",this.device_code);await new Promise(e=>setTimeout(e,this.#c));try{t=await this.#n.grant({...this.#a,grant_type:"urn:ietf:params:oauth:grant-type:device_code",device_code:this.device_code},{clientAssertionPayload:this.#o,DPoP:this.#i})}catch(t){switch(t instanceof i&&t.error){case"slow_down":this.#c+=5e3;case"authorization_pending":return this.poll({signal:e});default:throw t}}return"id_token"in t&&(await this.#n.decryptIdToken(t),await this.#n.validateIdToken(t,void 0,"token",this.#l)),t}get device_code(){return this.#u.device_code}get user_code(){return this.#u.user_code}get verification_uri(){return this.#u.verification_uri}get verification_uri_complete(){return this.#u.verification_uri_complete}get expires_in(){return Math.max.apply(null,[this.#s-a(),0])}expired(){return 0===this.expires_in}[n.custom](){return`${this.constructor.name} ${n(this.#u,{depth:1/0,colors:process.stdout.isTTY,compact:!1,sorted:!0})}`}}e.exports=s},30994:e=>{function t(r,n){return e.exports=t=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},e.exports.__esModule=!0,e.exports.default=e.exports,t(r,n)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},33648:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.generateSecret=void 0;let n=r(51688);t.generateSecret=async function(e,t){return(0,n.generateSecret)(e,t)}},34010:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(55511),o=r(2594);t.default=function(e,t,r,i,a,s){let c=(0,o.concat)(e,t,r,(0,o.uint64be)(e.length<<3)),l=(0,n.createHmac)(`sha${i}`,a);return l.update(c),l.digest().slice(0,s>>3)}},35426:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,t.getServerSession=s,t.unstable_getServerSession=c;var n=r(64872),o=r(87451);async function i(e,t,r){var i,a,s,c,l,u,d,f,p;let{nextauth:h,...y}=e.query;null!=r.secret||(r.secret=null!=(a=null!=(s=null==(c=r.jwt)?void 0:c.secret)?s:process.env.NEXTAUTH_SECRET)?a:process.env.AUTH_SECRET);let _=await (0,n.AuthHandler)({req:{body:e.body,query:y,cookies:e.cookies,headers:e.headers,method:e.method,action:null==h?void 0:h[0],providerId:null==h?void 0:h[1],error:null!=(l=e.query.error)?l:null==h?void 0:h[1]},options:r});if(t.status(null!=(u=_.status)?u:200),null==(d=_.cookies)||d.forEach(e=>(0,o.setCookie)(t,e)),null==(f=_.headers)||f.forEach(e=>t.setHeader(e.key,e.value)),_.redirect){if((null==(p=e.body)?void 0:p.json)!=="true"){t.status(302).setHeader("Location",_.redirect),t.end();return}return t.json({url:_.redirect})}return t.send(_.body)}async function a(e,t,i){var a,s,c,l;null!=i.secret||(i.secret=null!=(s=process.env.NEXTAUTH_SECRET)?s:process.env.AUTH_SECRET);let{headers:u,cookies:d}=r(44999),f=null==(c=await t.params)?void 0:c.nextauth,p=Object.fromEntries(e.nextUrl.searchParams),h=await (0,o.getBody)(e),y=await (0,n.AuthHandler)({req:{body:h,query:p,cookies:Object.fromEntries((await d()).getAll().map(e=>[e.name,e.value])),headers:Object.fromEntries(await u()),method:e.method,action:null==f?void 0:f[0],providerId:null==f?void 0:f[1],error:null!=(l=p.error)?l:null==f?void 0:f[1]},options:i}),_=(0,o.toResponse)(y),g=_.headers.get("Location");return(null==h?void 0:h.json)==="true"&&g?(_.headers.delete("Location"),_.headers.set("Content-Type","application/json"),new Response(JSON.stringify({url:g}),{status:y.status,headers:_.headers})):_}async function s(...e){var t,i,a;let c,l,u,d=0===e.length||1===e.length;if(d){u=Object.assign({},e[0],{providers:[]});let{headers:t,cookies:n}=r(44999);c={headers:Object.fromEntries(await t()),cookies:Object.fromEntries((await n()).getAll().map(e=>[e.name,e.value]))},l={getHeader(){},setCookie(){},setHeader(){}}}else c=e[0],l=e[1],u=Object.assign({},e[2],{providers:[]});null!=(t=u).secret||(t.secret=null!=(a=process.env.NEXTAUTH_SECRET)?a:process.env.AUTH_SECRET);let{body:f,cookies:p,status:h=200}=await (0,n.AuthHandler)({options:u,req:{action:"session",method:"GET",cookies:c.cookies,headers:c.headers}});if(null==p||p.forEach(e=>(0,o.setCookie)(l,e)),f&&"string"!=typeof f&&Object.keys(f).length){if(200===h)return d&&delete f.expires,f;throw Error(f.message)}return null}async function c(...e){return await s(...e)}t.default=function(...e){var t;return 1===e.length?async(t,r)=>null!=r&&r.params?await a(t,r,e[0]):await i(t,r,e[0]):null!=(t=e[1])&&t.params?a(...e):i(...e)}},35571:(e,t,r)=>{let{createHash:n,randomBytes:o}=r(55511),i=r(70885),a=(e=32)=>i.encode(o(e));e.exports={random:a,state:a,nonce:a,codeVerifier:a,codeChallenge:e=>i.encode(n("sha256").update(e).digest())}},35749:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.bitLength=void 0;let n=r(99938),o=r(7552);function i(e){switch(e){case"A128GCM":case"A128GCMKW":case"A192GCM":case"A192GCMKW":case"A256GCM":case"A256GCMKW":return 96;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return 128;default:throw new n.JOSENotSupported(`Unsupported JWE Algorithm: ${e}`)}}t.bitLength=i,t.default=e=>(0,o.default)(new Uint8Array(i(e)>>3))},36720:(e,t,r)=>{var n=r(11723),o=(r(55511),r(55591)),i=r(81630),a=r(79551),s=r(22165);t.OAuth2=function(e,t,r,n,o,i){this._clientId=e,this._clientSecret=t,this._baseSite=r,this._authorizeUrl=n||"/oauth/authorize",this._accessTokenUrl=o||"/oauth/access_token",this._accessTokenName="access_token",this._authMethod="Bearer",this._customHeaders=i||{},this._useAuthorizationHeaderForGET=!1,this._agent=void 0},t.OAuth2.prototype.setAgent=function(e){this._agent=e},t.OAuth2.prototype.setAccessTokenName=function(e){this._accessTokenName=e},t.OAuth2.prototype.setAuthMethod=function(e){this._authMethod=e},t.OAuth2.prototype.useAuthorizationHeaderforGET=function(e){this._useAuthorizationHeaderForGET=e},t.OAuth2.prototype._getAccessTokenUrl=function(){return this._baseSite+this._accessTokenUrl},t.OAuth2.prototype.buildAuthHeader=function(e){return this._authMethod+" "+e},t.OAuth2.prototype._chooseHttpLibrary=function(e){var t=o;return"https:"!=e.protocol&&(t=i),t},t.OAuth2.prototype._request=function(e,t,r,o,i,s){var c=a.parse(t,!0);"https:"!=c.protocol||c.port||(c.port=443);var l=this._chooseHttpLibrary(c),u={};for(var d in this._customHeaders)u[d]=this._customHeaders[d];if(r)for(var d in r)u[d]=r[d];u.Host=c.host,u["User-Agent"]||(u["User-Agent"]="Node-oauth"),o?Buffer.isBuffer(o)?u["Content-Length"]=o.length:u["Content-Length"]=Buffer.byteLength(o):u["Content-length"]=0,!i||"Authorization"in u||(c.query||(c.query={}),c.query[this._accessTokenName]=i);var f=n.stringify(c.query);f&&(f="?"+f);var p={host:c.hostname,port:c.port,path:c.pathname+f,method:e,headers:u};this._executeRequest(l,p,o,s)},t.OAuth2.prototype._executeRequest=function(e,t,r,n){var o=s.isAnEarlyCloseHost(t.host),i=!1;function a(e,t){i||(i=!0,e.statusCode>=200&&e.statusCode<=299||301==e.statusCode||302==e.statusCode?n(null,t,e):n({statusCode:e.statusCode,data:t}))}var c="";this._agent&&(t.agent=this._agent);var l=e.request(t);l.on("response",function(e){e.on("data",function(e){c+=e}),e.on("close",function(t){o&&a(e,c)}),e.addListener("end",function(){a(e,c)})}),l.on("error",function(e){i=!0,n(e)}),("POST"==t.method||"PUT"==t.method)&&r&&l.write(r),l.end()},t.OAuth2.prototype.getAuthorizeUrl=function(e){var e=e||{};return e.client_id=this._clientId,this._baseSite+this._authorizeUrl+"?"+n.stringify(e)},t.OAuth2.prototype.getOAuthAccessToken=function(e,t,r){var t=t||{};t.client_id=this._clientId,t.client_secret=this._clientSecret;var o="refresh_token"===t.grant_type?"refresh_token":"code";t[o]=e;var i=n.stringify(t);this._request("POST",this._getAccessTokenUrl(),{"Content-Type":"application/x-www-form-urlencoded"},i,null,function(e,t,o){if(e)r(e);else{try{i=JSON.parse(t)}catch(e){i=n.parse(t)}var i,a=i.access_token,s=i.refresh_token;delete i.refresh_token,r(null,a,s,i)}})},t.OAuth2.prototype.getProtectedResource=function(e,t,r){this._request("GET",e,{},"",t,r)},t.OAuth2.prototype.get=function(e,t,r){if(this._useAuthorizationHeaderForGET){var n={Authorization:this.buildAuthHeader(t)};t=null}else n={};this._request("GET",e,n,"",t,r)}},36736:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.flattenedDecrypt=void 0;let n=r(68327),o=r(85634),i=r(66318),a=r(99938),s=r(65597),c=r(25110),l=r(43343),u=r(2594),d=r(61689),f=r(8535),p=r(93069);t.flattenedDecrypt=async function(e,t,r){var h;let y,_,g,b,m,v,w;if(!(0,c.default)(e))throw new a.JWEInvalid("Flattened JWE must be an object");if(void 0===e.protected&&void 0===e.header&&void 0===e.unprotected)throw new a.JWEInvalid("JOSE Header missing");if("string"!=typeof e.iv)throw new a.JWEInvalid("JWE Initialization Vector missing or incorrect type");if("string"!=typeof e.ciphertext)throw new a.JWEInvalid("JWE Ciphertext missing or incorrect type");if("string"!=typeof e.tag)throw new a.JWEInvalid("JWE Authentication Tag missing or incorrect type");if(void 0!==e.protected&&"string"!=typeof e.protected)throw new a.JWEInvalid("JWE Protected Header incorrect type");if(void 0!==e.encrypted_key&&"string"!=typeof e.encrypted_key)throw new a.JWEInvalid("JWE Encrypted Key incorrect type");if(void 0!==e.aad&&"string"!=typeof e.aad)throw new a.JWEInvalid("JWE AAD incorrect type");if(void 0!==e.header&&!(0,c.default)(e.header))throw new a.JWEInvalid("JWE Shared Unprotected Header incorrect type");if(void 0!==e.unprotected&&!(0,c.default)(e.unprotected))throw new a.JWEInvalid("JWE Per-Recipient Unprotected Header incorrect type");if(e.protected)try{let t=(0,n.decode)(e.protected);y=JSON.parse(u.decoder.decode(t))}catch{throw new a.JWEInvalid("JWE Protected Header is invalid")}if(!(0,s.default)(y,e.header,e.unprotected))throw new a.JWEInvalid("JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint");let x={...y,...e.header,...e.unprotected};if((0,f.default)(a.JWEInvalid,new Map,null==r?void 0:r.crit,y,x),void 0!==x.zip){if(!y||!y.zip)throw new a.JWEInvalid('JWE "zip" (Compression Algorithm) Header MUST be integrity protected');if("DEF"!==x.zip)throw new a.JOSENotSupported('Unsupported JWE "zip" (Compression Algorithm) Header Parameter value')}let{alg:k,enc:E}=x;if("string"!=typeof k||!k)throw new a.JWEInvalid("missing JWE Algorithm (alg) in JWE Header");if("string"!=typeof E||!E)throw new a.JWEInvalid("missing JWE Encryption Algorithm (enc) in JWE Header");let S=r&&(0,p.default)("keyManagementAlgorithms",r.keyManagementAlgorithms),A=r&&(0,p.default)("contentEncryptionAlgorithms",r.contentEncryptionAlgorithms);if(S&&!S.has(k))throw new a.JOSEAlgNotAllowed('"alg" (Algorithm) Header Parameter not allowed');if(A&&!A.has(E))throw new a.JOSEAlgNotAllowed('"enc" (Encryption Algorithm) Header Parameter not allowed');if(void 0!==e.encrypted_key)try{_=(0,n.decode)(e.encrypted_key)}catch{throw new a.JWEInvalid("Failed to base64url decode the encrypted_key")}let O=!1;"function"==typeof t&&(t=await t(y,e),O=!0);try{g=await (0,l.default)(k,t,_,x,r)}catch(e){if(e instanceof TypeError||e instanceof a.JWEInvalid||e instanceof a.JOSENotSupported)throw e;g=(0,d.default)(E)}try{b=(0,n.decode)(e.iv)}catch{throw new a.JWEInvalid("Failed to base64url decode the iv")}try{m=(0,n.decode)(e.tag)}catch{throw new a.JWEInvalid("Failed to base64url decode the tag")}let P=u.encoder.encode(null!=(h=e.protected)?h:"");v=void 0!==e.aad?(0,u.concat)(P,u.encoder.encode("."),u.encoder.encode(e.aad)):P;try{w=(0,n.decode)(e.ciphertext)}catch{throw new a.JWEInvalid("Failed to base64url decode the ciphertext")}let T=await (0,o.default)(E,g,w,b,m,v);"DEF"===x.zip&&(T=await ((null==r?void 0:r.inflateRaw)||i.inflate)(T));let C={plaintext:T};if(void 0!==e.protected&&(C.protectedHeader=y),void 0!==e.aad)try{C.additionalAuthenticatedData=(0,n.decode)(e.aad)}catch{throw new a.JWEInvalid("Failed to base64url decode the aad")}return(void 0!==e.unprotected&&(C.sharedUnprotectedHeader=e.unprotected),void 0!==e.header&&(C.unprotectedHeader=e.header),O)?{...C,key:t}:C}},37265:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.types=void 0;let n=r(17996),o=r(90061);t.default=e=>(0,o.default)(e)||(0,n.isCryptoKey)(e);let i=["KeyObject"];t.types=i,(globalThis.CryptoKey||(null===n.default||void 0===n.default?void 0:n.default.CryptoKey))&&i.push("CryptoKey")},37375:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(55511);t.default=(e,t)=>(0,n.createHash)(e).update(t).digest()},37544:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.cryptoRuntime=t.base64url=t.generateSecret=t.generateKeyPair=t.errors=t.decodeJwt=t.decodeProtectedHeader=t.importJWK=t.importX509=t.importPKCS8=t.importSPKI=t.exportJWK=t.exportSPKI=t.exportPKCS8=t.UnsecuredJWT=t.createRemoteJWKSet=t.createLocalJWKSet=t.EmbeddedJWK=t.calculateJwkThumbprintUri=t.calculateJwkThumbprint=t.EncryptJWT=t.SignJWT=t.GeneralSign=t.FlattenedSign=t.CompactSign=t.FlattenedEncrypt=t.CompactEncrypt=t.jwtDecrypt=t.jwtVerify=t.generalVerify=t.flattenedVerify=t.compactVerify=t.GeneralEncrypt=t.generalDecrypt=t.flattenedDecrypt=t.compactDecrypt=void 0;var n=r(81454);Object.defineProperty(t,"compactDecrypt",{enumerable:!0,get:function(){return n.compactDecrypt}});var o=r(36736);Object.defineProperty(t,"flattenedDecrypt",{enumerable:!0,get:function(){return o.flattenedDecrypt}});var i=r(68907);Object.defineProperty(t,"generalDecrypt",{enumerable:!0,get:function(){return i.generalDecrypt}});var a=r(64127);Object.defineProperty(t,"GeneralEncrypt",{enumerable:!0,get:function(){return a.GeneralEncrypt}});var s=r(29132);Object.defineProperty(t,"compactVerify",{enumerable:!0,get:function(){return s.compactVerify}});var c=r(71746);Object.defineProperty(t,"flattenedVerify",{enumerable:!0,get:function(){return c.flattenedVerify}});var l=r(24575);Object.defineProperty(t,"generalVerify",{enumerable:!0,get:function(){return l.generalVerify}});var u=r(98905);Object.defineProperty(t,"jwtVerify",{enumerable:!0,get:function(){return u.jwtVerify}});var d=r(28247);Object.defineProperty(t,"jwtDecrypt",{enumerable:!0,get:function(){return d.jwtDecrypt}});var f=r(52118);Object.defineProperty(t,"CompactEncrypt",{enumerable:!0,get:function(){return f.CompactEncrypt}});var p=r(14988);Object.defineProperty(t,"FlattenedEncrypt",{enumerable:!0,get:function(){return p.FlattenedEncrypt}});var h=r(99312);Object.defineProperty(t,"CompactSign",{enumerable:!0,get:function(){return h.CompactSign}});var y=r(59310);Object.defineProperty(t,"FlattenedSign",{enumerable:!0,get:function(){return y.FlattenedSign}});var _=r(45495);Object.defineProperty(t,"GeneralSign",{enumerable:!0,get:function(){return _.GeneralSign}});var g=r(48513);Object.defineProperty(t,"SignJWT",{enumerable:!0,get:function(){return g.SignJWT}});var b=r(45787);Object.defineProperty(t,"EncryptJWT",{enumerable:!0,get:function(){return b.EncryptJWT}});var m=r(18652);Object.defineProperty(t,"calculateJwkThumbprint",{enumerable:!0,get:function(){return m.calculateJwkThumbprint}}),Object.defineProperty(t,"calculateJwkThumbprintUri",{enumerable:!0,get:function(){return m.calculateJwkThumbprintUri}});var v=r(1149);Object.defineProperty(t,"EmbeddedJWK",{enumerable:!0,get:function(){return v.EmbeddedJWK}});var w=r(2055);Object.defineProperty(t,"createLocalJWKSet",{enumerable:!0,get:function(){return w.createLocalJWKSet}});var x=r(91428);Object.defineProperty(t,"createRemoteJWKSet",{enumerable:!0,get:function(){return x.createRemoteJWKSet}});var k=r(71872);Object.defineProperty(t,"UnsecuredJWT",{enumerable:!0,get:function(){return k.UnsecuredJWT}});var E=r(65764);Object.defineProperty(t,"exportPKCS8",{enumerable:!0,get:function(){return E.exportPKCS8}}),Object.defineProperty(t,"exportSPKI",{enumerable:!0,get:function(){return E.exportSPKI}}),Object.defineProperty(t,"exportJWK",{enumerable:!0,get:function(){return E.exportJWK}});var S=r(97920);Object.defineProperty(t,"importSPKI",{enumerable:!0,get:function(){return S.importSPKI}}),Object.defineProperty(t,"importPKCS8",{enumerable:!0,get:function(){return S.importPKCS8}}),Object.defineProperty(t,"importX509",{enumerable:!0,get:function(){return S.importX509}}),Object.defineProperty(t,"importJWK",{enumerable:!0,get:function(){return S.importJWK}});var A=r(69184);Object.defineProperty(t,"decodeProtectedHeader",{enumerable:!0,get:function(){return A.decodeProtectedHeader}});var O=r(52645);Object.defineProperty(t,"decodeJwt",{enumerable:!0,get:function(){return O.decodeJwt}}),t.errors=r(99938);var P=r(13668);Object.defineProperty(t,"generateKeyPair",{enumerable:!0,get:function(){return P.generateKeyPair}});var T=r(33648);Object.defineProperty(t,"generateSecret",{enumerable:!0,get:function(){return T.generateSecret}}),t.base64url=r(67929);var C=r(8157);Object.defineProperty(t,"cryptoRuntime",{enumerable:!0,get:function(){return C.default}})},38700:e=>{let t=/(\w+)=("[^"]*")/g;e.exports=e=>{let r={};try{for(;null!==t.exec(e);)RegExp.$1&&RegExp.$2&&(r[RegExp.$1]=RegExp.$2.slice(1,-1))}catch(e){}return r}},41193:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(55511),o=r(68327),i=r(72443),a=r(99938),s=r(62909),c=r(17996),l=r(90061),u=r(78680),d=r(37265),f=r(80028),p=e=>{let t;if((0,c.isCryptoKey)(e)){if(!e.extractable)throw TypeError("CryptoKey is not extractable");t=n.KeyObject.from(e)}else if((0,l.default)(e))t=e;else if(e instanceof Uint8Array)return{kty:"oct",k:(0,o.encode)(e)};else throw TypeError((0,u.default)(e,...d.types,"Uint8Array"));if(f.jwkExport){if("secret"!==t.type&&!["rsa","ec","ed25519","x25519","ed448","x448"].includes(t.asymmetricKeyType))throw new a.JOSENotSupported("Unsupported key asymmetricKeyType");return t.export({format:"jwk"})}switch(t.type){case"secret":return{kty:"oct",k:(0,o.encode)(t.export())};case"private":case"public":switch(t.asymmetricKeyType){case"rsa":{let e,r=t.export({format:"der",type:"pkcs1"}),n=new i.default(r);"private"===t.type&&n.unsignedInteger();let a=(0,o.encode)(n.unsignedInteger()),s=(0,o.encode)(n.unsignedInteger());return"private"===t.type&&(e={d:(0,o.encode)(n.unsignedInteger()),p:(0,o.encode)(n.unsignedInteger()),q:(0,o.encode)(n.unsignedInteger()),dp:(0,o.encode)(n.unsignedInteger()),dq:(0,o.encode)(n.unsignedInteger()),qi:(0,o.encode)(n.unsignedInteger())}),n.end(),{kty:"RSA",n:a,e:s,...e}}case"ec":{let e,r,i,c=(0,s.default)(t);switch(c){case"secp256k1":e=64,r=33,i=-1;break;case"P-256":e=64,r=36,i=-1;break;case"P-384":e=96,r=35,i=-3;break;case"P-521":e=132,r=35,i=-3;break;default:throw new a.JOSENotSupported("Unsupported curve")}if("public"===t.type){let r=t.export({type:"spki",format:"der"});return{kty:"EC",crv:c,x:(0,o.encode)(r.subarray(-e,-e/2)),y:(0,o.encode)(r.subarray(-e/2))}}let l=t.export({type:"pkcs8",format:"der"});return l.length<100&&(r+=i),{...p((0,n.createPublicKey)(t)),d:(0,o.encode)(l.subarray(r,r+e/2))}}case"ed25519":case"x25519":{let e=(0,s.default)(t);if("public"===t.type){let r=t.export({type:"spki",format:"der"});return{kty:"OKP",crv:e,x:(0,o.encode)(r.subarray(-32))}}let r=t.export({type:"pkcs8",format:"der"});return{...p((0,n.createPublicKey)(t)),d:(0,o.encode)(r.subarray(-32))}}case"ed448":case"x448":{let e=(0,s.default)(t);if("public"===t.type){let r=t.export({type:"spki",format:"der"});return{kty:"OKP",crv:e,x:(0,o.encode)(r.subarray("Ed448"===e?-57:-56))}}let r=t.export({type:"pkcs8",format:"der"});return{...p((0,n.createPublicKey)(t)),d:(0,o.encode)(r.subarray("Ed448"===e?-57:-56))}}default:throw new a.JOSENotSupported("Unsupported key asymmetricKeyType")}default:throw new a.JOSENotSupported("Unsupported key type")}};t.default=p},42142:(e,t,r)=>{"use strict";let n=r(93466),o=Symbol("max"),i=Symbol("length"),a=Symbol("lengthCalculator"),s=Symbol("allowStale"),c=Symbol("maxAge"),l=Symbol("dispose"),u=Symbol("noDisposeOnSet"),d=Symbol("lruList"),f=Symbol("cache"),p=Symbol("updateAgeOnGet"),h=()=>1;class y{constructor(e){if("number"==typeof e&&(e={max:e}),e||(e={}),e.max&&("number"!=typeof e.max||e.max<0))throw TypeError("max must be a non-negative number");this[o]=e.max||1/0;let t=e.length||h;if(this[a]="function"!=typeof t?h:t,this[s]=e.stale||!1,e.maxAge&&"number"!=typeof e.maxAge)throw TypeError("maxAge must be a number");this[c]=e.maxAge||0,this[l]=e.dispose,this[u]=e.noDisposeOnSet||!1,this[p]=e.updateAgeOnGet||!1,this.reset()}set max(e){if("number"!=typeof e||e<0)throw TypeError("max must be a non-negative number");this[o]=e||1/0,b(this)}get max(){return this[o]}set allowStale(e){this[s]=!!e}get allowStale(){return this[s]}set maxAge(e){if("number"!=typeof e)throw TypeError("maxAge must be a non-negative number");this[c]=e,b(this)}get maxAge(){return this[c]}set lengthCalculator(e){"function"!=typeof e&&(e=h),e!==this[a]&&(this[a]=e,this[i]=0,this[d].forEach(e=>{e.length=this[a](e.value,e.key),this[i]+=e.length})),b(this)}get lengthCalculator(){return this[a]}get length(){return this[i]}get itemCount(){return this[d].length}rforEach(e,t){t=t||this;for(let r=this[d].tail;null!==r;){let n=r.prev;w(this,e,r,t),r=n}}forEach(e,t){t=t||this;for(let r=this[d].head;null!==r;){let n=r.next;w(this,e,r,t),r=n}}keys(){return this[d].toArray().map(e=>e.key)}values(){return this[d].toArray().map(e=>e.value)}reset(){this[l]&&this[d]&&this[d].length&&this[d].forEach(e=>this[l](e.key,e.value)),this[f]=new Map,this[d]=new n,this[i]=0}dump(){return this[d].map(e=>!g(this,e)&&{k:e.key,v:e.value,e:e.now+(e.maxAge||0)}).toArray().filter(e=>e)}dumpLru(){return this[d]}set(e,t,r){if((r=r||this[c])&&"number"!=typeof r)throw TypeError("maxAge must be a number");let n=r?Date.now():0,s=this[a](t,e);if(this[f].has(e)){if(s>this[o])return m(this,this[f].get(e)),!1;let a=this[f].get(e).value;return this[l]&&!this[u]&&this[l](e,a.value),a.now=n,a.maxAge=r,a.value=t,this[i]+=s-a.length,a.length=s,this.get(e),b(this),!0}let p=new v(e,t,s,n,r);return p.length>this[o]?(this[l]&&this[l](e,t),!1):(this[i]+=p.length,this[d].unshift(p),this[f].set(e,this[d].head),b(this),!0)}has(e){return!!this[f].has(e)&&!g(this,this[f].get(e).value)}get(e){return _(this,e,!0)}peek(e){return _(this,e,!1)}pop(){let e=this[d].tail;return e?(m(this,e),e.value):null}del(e){m(this,this[f].get(e))}load(e){this.reset();let t=Date.now();for(let r=e.length-1;r>=0;r--){let n=e[r],o=n.e||0;if(0===o)this.set(n.k,n.v);else{let e=o-t;e>0&&this.set(n.k,n.v,e)}}}prune(){this[f].forEach((e,t)=>_(this,t,!1))}}let _=(e,t,r)=>{let n=e[f].get(t);if(n){let t=n.value;if(g(e,t)){if(m(e,n),!e[s])return}else r&&(e[p]&&(n.value.now=Date.now()),e[d].unshiftNode(n));return t.value}},g=(e,t)=>{if(!t||!t.maxAge&&!e[c])return!1;let r=Date.now()-t.now;return t.maxAge?r>t.maxAge:e[c]&&r>e[c]},b=e=>{if(e[i]>e[o])for(let t=e[d].tail;e[i]>e[o]&&null!==t;){let r=t.prev;m(e,t),t=r}},m=(e,t)=>{if(t){let r=t.value;e[l]&&e[l](r.key,r.value),e[i]-=r.length,e[f].delete(r.key),e[d].removeNode(t)}};class v{constructor(e,t,r,n,o){this.key=e,this.value=t,this.length=r,this.now=n,this.maxAge=o||0}}let w=(e,t,r,n)=>{let o=r.value;g(e,o)&&(m(e,r),e[s]||(o=void 0)),o&&t.call(n,o.value,o.key,e)};e.exports=y},42567:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(55511),o=r(62909),i=r(99938),a=r(78328),s=r(80028),c={padding:n.constants.RSA_PKCS1_PSS_PADDING,saltLength:n.constants.RSA_PSS_SALTLEN_DIGEST},l=new Map([["ES256","P-256"],["ES256K","secp256k1"],["ES384","P-384"],["ES512","P-521"]]);t.default=function(e,t){switch(e){case"EdDSA":if(!["ed25519","ed448"].includes(t.asymmetricKeyType))throw TypeError("Invalid key for this operation, its asymmetricKeyType must be ed25519 or ed448");return t;case"RS256":case"RS384":case"RS512":if("rsa"!==t.asymmetricKeyType)throw TypeError("Invalid key for this operation, its asymmetricKeyType must be rsa");return(0,a.default)(t,e),t;case s.rsaPssParams&&"PS256":case s.rsaPssParams&&"PS384":case s.rsaPssParams&&"PS512":if("rsa-pss"===t.asymmetricKeyType){let{hashAlgorithm:r,mgf1HashAlgorithm:n,saltLength:o}=t.asymmetricKeyDetails,i=parseInt(e.slice(-3),10);if(void 0!==r&&(r!==`sha${i}`||n!==r))throw TypeError(`Invalid key for this operation, its RSA-PSS parameters do not meet the requirements of "alg" ${e}`);if(void 0!==o&&o>i>>3)throw TypeError(`Invalid key for this operation, its RSA-PSS parameter saltLength does not meet the requirements of "alg" ${e}`)}else if("rsa"!==t.asymmetricKeyType)throw TypeError("Invalid key for this operation, its asymmetricKeyType must be rsa or rsa-pss");return(0,a.default)(t,e),{key:t,...c};case!s.rsaPssParams&&"PS256":case!s.rsaPssParams&&"PS384":case!s.rsaPssParams&&"PS512":if("rsa"!==t.asymmetricKeyType)throw TypeError("Invalid key for this operation, its asymmetricKeyType must be rsa");return(0,a.default)(t,e),{key:t,...c};case"ES256":case"ES256K":case"ES384":case"ES512":{if("ec"!==t.asymmetricKeyType)throw TypeError("Invalid key for this operation, its asymmetricKeyType must be ec");let r=(0,o.default)(t),n=l.get(e);if(r!==n)throw TypeError(`Invalid key curve for the algorithm, its curve must be ${n}, got ${r}`);return{dsaEncoding:"ieee-p1363",key:t}}default:throw new i.JOSENotSupported(`alg ${e} is not supported either by JOSE or your javascript runtime`)}}},42856:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.detectOrigin=function(e,t){var r;return(null!=(r=process.env.VERCEL)?r:process.env.AUTH_TRUST_HOST)?`${"http"===t?"http":"https"}://${e}`:process.env.NEXTAUTH_URL}},42891:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(99938),o=r(35749);t.default=(e,t)=>{if(t.length<<3!==(0,o.bitLength)(e))throw new n.JWEInvalid("Invalid Initialization Vector length")}},43325:e=>{let t=Symbol();e.exports={CLOCK_TOLERANCE:Symbol(),HTTP_OPTIONS:t}},43343:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(88062),o=r(52131),i=r(62197),a=r(56913),s=r(68327),c=r(99938),l=r(61689),u=r(97920),d=r(80335),f=r(25110),p=r(97528);t.default=async function(e,t,r,h,y){switch((0,d.default)(e,t,"decrypt"),e){case"dir":if(void 0!==r)throw new c.JWEInvalid("Encountered unexpected JWE Encrypted Key");return t;case"ECDH-ES":if(void 0!==r)throw new c.JWEInvalid("Encountered unexpected JWE Encrypted Key");case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let i,a;if(!(0,f.default)(h.epk))throw new c.JWEInvalid('JOSE Header "epk" (Ephemeral Public Key) missing or invalid');if(!o.ecdhAllowed(t))throw new c.JOSENotSupported("ECDH with the provided key is not allowed or not supported by your javascript runtime");let d=await (0,u.importJWK)(h.epk,e);if(void 0!==h.apu){if("string"!=typeof h.apu)throw new c.JWEInvalid('JOSE Header "apu" (Agreement PartyUInfo) invalid');try{i=(0,s.decode)(h.apu)}catch{throw new c.JWEInvalid("Failed to base64url decode the apu")}}if(void 0!==h.apv){if("string"!=typeof h.apv)throw new c.JWEInvalid('JOSE Header "apv" (Agreement PartyVInfo) invalid');try{a=(0,s.decode)(h.apv)}catch{throw new c.JWEInvalid("Failed to base64url decode the apv")}}let p=await o.deriveKey(d,t,"ECDH-ES"===e?h.enc:e,"ECDH-ES"===e?(0,l.bitLength)(h.enc):parseInt(e.slice(-5,-2),10),i,a);if("ECDH-ES"===e)return p;if(void 0===r)throw new c.JWEInvalid("JWE Encrypted Key missing");return(0,n.unwrap)(e.slice(-6),p,r)}case"RSA1_5":case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":if(void 0===r)throw new c.JWEInvalid("JWE Encrypted Key missing");return(0,a.decrypt)(e,t,r);case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{let n;if(void 0===r)throw new c.JWEInvalid("JWE Encrypted Key missing");if("number"!=typeof h.p2c)throw new c.JWEInvalid('JOSE Header "p2c" (PBES2 Count) missing or invalid');let o=(null==y?void 0:y.maxPBES2Count)||1e4;if(h.p2c>o)throw new c.JWEInvalid('JOSE Header "p2c" (PBES2 Count) out is of acceptable bounds');if("string"!=typeof h.p2s)throw new c.JWEInvalid('JOSE Header "p2s" (PBES2 Salt) missing or invalid');try{n=(0,s.decode)(h.p2s)}catch{throw new c.JWEInvalid("Failed to base64url decode the p2s")}return(0,i.decrypt)(e,t,r,h.p2c,n)}case"A128KW":case"A192KW":case"A256KW":if(void 0===r)throw new c.JWEInvalid("JWE Encrypted Key missing");return(0,n.unwrap)(e,t,r);case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{let n,o;if(void 0===r)throw new c.JWEInvalid("JWE Encrypted Key missing");if("string"!=typeof h.iv)throw new c.JWEInvalid('JOSE Header "iv" (Initialization Vector) missing or invalid');if("string"!=typeof h.tag)throw new c.JWEInvalid('JOSE Header "tag" (Authentication Tag) missing or invalid');try{n=(0,s.decode)(h.iv)}catch{throw new c.JWEInvalid("Failed to base64url decode the iv")}try{o=(0,s.decode)(h.tag)}catch{throw new c.JWEInvalid("Failed to base64url decode the tag")}return(0,p.unwrap)(e,t,r,n,o)}default:throw new c.JOSENotSupported('Invalid or unsupported "alg" (JWE Algorithm) header value')}}},44726:e=>{e.exports=function(e){try{return -1!==Function.toString.call(e).indexOf("[native code]")}catch(t){return"function"==typeof e}},e.exports.__esModule=!0,e.exports.default=e.exports},44999:(e,t,r)=>{"use strict";r.r(t),r.d(t,{__esModule:()=>n.B,cookies:()=>n.U,draftMode:()=>i.r,headers:()=>o.b});var n=r(99933),o=r(86280),i=r(73913)},45495:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.GeneralSign=void 0;let n=r(59310),o=r(99938);class i{constructor(e,t,r){this.parent=e,this.key=t,this.options=r}setProtectedHeader(e){if(this.protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this.protectedHeader=e,this}setUnprotectedHeader(e){if(this.unprotectedHeader)throw TypeError("setUnprotectedHeader can only be called once");return this.unprotectedHeader=e,this}addSignature(...e){return this.parent.addSignature(...e)}sign(...e){return this.parent.sign(...e)}done(){return this.parent}}class a{constructor(e){this._signatures=[],this._payload=e}addSignature(e,t){let r=new i(this,e,t);return this._signatures.push(r),r}async sign(){if(!this._signatures.length)throw new o.JWSInvalid("at least one signature must be added");let e={signatures:[],payload:""};for(let t=0;t<this._signatures.length;t++){let r=this._signatures[t],i=new n.FlattenedSign(this._payload);i.setProtectedHeader(r.protectedHeader),i.setUnprotectedHeader(r.unprotectedHeader);let{payload:a,...s}=await i.sign(r.key,r.options);if(0===t)e.payload=a;else if(e.payload!==a)throw new o.JWSInvalid("inconsistent use of JWS Unencoded Payload (RFC7797)");e.signatures.push(s)}return e}}t.GeneralSign=a},45787:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.EncryptJWT=void 0;let n=r(52118),o=r(2594),i=r(2754);class a extends i.ProduceJWT{setProtectedHeader(e){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=e,this}setKeyManagementParameters(e){if(this._keyManagementParameters)throw TypeError("setKeyManagementParameters can only be called once");return this._keyManagementParameters=e,this}setContentEncryptionKey(e){if(this._cek)throw TypeError("setContentEncryptionKey can only be called once");return this._cek=e,this}setInitializationVector(e){if(this._iv)throw TypeError("setInitializationVector can only be called once");return this._iv=e,this}replicateIssuerAsHeader(){return this._replicateIssuerAsHeader=!0,this}replicateSubjectAsHeader(){return this._replicateSubjectAsHeader=!0,this}replicateAudienceAsHeader(){return this._replicateAudienceAsHeader=!0,this}async encrypt(e,t){let r=new n.CompactEncrypt(o.encoder.encode(JSON.stringify(this._payload)));return this._replicateIssuerAsHeader&&(this._protectedHeader={...this._protectedHeader,iss:this._payload.iss}),this._replicateSubjectAsHeader&&(this._protectedHeader={...this._protectedHeader,sub:this._payload.sub}),this._replicateAudienceAsHeader&&(this._protectedHeader={...this._protectedHeader,aud:this._payload.aud}),r.setProtectedHeader(this._protectedHeader),this._iv&&r.setInitializationVector(this._iv),this._cek&&r.setContentEncryptionKey(this._cek),this._keyManagementParameters&&r.setKeyManagementParameters(this._keyManagementParameters),r.encrypt(e,t)}}t.EncryptJWT=a},46821:(e,t,r)=>{"use strict";let n;Object.defineProperty(t,"__esModule",{value:!0});let o=r(55511),i=r(71800);"function"!=typeof o.hkdf||process.versions.electron||(n=async(...e)=>new Promise((t,r)=>{o.hkdf(...e,(e,n)=>{e?r(e):t(new Uint8Array(n))})})),t.default=async(e,t,r,o,a)=>(n||i.default)(e,t,r,o,a)},47024:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){let{url:t,csrfToken:r,theme:o}=e;return(0,n.h)("div",{className:"signout"},o.brandColor&&(0,n.h)("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${o.brandColor}
        }
      `}}),o.buttonText&&(0,n.h)("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${o.buttonText}
        }
      `}}),(0,n.h)("div",{className:"card"},o.logo&&(0,n.h)("img",{src:o.logo,alt:"Logo",className:"logo"}),(0,n.h)("h1",null,"Signout"),(0,n.h)("p",null,"Are you sure you want to sign out?"),(0,n.h)("form",{action:`${t}/signout`,method:"POST"},(0,n.h)("input",{type:"hidden",name:"csrfToken",value:r}),(0,n.h)("button",{id:"submitButton",type:"submit"},"Sign out"))))};var n=r(1441)},48160:(e,t,r)=>{let{STATUS_CODES:n}=r(81630),{format:o}=r(28354),{OPError:i}=r(61408),a=r(38700),s=e=>{let t=a(e.headers["www-authenticate"]);if(t.error)throw new i(t,e)},c=e=>{let t=!1;try{let r;r="object"!=typeof e.body||Buffer.isBuffer(e.body)?JSON.parse(e.body):e.body,(t="string"==typeof r.error&&r.error.length)&&Object.defineProperty(e,"body",{value:r,configurable:!0})}catch(e){}return t};e.exports=function(e,{statusCode:t=200,body:r=!0,bearer:a=!1}={}){if(e.statusCode!==t){if(a&&s(e),c(e))throw new i(e.body,e);throw new i({error:o("expected %i %s, got: %i %s",t,n[t],e.statusCode,n[e.statusCode])},e)}if(r&&!e.body)throw new i({error:o("expected %i %s with body but no body was returned",t,n[t])},e);return e.body}},48513:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SignJWT=void 0;let n=r(99312),o=r(99938),i=r(2594),a=r(2754);class s extends a.ProduceJWT{setProtectedHeader(e){return this._protectedHeader=e,this}async sign(e,t){var r;let a=new n.CompactSign(i.encoder.encode(JSON.stringify(this._payload)));if(a.setProtectedHeader(this._protectedHeader),Array.isArray(null==(r=this._protectedHeader)?void 0:r.crit)&&this._protectedHeader.crit.includes("b64")&&!1===this._protectedHeader.b64)throw new o.JWTInvalid("JWTs MUST NOT use unencoded payload");return a.sign(e,t)}}t.SignJWT=s},48807:e=>{function t(e,t,r,n,o,i,a){try{var s=e[i](a),c=s.value}catch(e){return void r(e)}s.done?t(c):Promise.resolve(c).then(n,o)}e.exports=function(e){return function(){var r=this,n=arguments;return new Promise(function(o,i){var a=e.apply(r,n);function s(e){t(a,o,i,s,c,"next",e)}function c(e){t(a,o,i,s,c,"throw",e)}s(void 0)})}},e.exports.__esModule=!0,e.exports.default=e.exports},49305:(e,t,r)=>{var n=r(24956).default;e.exports=function(e){if(null!=e){var t=e["function"==typeof Symbol&&Symbol.iterator||"@@iterator"],r=0;if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length))return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}}}throw TypeError(n(e)+" is not iterable")},e.exports.__esModule=!0,e.exports.default=e.exports},49450:(e,t,r)=>{let n=r(37544),{RPError:o}=r(61408),{assertIssuerConfiguration:i}=r(2279),{random:a}=r(35571),s=r(16156),c=r(9552),{keystores:l}=r(55132),u=r(86505),d=e=>encodeURIComponent(e).replace(/%20/g,"+");async function f(e,t){let r=this[`${e}_endpoint_auth_signing_alg`];if(r||i(this.issuer,`${e}_endpoint_auth_signing_alg_values_supported`),"client_secret_jwt"===this[`${e}_endpoint_auth_method`]){if(!r){let t=this.issuer[`${e}_endpoint_auth_signing_alg_values_supported`];r=Array.isArray(t)&&t.find(e=>/^HS(?:256|384|512)/.test(e))}if(!r)throw new o(`failed to determine a JWS Algorithm to use for ${this[`${e}_endpoint_auth_method`]} Client Assertion`);return new n.CompactSign(Buffer.from(JSON.stringify(t))).setProtectedHeader({alg:r}).sign(this.secretForAlg(r))}let a=await l.get(this);if(!a)throw TypeError("no client jwks provided for signing a client assertion with");if(!r){let t=this.issuer[`${e}_endpoint_auth_signing_alg_values_supported`];r=Array.isArray(t)&&t.find(e=>a.get({alg:e,use:"sig"}))}if(!r)throw new o(`failed to determine a JWS Algorithm to use for ${this[`${e}_endpoint_auth_method`]} Client Assertion`);let s=a.get({alg:r,use:"sig"});if(!s)throw new o(`no key found in client jwks to sign a client assertion with using alg ${r}`);return new n.CompactSign(Buffer.from(JSON.stringify(t))).setProtectedHeader({alg:r,kid:s.jwk&&s.jwk.kid}).sign(await s.keyObject(r))}async function p(e,{clientAssertionPayload:t}={}){switch(this[`${e}_endpoint_auth_method`]){case"self_signed_tls_client_auth":case"tls_client_auth":case"none":return{form:{client_id:this.client_id}};case"client_secret_post":if("string"!=typeof this.client_secret)throw TypeError("client_secret_post client authentication method requires a client_secret");return{form:{client_id:this.client_id,client_secret:this.client_secret}};case"private_key_jwt":case"client_secret_jwt":{let r=s(),n=await f.call(this,e,{iat:r,exp:r+60,jti:a(),iss:this.client_id,sub:this.client_id,aud:this.issuer.issuer,...t});return{form:{client_id:this.client_id,client_assertion:n,client_assertion_type:"urn:ietf:params:oauth:client-assertion-type:jwt-bearer"}}}case"client_secret_basic":{if("string"!=typeof this.client_secret)throw TypeError("client_secret_basic client authentication method requires a client_secret");let e=`${d(this.client_id)}:${d(this.client_secret)}`,t=Buffer.from(e).toString("base64");return{headers:{Authorization:`Basic ${t}`}}}default:throw TypeError(`missing, or unsupported, ${e}_endpoint_auth_method`)}}async function h(e,t,{clientAssertionPayload:r,endpointAuthMethod:n=e,DPoP:o}={}){let i,a=u(t,await p.call(this,n,{clientAssertionPayload:r})),s=this[`${n}_endpoint_auth_method`].includes("tls_client_auth")||"token"===e&&this.tls_client_certificate_bound_access_tokens;if(s&&this.issuer.mtls_endpoint_aliases&&(i=this.issuer.mtls_endpoint_aliases[`${e}_endpoint`]),i=i||this.issuer[`${e}_endpoint`],"form"in a)for(let[e,t]of Object.entries(a.form))void 0===t&&delete a.form[e];return c.call(this,{...a,method:"POST",url:i,headers:{..."revocation"!==e?{Accept:"application/json"}:void 0,...a.headers}},{mTLS:s,DPoP:o})}e.exports={resolveResponseType:function(){let{length:e,0:t}=this.response_types;if(1===e)return t},resolveRedirectUri:function(){let{length:e,0:t}=this.redirect_uris||[];if(1===e)return t},authFor:p,authenticatedPost:h}},49701:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return{headers:[{key:"Content-Type",value:"application/json"}],body:e.reduce((e,{id:t,name:r,type:n,signinUrl:o,callbackUrl:i})=>(e[t]={id:t,name:r,type:n,signinUrl:o,callbackUrl:i},e),{})}}},50003:(e,t)=>{"use strict";function r(e,t="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${t} must be ${e}`)}function n(e,t){return e.name===t}function o(e){return parseInt(e.name.slice(4),10)}function i(e,t){if(t.length&&!t.some(t=>e.usages.includes(t))){let e="CryptoKey does not support this operation, its usages must include ";if(t.length>2){let r=t.pop();e+=`one of ${t.join(", ")}, or ${r}.`}else 2===t.length?e+=`one of ${t[0]} or ${t[1]}.`:e+=`${t[0]}.`;throw TypeError(e)}}Object.defineProperty(t,"__esModule",{value:!0}),t.checkEncCryptoKey=t.checkSigCryptoKey=void 0,t.checkSigCryptoKey=function(e,t,...a){switch(t){case"HS256":case"HS384":case"HS512":{if(!n(e.algorithm,"HMAC"))throw r("HMAC");let i=parseInt(t.slice(2),10);if(o(e.algorithm.hash)!==i)throw r(`SHA-${i}`,"algorithm.hash");break}case"RS256":case"RS384":case"RS512":{if(!n(e.algorithm,"RSASSA-PKCS1-v1_5"))throw r("RSASSA-PKCS1-v1_5");let i=parseInt(t.slice(2),10);if(o(e.algorithm.hash)!==i)throw r(`SHA-${i}`,"algorithm.hash");break}case"PS256":case"PS384":case"PS512":{if(!n(e.algorithm,"RSA-PSS"))throw r("RSA-PSS");let i=parseInt(t.slice(2),10);if(o(e.algorithm.hash)!==i)throw r(`SHA-${i}`,"algorithm.hash");break}case"EdDSA":if("Ed25519"!==e.algorithm.name&&"Ed448"!==e.algorithm.name)throw r("Ed25519 or Ed448");break;case"ES256":case"ES384":case"ES512":{if(!n(e.algorithm,"ECDSA"))throw r("ECDSA");let o=function(e){switch(e){case"ES256":return"P-256";case"ES384":return"P-384";case"ES512":return"P-521";default:throw Error("unreachable")}}(t);if(e.algorithm.namedCurve!==o)throw r(o,"algorithm.namedCurve");break}default:throw TypeError("CryptoKey does not support this operation")}i(e,a)},t.checkEncCryptoKey=function(e,t,...a){switch(t){case"A128GCM":case"A192GCM":case"A256GCM":{if(!n(e.algorithm,"AES-GCM"))throw r("AES-GCM");let o=parseInt(t.slice(1,4),10);if(e.algorithm.length!==o)throw r(o,"algorithm.length");break}case"A128KW":case"A192KW":case"A256KW":{if(!n(e.algorithm,"AES-KW"))throw r("AES-KW");let o=parseInt(t.slice(1,4),10);if(e.algorithm.length!==o)throw r(o,"algorithm.length");break}case"ECDH":switch(e.algorithm.name){case"ECDH":case"X25519":case"X448":break;default:throw r("ECDH, X25519, or X448")}break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":if(!n(e.algorithm,"PBKDF2"))throw r("PBKDF2");break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{if(!n(e.algorithm,"RSA-OAEP"))throw r("RSA-OAEP");let i=parseInt(t.slice(9),10)||1;if(o(e.algorithm.hash)!==i)throw r(`SHA-${i}`,"algorithm.hash");break}default:throw TypeError("CryptoKey does not support this operation")}i(e,a)}},50671:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.pkce=t.nonce=t.PKCE_CODE_CHALLENGE_METHOD=void 0,t.signCookie=a,t.state=void 0;var n=r(12107),o=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=i(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&({}).hasOwnProperty.call(e,a)){var s=o?Object.getOwnPropertyDescriptor(e,a):null;s&&(s.get||s.set)?Object.defineProperty(n,a,s):n[a]=e[a]}return n.default=e,r&&r.set(e,n),n}(r(15912));function i(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(i=function(e){return e?r:t})(e)}async function a(e,t,r,n){let{cookies:i,logger:a}=n;a.debug(`CREATE_${e.toUpperCase()}`,{value:t,maxAge:r});let{name:s}=i[e],c=new Date;return c.setTime(c.getTime()+1e3*r),{name:s,value:await o.encode({...n.jwt,maxAge:r,token:{value:t},salt:s}),options:{...i[e].options,expires:c}}}let s=t.PKCE_CODE_CHALLENGE_METHOD="S256";t.pkce={async create(e,t,r){var o,i;if(!(null!=(o=e.provider)&&null!=(o=o.checks)&&o.includes("pkce")))return;let c=n.generators.codeVerifier();r.code_challenge=n.generators.codeChallenge(c),r.code_challenge_method=s;let l=null!=(i=e.cookies.pkceCodeVerifier.options.maxAge)?i:900;t.push(await a("pkceCodeVerifier",c,l,e))},async use(e,t,r,n){var i;if(!(null!=(i=r.provider)&&null!=(i=i.checks)&&i.includes("pkce")))return;let a=null==e?void 0:e[r.cookies.pkceCodeVerifier.name];if(!a)throw TypeError("PKCE code_verifier cookie was missing.");let{name:s}=r.cookies.pkceCodeVerifier,c=await o.decode({...r.jwt,token:a,salt:s});if(!(null!=c&&c.value))throw TypeError("PKCE code_verifier value could not be parsed.");t.push({name:s,value:"",options:{...r.cookies.pkceCodeVerifier.options,maxAge:0}}),n.code_verifier=c.value}},t.state={async create(e,t,r){var o,i;if(!(null!=(o=e.provider.checks)&&o.includes("state")))return;let s=n.generators.state();r.state=s;let c=null!=(i=e.cookies.state.options.maxAge)?i:900;t.push(await a("state",s,c,e))},async use(e,t,r,n){var i;if(!(null!=(i=r.provider.checks)&&i.includes("state")))return;let a=null==e?void 0:e[r.cookies.state.name];if(!a)throw TypeError("State cookie was missing.");let{name:s}=r.cookies.state,c=await o.decode({...r.jwt,token:a,salt:s});if(!(null!=c&&c.value))throw TypeError("State value could not be parsed.");t.push({name:s,value:"",options:{...r.cookies.state.options,maxAge:0}}),n.state=c.value}},t.nonce={async create(e,t,r){var o,i;if(!(null!=(o=e.provider.checks)&&o.includes("nonce")))return;let s=n.generators.nonce();r.nonce=s;let c=null!=(i=e.cookies.nonce.options.maxAge)?i:900;t.push(await a("nonce",s,c,e))},async use(e,t,r,n){var i;if(!(null!=(i=r.provider)&&null!=(i=i.checks)&&i.includes("nonce")))return;let a=null==e?void 0:e[r.cookies.nonce.name];if(!a)throw TypeError("Nonce cookie was missing.");let{name:s}=r.cookies.nonce,c=await o.decode({...r.jwt,token:a,salt:s});if(!(null!=c&&c.value))throw TypeError("Nonce value could not be parsed.");t.push({name:s,value:"",options:{...r.cookies.nonce.options,maxAge:0}}),n.nonce=c.value}}},51688:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.generateKeyPair=t.generateSecret=void 0;let n=r(55511),o=r(28354),i=r(7552),a=r(78328),s=r(99938),c=(0,o.promisify)(n.generateKeyPair);t.generateSecret=async function(e,t){let r;switch(e){case"HS256":case"HS384":case"HS512":case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":r=parseInt(e.slice(-3),10);break;case"A128KW":case"A192KW":case"A256KW":case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":case"A128GCM":case"A192GCM":case"A256GCM":r=parseInt(e.slice(1,4),10);break;default:throw new s.JOSENotSupported('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}return(0,n.createSecretKey)((0,i.default)(new Uint8Array(r>>3)))},t.generateKeyPair=async function(e,t){var r,n;switch(e){case"RS256":case"RS384":case"RS512":case"PS256":case"PS384":case"PS512":case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":case"RSA1_5":{let e=null!=(r=null==t?void 0:t.modulusLength)?r:2048;if("number"!=typeof e||e<2048)throw new s.JOSENotSupported("Invalid or unsupported modulusLength option provided, 2048 bits or larger keys must be used");let n=await c("rsa",{modulusLength:e,publicExponent:65537});return(0,a.setModulusLength)(n.privateKey,e),(0,a.setModulusLength)(n.publicKey,e),n}case"ES256":return c("ec",{namedCurve:"P-256"});case"ES256K":return c("ec",{namedCurve:"secp256k1"});case"ES384":return c("ec",{namedCurve:"P-384"});case"ES512":return c("ec",{namedCurve:"P-521"});case"EdDSA":switch(null==t?void 0:t.crv){case void 0:case"Ed25519":return c("ed25519");case"Ed448":return c("ed448");default:throw new s.JOSENotSupported("Invalid or unsupported crv option provided, supported values are Ed25519 and Ed448")}case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":let o=null!=(n=null==t?void 0:t.crv)?n:"P-256";switch(o){case void 0:case"P-256":case"P-384":case"P-521":return c("ec",{namedCurve:o});case"X25519":return c("x25519");case"X448":return c("x448");default:throw new s.JOSENotSupported("Invalid or unsupported crv option provided, supported values are P-256, P-384, P-521, X25519, and X448")}default:throw new s.JOSENotSupported('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}}},51800:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(99938),o=r(90061);t.default=(e,t)=>{let r;switch(e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":r=parseInt(e.slice(-3),10);break;case"A128GCM":case"A192GCM":case"A256GCM":r=parseInt(e.slice(1,4),10);break;default:throw new n.JOSENotSupported(`Content Encryption Algorithm ${e} is not supported either by JOSE or your javascript runtime`)}if(t instanceof Uint8Array){let e=t.byteLength<<3;if(e!==r)throw new n.JWEInvalid(`Invalid Content Encryption Key length. Expected ${r} bits, got ${e} bits`);return}if((0,o.default)(t)&&"secret"===t.type){let e=t.symmetricKeySize<<3;if(e!==r)throw new n.JWEInvalid(`Invalid Content Encryption Key length. Expected ${r} bits, got ${e} bits`);return}throw TypeError("Invalid Content Encryption Key type")}},52118:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CompactEncrypt=void 0;let n=r(14988);class o{constructor(e){this._flattened=new n.FlattenedEncrypt(e)}setContentEncryptionKey(e){return this._flattened.setContentEncryptionKey(e),this}setInitializationVector(e){return this._flattened.setInitializationVector(e),this}setProtectedHeader(e){return this._flattened.setProtectedHeader(e),this}setKeyManagementParameters(e){return this._flattened.setKeyManagementParameters(e),this}async encrypt(e,t){let r=await this._flattened.encrypt(e,t);return[r.protected,r.encrypted_key,r.iv,r.ciphertext,r.tag].join(".")}}t.CompactEncrypt=o},52131:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ecdhAllowed=t.generateEpk=t.deriveKey=void 0;let n=r(55511),o=r(28354),i=r(62909),a=r(2594),s=r(99938),c=r(17996),l=r(50003),u=r(90061),d=r(78680),f=r(37265),p=(0,o.promisify)(n.generateKeyPair);t.deriveKey=async function(e,t,r,o,i=new Uint8Array(0),s=new Uint8Array(0)){let p,h;if((0,c.isCryptoKey)(e))(0,l.checkEncCryptoKey)(e,"ECDH"),p=n.KeyObject.from(e);else if((0,u.default)(e))p=e;else throw TypeError((0,d.default)(e,...f.types));if((0,c.isCryptoKey)(t))(0,l.checkEncCryptoKey)(t,"ECDH","deriveBits"),h=n.KeyObject.from(t);else if((0,u.default)(t))h=t;else throw TypeError((0,d.default)(t,...f.types));let y=(0,a.concat)((0,a.lengthAndInput)(a.encoder.encode(r)),(0,a.lengthAndInput)(i),(0,a.lengthAndInput)(s),(0,a.uint32be)(o)),_=(0,n.diffieHellman)({privateKey:h,publicKey:p});return(0,a.concatKdf)(_,o,y)},t.generateEpk=async function(e){let t;if((0,c.isCryptoKey)(e))t=n.KeyObject.from(e);else if((0,u.default)(e))t=e;else throw TypeError((0,d.default)(e,...f.types));switch(t.asymmetricKeyType){case"x25519":return p("x25519");case"x448":return p("x448");case"ec":return p("ec",{namedCurve:(0,i.default)(t)});default:throw new s.JOSENotSupported("Invalid or unsupported EPK")}},t.ecdhAllowed=e=>["P-256","P-384","P-521","X25519","X448"].includes((0,i.default)(e))},52645:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.decodeJwt=void 0;let n=r(67929),o=r(2594),i=r(25110),a=r(99938);t.decodeJwt=function(e){let t,r;if("string"!=typeof e)throw new a.JWTInvalid("JWTs must use Compact JWS serialization, JWT must be a string");let{1:s,length:c}=e.split(".");if(5===c)throw new a.JWTInvalid("Only JWTs using Compact JWS serialization can be decoded");if(3!==c)throw new a.JWTInvalid("Invalid JWT");if(!s)throw new a.JWTInvalid("JWTs must contain a payload");try{t=(0,n.decode)(s)}catch{throw new a.JWTInvalid("Failed to base64url decode the payload")}try{r=JSON.parse(o.decoder.decode(t))}catch{throw new a.JWTInvalid("Failed to parse the decoded payload as JSON")}if(!(0,i.default)(r))throw new a.JWTInvalid("Invalid JWT Claims Set");return r}},53687:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(79428),o=r(55511),i=r(68327),a=r(99938),s=r(62909),c=r(78328),l=r(4275),u=r(80028);t.default=e=>{if(u.jwkImport&&"oct"!==e.kty)return e.d?(0,o.createPrivateKey)({format:"jwk",key:e}):(0,o.createPublicKey)({format:"jwk",key:e});switch(e.kty){case"oct":return(0,o.createSecretKey)((0,i.decode)(e.k));case"RSA":{let t=new l.default,r=void 0!==e.d,i=n.Buffer.from(e.n,"base64"),a=n.Buffer.from(e.e,"base64");r?(t.zero(),t.unsignedInteger(i),t.unsignedInteger(a),t.unsignedInteger(n.Buffer.from(e.d,"base64")),t.unsignedInteger(n.Buffer.from(e.p,"base64")),t.unsignedInteger(n.Buffer.from(e.q,"base64")),t.unsignedInteger(n.Buffer.from(e.dp,"base64")),t.unsignedInteger(n.Buffer.from(e.dq,"base64")),t.unsignedInteger(n.Buffer.from(e.qi,"base64"))):(t.unsignedInteger(i),t.unsignedInteger(a));let s={key:t.end(),format:"der",type:"pkcs1"},u=r?(0,o.createPrivateKey)(s):(0,o.createPublicKey)(s);return(0,c.setModulusLength)(u,i.length<<3),u}case"EC":{let t=new l.default,r=void 0!==e.d,i=n.Buffer.concat([n.Buffer.alloc(1,4),n.Buffer.from(e.x,"base64"),n.Buffer.from(e.y,"base64")]);if(r){t.zero();let r=new l.default;r.oidFor("ecPublicKey"),r.oidFor(e.crv),t.add(r.end());let a=new l.default;a.one(),a.octStr(n.Buffer.from(e.d,"base64"));let c=new l.default;c.bitStr(i);let u=c.end(n.Buffer.from([161]));a.add(u);let d=a.end(),f=new l.default;f.add(d);let p=f.end(n.Buffer.from([4]));t.add(p);let h=t.end(),y=(0,o.createPrivateKey)({key:h,format:"der",type:"pkcs8"});return(0,s.setCurve)(y,e.crv),y}let a=new l.default;a.oidFor("ecPublicKey"),a.oidFor(e.crv),t.add(a.end()),t.bitStr(i);let c=t.end(),u=(0,o.createPublicKey)({key:c,format:"der",type:"spki"});return(0,s.setCurve)(u,e.crv),u}case"OKP":{let t=new l.default;if(void 0!==e.d){t.zero();let r=new l.default;r.oidFor(e.crv),t.add(r.end());let i=new l.default;i.octStr(n.Buffer.from(e.d,"base64"));let a=i.end(n.Buffer.from([4]));t.add(a);let s=t.end();return(0,o.createPrivateKey)({key:s,format:"der",type:"pkcs8"})}let r=new l.default;r.oidFor(e.crv),t.add(r.end()),t.bitStr(n.Buffer.from(e.x,"base64"));let i=t.end();return(0,o.createPublicKey)({key:i,format:"der",type:"spki"})}default:throw new a.JOSENotSupported('Invalid or unsupported JWK "kty" (Key Type) Parameter value')}}},53873:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=l;var n=r(12107),o=r(9545),i=r(8365),a=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=c(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&({}).hasOwnProperty.call(e,i)){var a=o?Object.getOwnPropertyDescriptor(e,i):null;a&&(a.get||a.set)?Object.defineProperty(n,i,a):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}(r(50671)),s=r(20113);function c(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(c=function(e){return e?r:t})(e)}async function l(e){var t,r,c,l,d,f;let{options:p,query:h,body:y,method:_,cookies:g}=e,{logger:b,provider:m}=p,v=null!=(t=null==y?void 0:y.error)?t:null==h?void 0:h.error;if(v){let e=Error(v);throw b.error("OAUTH_CALLBACK_HANDLER_ERROR",{error:e,error_description:null==h?void 0:h.error_description,providerId:m.id}),b.debug("OAUTH_CALLBACK_HANDLER_ERROR",{body:y}),e}if(null!=(r=m.version)&&r.startsWith("1."))try{let e=await (0,i.oAuth1Client)(p),{oauth_token:t,oauth_verifier:r}=null!=h?h:{},n=await e.getOAuthAccessToken(t,i.oAuth1TokenStore.get(t),r),o=await e.get(m.profileUrl,n.oauth_token,n.oauth_token_secret);return"string"==typeof o&&(o=JSON.parse(o)),{...await u({profile:o,tokens:n,provider:m,logger:b}),cookies:[]}}catch(e){throw b.error("OAUTH_V1_GET_ACCESS_TOKEN_ERROR",e),e}null!=h&&h.oauth_token&&i.oAuth1TokenStore.delete(h.oauth_token);try{let e,t,r=await (0,o.openidClient)(p),i={},s=[];await a.state.use(g,s,p,i),await a.pkce.use(g,s,p,i),await a.nonce.use(g,s,p,i);let v={...r.callbackParams({url:`http://n?${new URLSearchParams(h)}`,body:y,method:_}),...null==(c=m.token)?void 0:c.params};if(null!=(l=m.token)&&l.request){let t=await m.token.request({provider:m,params:v,checks:i,client:r});e=new n.TokenSet(t.tokens)}else e=m.idToken?await r.callback(m.callbackUrl,v,i):await r.oauthCallback(m.callbackUrl,v,i);return Array.isArray(e.scope)&&(e.scope=e.scope.join(" ")),t=null!=(d=m.userinfo)&&d.request?await m.userinfo.request({provider:m,tokens:e,client:r}):m.idToken?e.claims():await r.userinfo(e,{params:null==(f=m.userinfo)?void 0:f.params}),{...await u({profile:t,provider:m,tokens:e,logger:b}),cookies:s}}catch(e){throw new s.OAuthCallbackError(e)}}async function u({profile:e,tokens:t,provider:r,logger:n}){try{var o;n.debug("PROFILE_DATA",{OAuthProfile:e});let i=await r.profile(e,t);if(i.email=null==(o=i.email)?void 0:o.toLowerCase(),!i.id)throw TypeError(`Profile id is missing in ${r.name} OAuth profile response`);return{profile:i,account:{provider:r.id,type:r.type,providerAccountId:i.id.toString(),...t},OAuthProfile:e}}catch(t){n.error("OAUTH_PARSE_PROFILE_ERROR",{error:t,OAuthProfile:e})}}},54688:(e,t)=>{"use strict";async function r(e){var t,r;let{options:n,sessionStore:o}=e,{adapter:i,events:a,jwt:s,callbackUrl:c,logger:l,session:u}=n,d=null==o?void 0:o.value;if(!d)return{redirect:c};if("jwt"===u.strategy)try{let e=await s.decode({...s,token:d});await (null==(t=a.signOut)?void 0:t.call(a,{token:e}))}catch(e){l.error("SIGNOUT_ERROR",e)}else try{let e=await i.deleteSession(d);await (null==(r=a.signOut)?void 0:r.call(a,{session:e}))}catch(e){l.error("SIGNOUT_ERROR",e)}return{redirect:c,cookies:o.clean()}}Object.defineProperty(t,"__esModule",{value:!0}),t.default=r},55132:e=>{e.exports.keystores=new WeakMap},55437:e=>{e.exports=function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports.default=e.exports},55920:(e,t,r)=>{"use strict";var n=r(9168);Object.defineProperty(t,"__esModule",{value:!0}),t.init=g;var o=r(55511),i=n(r(28510)),a=r(20113),s=n(r(64470)),c=r(29837),l=_(r(99632)),u=_(r(15912)),d=r(68588),f=r(89412),p=r(61665),h=n(r(16851));function y(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(y=function(e){return e?r:t})(e)}function _(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=y(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&({}).hasOwnProperty.call(e,i)){var a=o?Object.getOwnPropertyDescriptor(e,i):null;a&&(a.get||a.set)?Object.defineProperty(n,i,a):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}async function g({authOptions:e,providerId:t,action:r,origin:n,cookies:y,callbackUrl:_,csrfToken:g,isPost:b}){var m,v;let w=(0,h.default)(n),x=(0,c.createSecret)({authOptions:e,url:w}),{providers:k,provider:E}=(0,s.default)({providers:e.providers,url:w,providerId:t}),S={debug:!1,pages:{},theme:{colorScheme:"auto",logo:"",brandColor:"",buttonText:""},...e,url:w,action:r,provider:E,cookies:{...l.defaultCookies(null!=(m=e.useSecureCookies)?m:w.base.startsWith("https://")),...e.cookies},secret:x,providers:k,session:{strategy:e.adapter?"database":"jwt",maxAge:2592e3,updateAge:86400,generateSessionToken:()=>{var e;return null!=(e=null===o.randomUUID||void 0===o.randomUUID?void 0:(0,o.randomUUID)())?e:(0,o.randomBytes)(32).toString("hex")},...e.session},jwt:{secret:x,maxAge:2592e3,encode:u.encode,decode:u.decode,...e.jwt},events:(0,a.eventsErrorHandler)(null!=(v=e.events)?v:{},i.default),adapter:(0,a.adapterErrorHandler)(e.adapter,i.default),callbacks:{...d.defaultCallbacks,...e.callbacks},logger:i.default,callbackUrl:w.origin},A=[],{csrfToken:O,cookie:P,csrfTokenVerified:T}=(0,f.createCSRFToken)({options:S,cookieValue:null==y?void 0:y[S.cookies.csrfToken.name],isPost:b,bodyValue:g});S.csrfToken=O,S.csrfTokenVerified=T,P&&A.push({name:S.cookies.csrfToken.name,value:P,options:S.cookies.csrfToken.options});let{callbackUrl:C,callbackUrlCookie:j}=await (0,p.createCallbackUrl)({options:S,cookieValue:null==y?void 0:y[S.cookies.callbackUrl.name],paramValue:_});return S.callbackUrl=C,j&&A.push({name:S.cookies.callbackUrl.name,value:j,options:S.cookies.callbackUrl.options}),{options:S,cookies:A}}},56913:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.decrypt=t.encrypt=void 0;let n=r(55511),o=r(78328),i=r(17996),a=r(50003),s=r(90061),c=r(78680),l=r(37265),u=(e,t)=>{if("rsa"!==e.asymmetricKeyType)throw TypeError("Invalid key for this operation, its asymmetricKeyType must be rsa");(0,o.default)(e,t)},d=e=>{switch(e){case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":return n.constants.RSA_PKCS1_OAEP_PADDING;case"RSA1_5":return n.constants.RSA_PKCS1_PADDING;default:return}},f=e=>{switch(e){case"RSA-OAEP":return"sha1";case"RSA-OAEP-256":return"sha256";case"RSA-OAEP-384":return"sha384";case"RSA-OAEP-512":return"sha512";default:return}};function p(e,t,...r){if((0,s.default)(e))return e;if((0,i.isCryptoKey)(e))return(0,a.checkEncCryptoKey)(e,t,...r),n.KeyObject.from(e);throw TypeError((0,c.default)(e,...l.types))}t.encrypt=(e,t,r)=>{let o=d(e),i=f(e),a=p(t,e,"wrapKey","encrypt");return u(a,e),(0,n.publicEncrypt)({key:a,oaepHash:i,padding:o},r)},t.decrypt=(e,t,r)=>{let o=d(e),i=f(e),a=p(t,e,"unwrapKey","decrypt");return u(a,e),(0,n.privateDecrypt)({key:a,oaepHash:i,padding:o},r)}},57502:e=>{function t(r){return e.exports=t=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},e.exports.__esModule=!0,e.exports.default=e.exports,t(r)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},58513:(e,t,r)=>{let n=r(79551),{format:o}=r(28354),i=r(93929),{RPError:a,OPError:s}=r(61408),{BaseClient:c}=r(83872),{random:l,codeChallenge:u}=r(35571),d=r(24740),{resolveResponseType:f,resolveRedirectUri:p}=r(49450);function h(e,t,r={}){e?this.error(e):t?this.success(t,r):this.fail(r)}function y({client:e,params:t={},passReqToCallback:r=!1,sessionKey:o,usePKCE:a=!0,extras:s={}}={},l){if(!(e instanceof c))throw TypeError("client must be an instance of openid-client Client");if("function"!=typeof l)throw TypeError("verify callback must be a function");if(!e.issuer||!e.issuer.issuer)throw TypeError("client must have an issuer with an identifier");if(this._client=e,this._issuer=e.issuer,this._verify=l,this._passReqToCallback=r,this._usePKCE=a,this._key=o||`oidc:${n.parse(this._issuer.issuer).hostname}`,this._params=i(t),delete this._params.state,delete this._params.nonce,this._extras=i(s),this._params.response_type||(this._params.response_type=f.call(e)),this._params.redirect_uri||(this._params.redirect_uri=p.call(e)),this._params.scope||(this._params.scope="openid"),!0===this._usePKCE){let e=!!Array.isArray(this._issuer.code_challenge_methods_supported)&&this._issuer.code_challenge_methods_supported;if(e&&e.includes("S256"))this._usePKCE="S256";else if(e&&e.includes("plain"))this._usePKCE="plain";else if(e)throw TypeError("neither code_challenge_method supported by the client is supported by the issuer");else this._usePKCE="S256"}else if("string"==typeof this._usePKCE&&!["plain","S256"].includes(this._usePKCE))throw TypeError(`${this._usePKCE} is not valid/implemented PKCE code_challenge_method`);this.name=n.parse(e.issuer.issuer).hostname}y.prototype.authenticate=function(e,t){(async()=>{let r=this._client;if(!e.session)throw TypeError("authentication requires session support");let n=r.callbackParams(e),i=this._key,{0:s,length:c}=Object.keys(n);if(0===c||1===c&&"iss"===s){let n={state:l(),...this._params,...t};if(!n.nonce&&n.response_type.includes("id_token")&&(n.nonce=l()),e.session[i]=d(n,"nonce","state","max_age","response_type"),this._usePKCE&&n.response_type.includes("code")){let t=l();switch(e.session[i].code_verifier=t,this._usePKCE){case"S256":n.code_challenge=u(t),n.code_challenge_method="S256";break;case"plain":n.code_challenge=t}}this.redirect(r.authorizationUrl(n));return}let f=e.session[i];if(0===Object.keys(f||{}).length)throw Error(o('did not find expected authorization request details in session, req.session["%s"] is %j',i,f));let{state:p,nonce:y,max_age:_,code_verifier:g,response_type:b}=f;try{delete e.session[i]}catch(e){}let m={redirect_uri:this._params.redirect_uri,...t},v=await r.callback(m.redirect_uri,n,{state:p,nonce:y,max_age:_,code_verifier:g,response_type:b},this._extras),w=this._passReqToCallback,x=this._verify.length>(w?3:2)&&r.issuer.userinfo_endpoint,k=[v,h.bind(this)];if(x){if(!v.access_token)throw new a({message:"expected access_token to be returned when asking for userinfo in verify callback",tokenset:v});let e=await r.userinfo(v);k.splice(1,0,e)}w&&k.unshift(e),this._verify(...k)})().catch(e=>{e instanceof s&&"server_error"!==e.error&&!e.error.startsWith("invalid")||e instanceof a?this.fail(e):this.error(e)})},e.exports=y},58985:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=i;var n=r(55511),o=r(29837);async function i(e,t){var r,i,a,s;let{url:c,adapter:l,provider:u,callbackUrl:d,theme:f}=t,p=null!=(r=await (null==(i=u.generateVerificationToken)?void 0:i.call(u)))?r:(0,n.randomBytes)(32).toString("hex"),h=new Date(Date.now()+(null!=(a=u.maxAge)?a:86400)*1e3),y=new URLSearchParams({callbackUrl:d,token:p,email:e}),_=`${c}/callback/${u.id}?${y}`;return await Promise.all([u.sendVerificationRequest({identifier:e,token:p,expires:h,url:_,provider:u,theme:f}),null==(s=l.createVerificationToken)?void 0:s.call(l,{identifier:e,token:(0,o.hashToken)(p,t),expires:h})]),`${c}/verify-request?${new URLSearchParams({provider:u.id,type:u.type})}`}},59310:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.FlattenedSign=void 0;let n=r(68327),o=r(92562),i=r(65597),a=r(99938),s=r(2594),c=r(80335),l=r(8535);class u{constructor(e){if(!(e instanceof Uint8Array))throw TypeError("payload must be an instance of Uint8Array");this._payload=e}setProtectedHeader(e){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=e,this}setUnprotectedHeader(e){if(this._unprotectedHeader)throw TypeError("setUnprotectedHeader can only be called once");return this._unprotectedHeader=e,this}async sign(e,t){let r;if(!this._protectedHeader&&!this._unprotectedHeader)throw new a.JWSInvalid("either setProtectedHeader or setUnprotectedHeader must be called before #sign()");if(!(0,i.default)(this._protectedHeader,this._unprotectedHeader))throw new a.JWSInvalid("JWS Protected and JWS Unprotected Header Parameter names must be disjoint");let u={...this._protectedHeader,...this._unprotectedHeader},d=(0,l.default)(a.JWSInvalid,new Map([["b64",!0]]),null==t?void 0:t.crit,this._protectedHeader,u),f=!0;if(d.has("b64")&&"boolean"!=typeof(f=this._protectedHeader.b64))throw new a.JWSInvalid('The "b64" (base64url-encode payload) Header Parameter must be a boolean');let{alg:p}=u;if("string"!=typeof p||!p)throw new a.JWSInvalid('JWS "alg" (Algorithm) Header Parameter missing or invalid');(0,c.default)(p,e,"sign");let h=this._payload;f&&(h=s.encoder.encode((0,n.encode)(h))),r=this._protectedHeader?s.encoder.encode((0,n.encode)(JSON.stringify(this._protectedHeader))):s.encoder.encode("");let y=(0,s.concat)(r,s.encoder.encode("."),h),_=await (0,o.default)(p,e,y),g={signature:(0,n.encode)(_),payload:""};return f&&(g.payload=s.decoder.decode(h)),this._unprotectedHeader&&(g.header=this._unprotectedHeader),this._protectedHeader&&(g.protected=s.decoder.decode(r)),g}}t.FlattenedSign=u},59487:(e,t,r)=>{"use strict";var n=r(9168);Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"callback",{enumerable:!0,get:function(){return o.default}}),Object.defineProperty(t,"providers",{enumerable:!0,get:function(){return c.default}}),Object.defineProperty(t,"session",{enumerable:!0,get:function(){return s.default}}),Object.defineProperty(t,"signin",{enumerable:!0,get:function(){return i.default}}),Object.defineProperty(t,"signout",{enumerable:!0,get:function(){return a.default}});var o=n(r(99296)),i=n(r(22429)),a=n(r(54688)),s=n(r(71871)),c=n(r(49701))},59496:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=i;var n=r(20113),o=r(29837);async function i(e){var t,r,i,a,s,c;let{sessionToken:l,profile:u,account:d,options:f}=e;if(!(null!=d&&d.providerAccountId)||!d.type)throw Error("Missing or invalid provider account");if(!["email","oauth"].includes(d.type))throw Error("Provider not supported");let{adapter:p,jwt:h,events:y,session:{strategy:_,generateSessionToken:g}}=f;if(!p)return{user:u,account:d};let{createUser:b,updateUser:m,getUser:v,getUserByAccount:w,getUserByEmail:x,linkAccount:k,createSession:E,getSessionAndUser:S,deleteSession:A}=p,O=null,P=null,T=!1,C="jwt"===_;if(l)if(C)try{(O=await h.decode({...h,token:l}))&&"sub"in O&&O.sub&&(P=await v(O.sub))}catch(e){}else{let e=await S(l);e&&(O=e.session,P=e.user)}if("email"===d.type){let e=await x(u.email);if(e)(null==(t=P)?void 0:t.id)!==e.id&&!C&&l&&await A(l),P=await m({id:e.id,emailVerified:new Date}),await (null==(r=y.updateUser)?void 0:r.call(y,{user:P}));else{let{id:e,...t}={...u,emailVerified:new Date};P=await b(t),await (null==(i=y.createUser)?void 0:i.call(y,{user:P})),T=!0}return{session:O=C?{}:await E({sessionToken:await g(),userId:P.id,expires:(0,o.fromDate)(f.session.maxAge)}),user:P,isNewUser:T}}if("oauth"===d.type){let e=await w({providerAccountId:d.providerAccountId,provider:d.provider});if(e){if(P){if(e.id===P.id)return{session:O,user:P,isNewUser:T};throw new n.AccountNotLinkedError("The account is already associated with another user")}return{session:O=C?{}:await E({sessionToken:await g(),userId:e.id,expires:(0,o.fromDate)(f.session.maxAge)}),user:e,isNewUser:T}}{if(P)return await k({...d,userId:P.id}),await (null==(c=y.linkAccount)?void 0:c.call(y,{user:P,account:d,profile:u})),{session:O,user:P,isNewUser:T};let e=u.email?await x(u.email):null;if(e){let t=f.provider;if(null!=t&&t.allowDangerousEmailAccountLinking)P=e;else throw new n.AccountNotLinkedError("Another account already exists with the same e-mail address")}else{let{id:e,...t}={...u,emailVerified:null};P=await b(t)}return await (null==(a=y.createUser)?void 0:a.call(y,{user:P})),await k({...d,userId:P.id}),await (null==(s=y.linkAccount)?void 0:s.call(y,{user:P,account:d,profile:u})),{session:O=C?{}:await E({sessionToken:await g(),userId:P.id,expires:(0,o.fromDate)(f.session.maxAge)}),user:P,isNewUser:!0}}}throw Error("Unsupported account type")}},60105:e=>{e.exports=function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e},e.exports.__esModule=!0,e.exports.default=e.exports},61408:(e,t,r)=>{let{format:n}=r(28354);class o extends Error{constructor({error_description:e,error:t,error_uri:r,session_state:n,state:o,scope:i},a){super(e?`${t} (${e})`:t),Object.assign(this,{error:t},e&&{error_description:e},r&&{error_uri:r},o&&{state:o},i&&{scope:i},n&&{session_state:n}),a&&Object.defineProperty(this,"response",{value:a}),this.name=this.constructor.name,Error.captureStackTrace(this,this.constructor)}}class i extends Error{constructor(...e){if("string"==typeof e[0])super(n(...e));else{let{message:t,printf:r,response:o,...i}=e[0];r?super(n(...r)):super(t),Object.assign(this,i),o&&Object.defineProperty(this,"response",{value:o})}this.name=this.constructor.name,Error.captureStackTrace(this,this.constructor)}}e.exports={OPError:o,RPError:i}},61665:(e,t)=>{"use strict";async function r({options:e,paramValue:t,cookieValue:r}){let{url:n,callbacks:o}=e,i=n.origin;return t?i=await o.redirect({url:t,baseUrl:n.origin}):r&&(i=await o.redirect({url:r,baseUrl:n.origin})),{callbackUrl:i,callbackUrlCookie:i!==r?i:void 0}}Object.defineProperty(t,"__esModule",{value:!0}),t.createCallbackUrl=r},61689:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.bitLength=void 0;let n=r(99938),o=r(7552);function i(e){switch(e){case"A128GCM":return 128;case"A192GCM":return 192;case"A256GCM":case"A128CBC-HS256":return 256;case"A192CBC-HS384":return 384;case"A256CBC-HS512":return 512;default:throw new n.JOSENotSupported(`Unsupported JWE Algorithm: ${e}`)}}t.bitLength=i,t.default=e=>(0,o.default)(new Uint8Array(i(e)>>3))},62197:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.decrypt=t.encrypt=void 0;let n=r(28354),o=r(55511),i=r(7552),a=r(2594),s=r(68327),c=r(88062),l=r(92296),u=r(17996),d=r(50003),f=r(90061),p=r(78680),h=r(37265),y=(0,n.promisify)(o.pbkdf2);function _(e,t){if((0,f.default)(e))return e.export();if(e instanceof Uint8Array)return e;if((0,u.isCryptoKey)(e))return(0,d.checkEncCryptoKey)(e,t,"deriveBits","deriveKey"),o.KeyObject.from(e).export();throw TypeError((0,p.default)(e,...h.types,"Uint8Array"))}t.encrypt=async(e,t,r,n=2048,o=(0,i.default)(new Uint8Array(16)))=>{(0,l.default)(o);let u=(0,a.p2s)(e,o),d=parseInt(e.slice(13,16),10)>>3,f=_(t,e),p=await y(f,u,n,d,`sha${e.slice(8,11)}`);return{encryptedKey:await (0,c.wrap)(e.slice(-6),p,r),p2c:n,p2s:(0,s.encode)(o)}},t.decrypt=async(e,t,r,n,o)=>{(0,l.default)(o);let i=(0,a.p2s)(e,o),s=parseInt(e.slice(13,16),10)>>3,u=_(t,e),d=await y(u,i,n,s,`sha${e.slice(8,11)}`);return(0,c.unwrap)(e.slice(-6),d,r)}},62909:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setCurve=t.weakMap=void 0;let n=r(79428),o=r(55511),i=r(99938),a=r(17996),s=r(90061),c=r(78680),l=r(37265),u=n.Buffer.from([42,134,72,206,61,3,1,7]),d=n.Buffer.from([43,129,4,0,34]),f=n.Buffer.from([43,129,4,0,35]),p=n.Buffer.from([43,129,4,0,10]);t.weakMap=new WeakMap;let h=e=>{switch(e){case"prime256v1":return"P-256";case"secp384r1":return"P-384";case"secp521r1":return"P-521";case"secp256k1":return"secp256k1";default:throw new i.JOSENotSupported("Unsupported key curve for this operation")}},y=(e,r)=>{var n;let _;if((0,a.isCryptoKey)(e))_=o.KeyObject.from(e);else if((0,s.default)(e))_=e;else throw TypeError((0,c.default)(e,...l.types));if("secret"===_.type)throw TypeError('only "private" or "public" type keys can be used for this operation');switch(_.asymmetricKeyType){case"ed25519":case"ed448":return`Ed${_.asymmetricKeyType.slice(2)}`;case"x25519":case"x448":return`X${_.asymmetricKeyType.slice(1)}`;case"ec":{if(t.weakMap.has(_))return t.weakMap.get(_);let e=null==(n=_.asymmetricKeyDetails)?void 0:n.namedCurve;if(e||"private"!==_.type){if(!e){let t=_.export({format:"der",type:"spki"}),r=t[1]<128?14:15,n=t[r],o=t.slice(r+1,r+1+n);if(o.equals(u))e="prime256v1";else if(o.equals(d))e="secp384r1";else if(o.equals(f))e="secp521r1";else if(o.equals(p))e="secp256k1";else throw new i.JOSENotSupported("Unsupported key curve for this operation")}}else e=y((0,o.createPublicKey)(_),!0);if(r)return e;let a=h(e);return t.weakMap.set(_,a),a}default:throw TypeError("Invalid asymmetric key type for this operation")}};t.setCurve=function(e,r){t.weakMap.set(e,r)},t.default=y},63580:e=>{"use strict";e.exports=function(e){e.prototype[Symbol.iterator]=function*(){for(let e=this.head;e;e=e.next)yield e.value}}},63667:e=>{"use strict";e.exports=JSON.parse('{"name":"openid-client","version":"5.7.1","description":"OpenID Connect Relying Party (RP, Client) implementation for Node.js runtime, supports passportjs","keywords":["auth","authentication","basic","certified","client","connect","dynamic","electron","hybrid","identity","implicit","oauth","oauth2","oidc","openid","passport","relying party","strategy"],"homepage":"https://github.com/panva/openid-client","repository":"panva/openid-client","funding":{"url":"https://github.com/sponsors/panva"},"license":"MIT","author":"Filip Skokan <<EMAIL>>","exports":{"types":"./types/index.d.ts","import":"./lib/index.mjs","require":"./lib/index.js"},"main":"./lib/index.js","types":"./types/index.d.ts","files":["lib","types/index.d.ts"],"scripts":{"format":"npx prettier --loglevel silent --write ./lib ./test ./certification ./types","test":"mocha test/**/*.test.js"},"dependencies":{"jose":"^4.15.9","lru-cache":"^6.0.0","object-hash":"^2.2.0","oidc-token-hash":"^5.0.3"},"devDependencies":{"@types/node":"^16.18.106","@types/passport":"^1.0.16","base64url":"^3.0.1","chai":"^4.5.0","mocha":"^10.7.3","nock":"^13.5.5","prettier":"^2.8.8","readable-mock-req":"^0.2.2","sinon":"^9.2.4","timekeeper":"^2.3.1"},"standard-version":{"scripts":{"postchangelog":"sed -i \'\' -e \'s/### \\\\[/## [/g\' CHANGELOG.md"},"types":[{"type":"feat","section":"Features"},{"type":"fix","section":"Fixes"},{"type":"chore","hidden":true},{"type":"docs","hidden":true},{"type":"style","hidden":true},{"type":"refactor","section":"Refactor","hidden":false},{"type":"perf","section":"Performance","hidden":false},{"type":"test","hidden":true}]}}')},63898:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(99938);t.default=function(e){switch(e){case"PS256":case"RS256":case"ES256":case"ES256K":return"sha256";case"PS384":case"RS384":case"ES384":return"sha384";case"PS512":case"RS512":case"ES512":return"sha512";case"EdDSA":return;default:throw new n.JOSENotSupported(`alg ${e} is not supported either by JOSE or your javascript runtime`)}}},64127:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.GeneralEncrypt=void 0;let n=r(14988),o=r(99938),i=r(61689),a=r(65597),s=r(22599),c=r(68327),l=r(8535);class u{constructor(e,t,r){this.parent=e,this.key=t,this.options=r}setUnprotectedHeader(e){if(this.unprotectedHeader)throw TypeError("setUnprotectedHeader can only be called once");return this.unprotectedHeader=e,this}addRecipient(...e){return this.parent.addRecipient(...e)}encrypt(...e){return this.parent.encrypt(...e)}done(){return this.parent}}class d{constructor(e){this._recipients=[],this._plaintext=e}addRecipient(e,t){let r=new u(this,e,{crit:null==t?void 0:t.crit});return this._recipients.push(r),r}setProtectedHeader(e){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=e,this}setSharedUnprotectedHeader(e){if(this._unprotectedHeader)throw TypeError("setSharedUnprotectedHeader can only be called once");return this._unprotectedHeader=e,this}setAdditionalAuthenticatedData(e){return this._aad=e,this}async encrypt(e){var t,r,u;let d;if(!this._recipients.length)throw new o.JWEInvalid("at least one recipient must be added");if(e={deflateRaw:null==e?void 0:e.deflateRaw},1===this._recipients.length){let[t]=this._recipients,r=await new n.FlattenedEncrypt(this._plaintext).setAdditionalAuthenticatedData(this._aad).setProtectedHeader(this._protectedHeader).setSharedUnprotectedHeader(this._unprotectedHeader).setUnprotectedHeader(t.unprotectedHeader).encrypt(t.key,{...t.options,...e}),o={ciphertext:r.ciphertext,iv:r.iv,recipients:[{}],tag:r.tag};return r.aad&&(o.aad=r.aad),r.protected&&(o.protected=r.protected),r.unprotected&&(o.unprotected=r.unprotected),r.encrypted_key&&(o.recipients[0].encrypted_key=r.encrypted_key),r.header&&(o.recipients[0].header=r.header),o}for(let e=0;e<this._recipients.length;e++){let t=this._recipients[e];if(!(0,a.default)(this._protectedHeader,this._unprotectedHeader,t.unprotectedHeader))throw new o.JWEInvalid("JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint");let r={...this._protectedHeader,...this._unprotectedHeader,...t.unprotectedHeader},{alg:n}=r;if("string"!=typeof n||!n)throw new o.JWEInvalid('JWE "alg" (Algorithm) Header Parameter missing or invalid');if("dir"===n||"ECDH-ES"===n)throw new o.JWEInvalid('"dir" and "ECDH-ES" alg may only be used with a single recipient');if("string"!=typeof r.enc||!r.enc)throw new o.JWEInvalid('JWE "enc" (Encryption Algorithm) Header Parameter missing or invalid');if(d){if(d!==r.enc)throw new o.JWEInvalid('JWE "enc" (Encryption Algorithm) Header Parameter must be the same for all recipients')}else d=r.enc;if((0,l.default)(o.JWEInvalid,new Map,t.options.crit,this._protectedHeader,r),void 0!==r.zip&&(!this._protectedHeader||!this._protectedHeader.zip))throw new o.JWEInvalid('JWE "zip" (Compression Algorithm) Header MUST be integrity protected')}let f=(0,i.default)(d),p={ciphertext:"",iv:"",recipients:[],tag:""};for(let o=0;o<this._recipients.length;o++){let i=this._recipients[o],a={};p.recipients.push(a);let l=({...this._protectedHeader,...this._unprotectedHeader,...i.unprotectedHeader}).alg.startsWith("PBES2")?2048+o:void 0;if(0===o){let t=await new n.FlattenedEncrypt(this._plaintext).setAdditionalAuthenticatedData(this._aad).setContentEncryptionKey(f).setProtectedHeader(this._protectedHeader).setSharedUnprotectedHeader(this._unprotectedHeader).setUnprotectedHeader(i.unprotectedHeader).setKeyManagementParameters({p2c:l}).encrypt(i.key,{...i.options,...e,[n.unprotected]:!0});p.ciphertext=t.ciphertext,p.iv=t.iv,p.tag=t.tag,t.aad&&(p.aad=t.aad),t.protected&&(p.protected=t.protected),t.unprotected&&(p.unprotected=t.unprotected),a.encrypted_key=t.encrypted_key,t.header&&(a.header=t.header);continue}let{encryptedKey:h,parameters:y}=await (0,s.default)((null==(t=i.unprotectedHeader)?void 0:t.alg)||(null==(r=this._protectedHeader)?void 0:r.alg)||(null==(u=this._unprotectedHeader)?void 0:u.alg),d,i.key,f,{p2c:l});a.encrypted_key=(0,c.encode)(h),(i.unprotectedHeader||y)&&(a.header={...i.unprotectedHeader,...y})}return p}}t.GeneralEncrypt=d},64150:e=>{let t=/^\d+$/;e.exports=function(e){let r;if("string"!=typeof e)throw TypeError("input must be a string");return(!function(e){if(e.includes("://"))return!0;let r=e.replace(/(\/|\?)/g,"#").split("#")[0];if(r.includes(":")){let e=r.indexOf(":"),n=r.slice(e+1);if(!t.test(n))return!0}return!1}(e)?!function(e){if(!e.includes("@"))return!1;let t=e.split("@"),r=t[t.length-1];return!(r.includes(":")||r.includes("/")||r.includes("?"))}(e)?`https://${e}`:`acct:${e}`:e).split("#")[0]}},64470:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){let{url:t,providerId:r}=e,i=e.providers.map(({options:e,...r})=>{var i,a;if("oauth"===r.type){let i=o(r),s=o(e,!0),c=null!=(a=null==s?void 0:s.id)?a:r.id;return(0,n.merge)(i,{...s,signinUrl:`${t}/signin/${c}`,callbackUrl:`${t}/callback/${c}`})}let s=null!=(i=null==e?void 0:e.id)?i:r.id;return(0,n.merge)(r,{...e,signinUrl:`${t}/signin/${s}`,callbackUrl:`${t}/callback/${s}`})});return{providers:i,provider:i.find(({id:e})=>e===r)}};var n=r(6680);function o(e,t=!1){var r,n,i,a,s;if(!e)return;let c=Object.entries(e).reduce((e,[t,r])=>{if(["authorization","token","userinfo"].includes(t)&&"string"==typeof r){var n;let o=new URL(r);e[t]={url:`${o.origin}${o.pathname}`,params:Object.fromEntries(null!=(n=o.searchParams)?n:[])}}else e[t]=r;return e},{});return t||null!=(r=c.version)&&r.startsWith("1.")||(c.idToken=!!(null!=(n=null!=(i=c.idToken)?i:null==(a=c.wellKnown)?void 0:a.includes("openid-configuration"))?n:null==(s=c.authorization)||null==(s=s.params)||null==(s=s.scope)?void 0:s.includes("openid")),c.checks||(c.checks=["state"])),c}},64872:(e,t,r)=>{"use strict";var n=r(9168);Object.defineProperty(t,"__esModule",{value:!0}),t.AuthHandler=_;var o=p(r(28510)),i=r(42856),a=p(r(59487)),s=n(r(70959)),c=r(55920),l=r(67804),u=r(99632),d=r(25538);function f(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(f=function(e){return e?r:t})(e)}function p(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=f(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&({}).hasOwnProperty.call(e,i)){var a=o?Object.getOwnPropertyDescriptor(e,i):null;a&&(a.get||a.set)?Object.defineProperty(n,i,a):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}async function h(e){try{return await e.json()}catch(e){}}async function y(e){var t,r,n,o;if(e instanceof Request){let t=new URL(e.url),a=t.pathname.split("/").slice(3),s=Object.fromEntries(e.headers),c=Object.fromEntries(t.searchParams);return c.nextauth=a,{action:a[0],method:e.method,headers:s,body:await h(e),cookies:(0,d.parse)(null!=(r=e.headers.get("cookie"))?r:""),providerId:a[1],error:null!=(n=t.searchParams.get("error"))?n:a[1],origin:(0,i.detectOrigin)(null!=(o=s["x-forwarded-host"])?o:s.host,s["x-forwarded-proto"]),query:c}}let{headers:a}=e,s=null!=(t=null==a?void 0:a["x-forwarded-host"])?t:null==a?void 0:a.host;return e.origin=(0,i.detectOrigin)(s,null==a?void 0:a["x-forwarded-proto"]),e}async function _(e){var t,r,n,i,d,f,p;let{options:h,req:_}=e,g=await y(_);(0,o.setLogger)(h.logger,h.debug);let b=(0,l.assertConfig)({options:h,req:g});if(Array.isArray(b))b.forEach(o.default.warn);else if(b instanceof Error){if(o.default.error(b.code,b),!["signin","signout","error","verify-request"].includes(g.action)||"GET"!==g.method)return{status:500,headers:[{key:"Content-Type",value:"application/json"}],body:{message:"There is a problem with the server configuration. Check the server logs for more information."}};let{pages:e,theme:t}=h,r=(null==e?void 0:e.error)&&(null==(d=g.query)||null==(d=d.callbackUrl)?void 0:d.startsWith(e.error));return!(null!=e&&e.error)||r?(r&&o.default.error("AUTH_ON_ERROR_PAGE_ERROR",Error(`The error page ${null==e?void 0:e.error} should not require authentication`)),(0,s.default)({theme:t}).error({error:"configuration"})):{redirect:`${e.error}?error=Configuration`}}let{action:m,providerId:v,error:w,method:x="GET"}=g,{options:k,cookies:E}=await (0,c.init)({authOptions:h,action:m,providerId:v,origin:g.origin,callbackUrl:null!=(t=null==(r=g.body)?void 0:r.callbackUrl)?t:null==(n=g.query)?void 0:n.callbackUrl,csrfToken:null==(i=g.body)?void 0:i.csrfToken,cookies:g.cookies,isPost:"POST"===x}),S=new u.SessionStore(k.cookies.sessionToken,g,k.logger);if("GET"===x){let e=(0,s.default)({...k,query:g.query,cookies:E}),{pages:t}=k;switch(m){case"providers":return await a.providers(k.providers);case"session":{let e=await a.session({options:k,sessionStore:S});return e.cookies&&E.push(...e.cookies),{...e,cookies:E}}case"csrf":return{headers:[{key:"Content-Type",value:"application/json"}],body:{csrfToken:k.csrfToken},cookies:E};case"signin":if(t.signIn){let e=`${t.signIn}${t.signIn.includes("?")?"&":"?"}callbackUrl=${encodeURIComponent(k.callbackUrl)}`;return w&&(e=`${e}&error=${encodeURIComponent(w)}`),{redirect:e,cookies:E}}return e.signin();case"signout":if(t.signOut)return{redirect:t.signOut,cookies:E};return e.signout();case"callback":if(k.provider){let e=await a.callback({body:g.body,query:g.query,headers:g.headers,cookies:g.cookies,method:x,options:k,sessionStore:S});return e.cookies&&E.push(...e.cookies),{...e,cookies:E}}break;case"verify-request":if(t.verifyRequest)return{redirect:t.verifyRequest,cookies:E};return e.verifyRequest();case"error":if(["Signin","OAuthSignin","OAuthCallback","OAuthCreateAccount","EmailCreateAccount","Callback","OAuthAccountNotLinked","EmailSignin","CredentialsSignin","SessionRequired"].includes(w))return{redirect:`${k.url}/signin?error=${w}`,cookies:E};if(t.error)return{redirect:`${t.error}${t.error.includes("?")?"&":"?"}error=${w}`,cookies:E};return e.error({error:w})}}else if("POST"===x)switch(m){case"signin":if(k.csrfTokenVerified&&k.provider){let e=await a.signin({query:g.query,body:g.body,options:k});return e.cookies&&E.push(...e.cookies),{...e,cookies:E}}return{redirect:`${k.url}/signin?csrf=true`,cookies:E};case"signout":if(k.csrfTokenVerified){let e=await a.signout({options:k,sessionStore:S});return e.cookies&&E.push(...e.cookies),{...e,cookies:E}}return{redirect:`${k.url}/signout?csrf=true`,cookies:E};case"callback":if(k.provider){if("credentials"===k.provider.type&&!k.csrfTokenVerified)return{redirect:`${k.url}/signin?csrf=true`,cookies:E};let e=await a.callback({body:g.body,query:g.query,headers:g.headers,cookies:g.cookies,method:x,options:k,sessionStore:S});return e.cookies&&E.push(...e.cookies),{...e,cookies:E}}break;case"_log":if(h.logger)try{let{code:e,level:t,...r}=null!=(f=g.body)?f:{};o.default[t](e,r)}catch(e){o.default.error("LOGGER_ERROR",e)}return{};case"session":if(k.csrfTokenVerified){let e=await a.session({options:k,sessionStore:S,newSession:null==(p=g.body)?void 0:p.data,isUpdate:!0});return e.cookies&&E.push(...e.cookies),{...e,cookies:E}}return{status:400,body:{},cookies:E}}return{status:400,body:`Error: This action with HTTP ${x} is not supported by NextAuth.js`}}},65597:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=(...e)=>{let t,r=e.filter(Boolean);if(0===r.length||1===r.length)return!0;for(let e of r){let r=Object.keys(e);if(!t||0===t.size){t=new Set(r);continue}for(let e of r){if(t.has(e))return!1;t.add(e)}}return!0}},65764:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.exportJWK=t.exportPKCS8=t.exportSPKI=void 0;let n=r(93346),o=r(93346),i=r(41193);t.exportSPKI=async function(e){return(0,n.toSPKI)(e)},t.exportPKCS8=async function(e){return(0,o.toPKCS8)(e)},t.exportJWK=async function(e){return(0,i.default)(e)}},66318:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.deflate=t.inflate=void 0;let n=r(28354),o=r(74075),i=r(99938),a=(0,n.promisify)(o.inflateRaw),s=(0,n.promisify)(o.deflateRaw);t.inflate=e=>a(e,{maxOutputLength:25e4}).catch(()=>{throw new i.JWEDecompressionFailed}),t.deflate=e=>s(e)},66446:e=>{e.exports=function(e,t){this.v=e,this.k=t},e.exports.__esModule=!0,e.exports.default=e.exports},66837:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=s;var n=r(9545),o=r(8365),i=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=a(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&({}).hasOwnProperty.call(e,i)){var s=o?Object.getOwnPropertyDescriptor(e,i):null;s&&(s.get||s.set)?Object.defineProperty(n,i,s):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}(r(50671));function a(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(a=function(e){return e?r:t})(e)}async function s({options:e,query:t}){var r,a,s;let{logger:c,provider:l}=e,u={};if("string"==typeof l.authorization){let e=Object.fromEntries(new URL(l.authorization).searchParams);u={...u,...e}}else u={...u,...null==(a=l.authorization)?void 0:a.params};if(u={...u,...t},null!=(r=l.version)&&r.startsWith("1.")){let t=(0,o.oAuth1Client)(e),r=await t.getOAuthRequestToken(u),n=`${null==(s=l.authorization)?void 0:s.url}?${new URLSearchParams({oauth_token:r.oauth_token,oauth_token_secret:r.oauth_token_secret,...r.params})}`;return o.oAuth1TokenStore.set(r.oauth_token,r.oauth_token_secret),c.debug("GET_AUTHORIZATION_URL",{url:n,provider:l}),{redirect:n}}let d=await (0,n.openidClient)(e),f=u,p=[];await i.state.create(e,p,f),await i.pkce.create(e,p,f),await i.nonce.create(e,p,f);let h=d.authorizationUrl(f);return c.debug("GET_AUTHORIZATION_URL",{url:h,cookies:p,provider:l}),{redirect:h,cookies:p}}},67131:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default="node:crypto"},67135:e=>{e.exports=function(e){var t=Object(e),r=[];for(var n in t)r.unshift(n);return function e(){for(;r.length;)if((n=r.pop())in t)return e.value=n,e.done=!1,e;return e.done=!0,e}},e.exports.__esModule=!0,e.exports.default=e.exports},67459:(e,t,r)=>{"use strict";var n=r(55511);function o(e,t){return t=s(e,t),function(e,t){if(void 0===(r="passthrough"!==t.algorithm?n.createHash(t.algorithm):new u).write&&(r.write=r.update,r.end=r.update),l(t,r).dispatch(e),r.update||r.end(""),r.digest)return r.digest("buffer"===t.encoding?void 0:t.encoding);var r,o=r.read();return"buffer"===t.encoding?o:o.toString(t.encoding)}(e,t)}(t=e.exports=o).sha1=function(e){return o(e)},t.keys=function(e){return o(e,{excludeValues:!0,algorithm:"sha1",encoding:"hex"})},t.MD5=function(e){return o(e,{algorithm:"md5",encoding:"hex"})},t.keysMD5=function(e){return o(e,{algorithm:"md5",encoding:"hex",excludeValues:!0})};var i=n.getHashes?n.getHashes().slice():["sha1","md5"];i.push("passthrough");var a=["buffer","hex","binary","base64"];function s(e,t){var r={};if(r.algorithm=(t=t||{}).algorithm||"sha1",r.encoding=t.encoding||"hex",r.excludeValues=!!t.excludeValues,r.algorithm=r.algorithm.toLowerCase(),r.encoding=r.encoding.toLowerCase(),r.ignoreUnknown=!0===t.ignoreUnknown,r.respectType=!1!==t.respectType,r.respectFunctionNames=!1!==t.respectFunctionNames,r.respectFunctionProperties=!1!==t.respectFunctionProperties,r.unorderedArrays=!0===t.unorderedArrays,r.unorderedSets=!1!==t.unorderedSets,r.unorderedObjects=!1!==t.unorderedObjects,r.replacer=t.replacer||void 0,r.excludeKeys=t.excludeKeys||void 0,void 0===e)throw Error("Object argument required.");for(var n=0;n<i.length;++n)i[n].toLowerCase()===r.algorithm.toLowerCase()&&(r.algorithm=i[n]);if(-1===i.indexOf(r.algorithm))throw Error('Algorithm "'+r.algorithm+'"  not supported. supported values: '+i.join(", "));if(-1===a.indexOf(r.encoding)&&"passthrough"!==r.algorithm)throw Error('Encoding "'+r.encoding+'"  not supported. supported values: '+a.join(", "));return r}function c(e){return"function"==typeof e&&null!=/^function\s+\w*\s*\(\s*\)\s*{\s+\[native code\]\s+}$/i.exec(Function.prototype.toString.call(e))}function l(e,t,r){r=r||[];var n=function(e){return t.update?t.update(e,"utf8"):t.write(e,"utf8")};return{dispatch:function(t){e.replacer&&(t=e.replacer(t));var r=typeof t;return null===t&&(r="null"),this["_"+r](t)},_object:function(t){var o=Object.prototype.toString.call(t),i=/\[object (.*)\]/i.exec(o);i=(i=i?i[1]:"unknown:["+o+"]").toLowerCase();var a=null;if((a=r.indexOf(t))>=0)return this.dispatch("[CIRCULAR:"+a+"]");if(r.push(t),"undefined"!=typeof Buffer&&Buffer.isBuffer&&Buffer.isBuffer(t))return n("buffer:"),n(t);if("object"!==i&&"function"!==i&&"asyncfunction"!==i)if(this["_"+i])this["_"+i](t);else if(e.ignoreUnknown)return n("["+i+"]");else throw Error('Unknown object type "'+i+'"');else{var s=Object.keys(t);e.unorderedObjects&&(s=s.sort()),!1===e.respectType||c(t)||s.splice(0,0,"prototype","__proto__","constructor"),e.excludeKeys&&(s=s.filter(function(t){return!e.excludeKeys(t)})),n("object:"+s.length+":");var l=this;return s.forEach(function(r){l.dispatch(r),n(":"),e.excludeValues||l.dispatch(t[r]),n(",")})}},_array:function(t,o){o=void 0!==o?o:!1!==e.unorderedArrays;var i=this;if(n("array:"+t.length+":"),!o||t.length<=1)return t.forEach(function(e){return i.dispatch(e)});var a=[],s=t.map(function(t){var n=new u,o=r.slice();return l(e,n,o).dispatch(t),a=a.concat(o.slice(r.length)),n.read().toString()});return r=r.concat(a),s.sort(),this._array(s,!1)},_date:function(e){return n("date:"+e.toJSON())},_symbol:function(e){return n("symbol:"+e.toString())},_error:function(e){return n("error:"+e.toString())},_boolean:function(e){return n("bool:"+e.toString())},_string:function(e){n("string:"+e.length+":"),n(e.toString())},_function:function(t){n("fn:"),c(t)?this.dispatch("[native]"):this.dispatch(t.toString()),!1!==e.respectFunctionNames&&this.dispatch("function-name:"+String(t.name)),e.respectFunctionProperties&&this._object(t)},_number:function(e){return n("number:"+e.toString())},_xml:function(e){return n("xml:"+e.toString())},_null:function(){return n("Null")},_undefined:function(){return n("Undefined")},_regexp:function(e){return n("regex:"+e.toString())},_uint8array:function(e){return n("uint8array:"),this.dispatch(Array.prototype.slice.call(e))},_uint8clampedarray:function(e){return n("uint8clampedarray:"),this.dispatch(Array.prototype.slice.call(e))},_int8array:function(e){return n("uint8array:"),this.dispatch(Array.prototype.slice.call(e))},_uint16array:function(e){return n("uint16array:"),this.dispatch(Array.prototype.slice.call(e))},_int16array:function(e){return n("uint16array:"),this.dispatch(Array.prototype.slice.call(e))},_uint32array:function(e){return n("uint32array:"),this.dispatch(Array.prototype.slice.call(e))},_int32array:function(e){return n("uint32array:"),this.dispatch(Array.prototype.slice.call(e))},_float32array:function(e){return n("float32array:"),this.dispatch(Array.prototype.slice.call(e))},_float64array:function(e){return n("float64array:"),this.dispatch(Array.prototype.slice.call(e))},_arraybuffer:function(e){return n("arraybuffer:"),this.dispatch(new Uint8Array(e))},_url:function(e){return n("url:"+e.toString(),"utf8")},_map:function(t){n("map:");var r=Array.from(t);return this._array(r,!1!==e.unorderedSets)},_set:function(t){n("set:");var r=Array.from(t);return this._array(r,!1!==e.unorderedSets)},_file:function(e){return n("file:"),this.dispatch([e.name,e.size,e.type,e.lastModfied])},_blob:function(){if(e.ignoreUnknown)return n("[blob]");throw Error('Hashing Blob objects is currently not supported\n(see https://github.com/puleos/object-hash/issues/26)\nUse "options.replacer" or "options.ignoreUnknown"\n')},_domwindow:function(){return n("domwindow")},_bigint:function(e){return n("bigint:"+e.toString())},_process:function(){return n("process")},_timer:function(){return n("timer")},_pipe:function(){return n("pipe")},_tcp:function(){return n("tcp")},_udp:function(){return n("udp")},_tty:function(){return n("tty")},_statwatcher:function(){return n("statwatcher")},_securecontext:function(){return n("securecontext")},_connection:function(){return n("connection")},_zlib:function(){return n("zlib")},_context:function(){return n("context")},_nodescript:function(){return n("nodescript")},_httpparser:function(){return n("httpparser")},_dataview:function(){return n("dataview")},_signal:function(){return n("signal")},_fsevent:function(){return n("fsevent")},_tlswrap:function(){return n("tlswrap")}}}function u(){return{buf:"",write:function(e){this.buf+=e},end:function(e){this.buf+=e},read:function(){return this.buf}}}t.writeToStream=function(e,t,r){return void 0===r&&(r=t,t={}),l(t=s(e,t),r).dispatch(e)}},67804:(e,t,r)=>{"use strict";var n=r(9168);Object.defineProperty(t,"__esModule",{value:!0}),t.assertConfig=function(e){var t,r,n,l,u,d,f;let p,h,y,{options:_,req:g}=e,b=[];if(!s&&(g.origin||b.push("NEXTAUTH_URL"),_.secret,_.debug&&b.push("DEBUG_ENABLED")),!_.secret)return new o.MissingSecret("Please define a `secret` in production.");if(!(null!=(t=g.query)&&t.nextauth)&&!g.action)return new o.MissingAPIRoute("Cannot find [...nextauth].{js,ts} in `/pages/api/auth`. Make sure the filename is written correctly.");let m=null==(r=g.query)?void 0:r.callbackUrl,v=(0,i.default)(g.origin);if(m&&!c(m,v.base))return new o.InvalidCallbackUrl(`Invalid callback URL. Received: ${m}`);let{callbackUrl:w}=(0,a.defaultCookies)(null!=(n=_.useSecureCookies)?n:v.base.startsWith("https://")),x=null==(l=g.cookies)?void 0:l[null!=(u=null==(d=_.cookies)||null==(d=d.callbackUrl)?void 0:d.name)?u:w.name];if(x&&!c(x,v.base))return new o.InvalidCallbackUrl(`Invalid callback URL. Received: ${x}`);for(let e of _.providers)"credentials"===e.type?p=!0:"email"===e.type?h=!0:"twitter"===e.id&&"2.0"===e.version&&(y=!0);if(p){let e=(null==(f=_.session)?void 0:f.strategy)==="database",t=!_.providers.some(e=>"credentials"!==e.type);if(e&&t)return new o.UnsupportedStrategy("Signin in with credentials only supported if JWT strategy is enabled");if(_.providers.some(e=>"credentials"===e.type&&!e.authorize))return new o.MissingAuthorize("Must define an authorize() handler to use credentials authentication provider")}if(h){let{adapter:e}=_;if(!e)return new o.MissingAdapter("E-mail login requires an adapter.");let t=["createVerificationToken","useVerificationToken","getUserByEmail"].filter(t=>!e[t]);if(t.length)return new o.MissingAdapterMethods(`Required adapter methods were missing: ${t.join(", ")}`)}return s||(y&&b.push("TWITTER_OAUTH_2_BETA"),s=!0),b};var o=r(20113),i=n(r(16851)),a=r(99632);let s=!1;function c(e,t){try{return/^https?:/.test(new URL(e,e.startsWith("/")?t:void 0).protocol)}catch(e){return!1}}},67929:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.decode=t.encode=void 0;let n=r(68327);t.encode=n.encode,t.decode=n.decode},68327:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.decode=t.encode=t.encodeBase64=t.decodeBase64=void 0;let n=r(79428),o=r(2594);n.Buffer.isEncoding("base64url")?t.encode=e=>n.Buffer.from(e).toString("base64url"):t.encode=e=>n.Buffer.from(e).toString("base64").replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_"),t.decodeBase64=e=>n.Buffer.from(e,"base64"),t.encodeBase64=e=>n.Buffer.from(e).toString("base64"),t.decode=e=>n.Buffer.from(function(e){let t=e;return t instanceof Uint8Array&&(t=o.decoder.decode(t)),t}(e),"base64")},68588:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.defaultCallbacks=void 0,t.defaultCallbacks={signIn:()=>!0,redirect:({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t,session:({session:e})=>e,jwt:({token:e})=>e}},68907:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.generalDecrypt=void 0;let n=r(36736),o=r(99938),i=r(25110);t.generalDecrypt=async function(e,t,r){if(!(0,i.default)(e))throw new o.JWEInvalid("General JWE must be an object");if(!Array.isArray(e.recipients)||!e.recipients.every(i.default))throw new o.JWEInvalid("JWE Recipients missing or incorrect type");if(!e.recipients.length)throw new o.JWEInvalid("JWE Recipients has no members");for(let o of e.recipients)try{return await (0,n.flattenedDecrypt)({aad:e.aad,ciphertext:e.ciphertext,encrypted_key:o.encrypted_key,header:o.header,iv:e.iv,protected:e.protected,tag:e.tag,unprotected:e.unprotected},t,r)}catch{}throw new o.JWEDecryptionFailed}},68919:(e,t,r)=>{e.exports=r(1889).default},69184:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.decodeProtectedHeader=void 0;let n=r(67929),o=r(2594),i=r(25110);t.decodeProtectedHeader=function(e){let t;if("string"==typeof e){let r=e.split(".");(3===r.length||5===r.length)&&([t]=r)}else if("object"==typeof e&&e)if("protected"in e)t=e.protected;else throw TypeError("Token does not contain a Protected Header");try{if("string"!=typeof t||!t)throw Error();let e=JSON.parse(o.decoder.decode((0,n.decode)(t)));if(!(0,i.default)(e))throw Error();return e}catch{throw TypeError("Invalid Token or Protected Header formatting")}}},69717:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=e=>Math.floor(e.getTime()/1e3)},70885:e=>{let t;if(Buffer.isEncoding("base64url"))t=(e,t="utf8")=>Buffer.from(e,t).toString("base64url");else{let e=e=>e.replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_");t=(t,r="utf8")=>e(Buffer.from(t,r).toString("base64"))}e.exports.decode=e=>Buffer.from(e,"base64"),e.exports.encode=t},70959:(e,t,r)=>{"use strict";var n=r(9168);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){let{url:t,theme:r,query:n,cookies:u}=e;function d({html:e,title:t,status:n}){var i;return{cookies:u,status:n,headers:[{key:"Content-Type",value:"text/html"}],body:`<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0"><style>${(0,l.default)()}</style><title>${t}</title></head><body class="__next-auth-theme-${null!=(i=null==r?void 0:r.colorScheme)?i:"auto"}"><div class="page">${(0,o.default)(e)}</div></body></html>`}}return{signin:t=>d({html:(0,i.default)({csrfToken:e.csrfToken,providers:e.providers,callbackUrl:e.callbackUrl,theme:r,...n,...t}),title:"Sign In"}),signout:n=>d({html:(0,a.default)({csrfToken:e.csrfToken,url:t,theme:r,...n}),title:"Sign Out"}),verifyRequest:e=>d({html:(0,s.default)({url:t,theme:r,...e}),title:"Verify Request"}),error:e=>d({...(0,c.default)({url:t,theme:r,...e}),title:"Error"})}};var o=n(r(68919)),i=n(r(8557)),a=n(r(47024)),s=n(r(16974)),c=n(r(27969)),l=n(r(84838))},70997:(e,t,r)=>{var n=r(30994);e.exports=function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&n(e,t)},e.exports.__esModule=!0,e.exports.default=e.exports},71746:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.flattenedVerify=void 0;let n=r(68327),o=r(9534),i=r(99938),a=r(2594),s=r(65597),c=r(25110),l=r(80335),u=r(8535),d=r(93069);t.flattenedVerify=async function(e,t,r){var f;let p,h;if(!(0,c.default)(e))throw new i.JWSInvalid("Flattened JWS must be an object");if(void 0===e.protected&&void 0===e.header)throw new i.JWSInvalid('Flattened JWS must have either of the "protected" or "header" members');if(void 0!==e.protected&&"string"!=typeof e.protected)throw new i.JWSInvalid("JWS Protected Header incorrect type");if(void 0===e.payload)throw new i.JWSInvalid("JWS Payload missing");if("string"!=typeof e.signature)throw new i.JWSInvalid("JWS Signature missing or incorrect type");if(void 0!==e.header&&!(0,c.default)(e.header))throw new i.JWSInvalid("JWS Unprotected Header incorrect type");let y={};if(e.protected)try{let t=(0,n.decode)(e.protected);y=JSON.parse(a.decoder.decode(t))}catch{throw new i.JWSInvalid("JWS Protected Header is invalid")}if(!(0,s.default)(y,e.header))throw new i.JWSInvalid("JWS Protected and JWS Unprotected Header Parameter names must be disjoint");let _={...y,...e.header},g=(0,u.default)(i.JWSInvalid,new Map([["b64",!0]]),null==r?void 0:r.crit,y,_),b=!0;if(g.has("b64")&&"boolean"!=typeof(b=y.b64))throw new i.JWSInvalid('The "b64" (base64url-encode payload) Header Parameter must be a boolean');let{alg:m}=_;if("string"!=typeof m||!m)throw new i.JWSInvalid('JWS "alg" (Algorithm) Header Parameter missing or invalid');let v=r&&(0,d.default)("algorithms",r.algorithms);if(v&&!v.has(m))throw new i.JOSEAlgNotAllowed('"alg" (Algorithm) Header Parameter not allowed');if(b){if("string"!=typeof e.payload)throw new i.JWSInvalid("JWS Payload must be a string")}else if("string"!=typeof e.payload&&!(e.payload instanceof Uint8Array))throw new i.JWSInvalid("JWS Payload must be a string or an Uint8Array instance");let w=!1;"function"==typeof t&&(t=await t(y,e),w=!0),(0,l.default)(m,t,"verify");let x=(0,a.concat)(a.encoder.encode(null!=(f=e.protected)?f:""),a.encoder.encode("."),"string"==typeof e.payload?a.encoder.encode(e.payload):e.payload);try{p=(0,n.decode)(e.signature)}catch{throw new i.JWSInvalid("Failed to base64url decode the signature")}if(!await (0,o.default)(m,t,p,x))throw new i.JWSSignatureVerificationFailed;if(b)try{h=(0,n.decode)(e.payload)}catch{throw new i.JWSInvalid("Failed to base64url decode the payload")}else h="string"==typeof e.payload?a.encoder.encode(e.payload):e.payload;let k={payload:h};return(void 0!==e.protected&&(k.protectedHeader=y),void 0!==e.header&&(k.unprotectedHeader=e.header),w)?{...k,key:t}:k}},71800:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(55511);t.default=(e,t,r,o,i)=>{let a=parseInt(e.substr(3),10)>>3||20,s=(0,n.createHmac)(e,r.byteLength?r:new Uint8Array(a)).update(t).digest(),c=Math.ceil(i/a),l=new Uint8Array(a*c+o.byteLength+1),u=0,d=0;for(let t=1;t<=c;t++)l.set(o,d),l[d+o.byteLength]=t,l.set((0,n.createHmac)(e,s).update(l.subarray(u,d+o.byteLength+1)).digest(),d),u=d,d+=a;return l.slice(0,i)}},71871:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=o;var n=r(29837);async function o(e){var t,r,o,i,a,s;let{options:c,sessionStore:l,newSession:u,isUpdate:d}=e,{adapter:f,jwt:p,events:h,callbacks:y,logger:_,session:{strategy:g,maxAge:b}}=c,m={body:{},headers:[{key:"Content-Type",value:"application/json"}],cookies:[]},v=l.value;if(!v)return m;if("jwt"===g)try{let e=await p.decode({...p,token:v});if(!e)throw Error("JWT invalid");let o=await y.jwt({token:e,...d&&{trigger:"update"},session:u}),i=(0,n.fromDate)(b),a=await y.session({session:{user:{name:null==e?void 0:e.name,email:null==e?void 0:e.email,image:null==e?void 0:e.picture},expires:i.toISOString()},token:o});m.body=a;let s=await p.encode({...p,token:o,maxAge:c.session.maxAge}),f=l.chunk(s,{expires:i});null==(t=m.cookies)||t.push(...f),await (null==(r=h.session)?void 0:r.call(h,{session:a,token:o}))}catch(e){_.error("JWT_SESSION_ERROR",e),null==(o=m.cookies)||o.push(...l.clean())}else try{let{getSessionAndUser:e,deleteSession:t,updateSession:r}=f,o=await e(v);if(o&&o.session.expires.valueOf()<Date.now()&&(await t(v),o=null),o){let{user:e,session:t}=o,s=c.session.updateAge,l=t.expires.valueOf()-1e3*b+1e3*s,f=(0,n.fromDate)(b);l<=Date.now()&&await r({sessionToken:v,expires:f});let p=await y.session({session:{user:{name:e.name,email:e.email,image:e.image},expires:t.expires.toISOString()},user:e,newSession:u,...d?{trigger:"update"}:{}});m.body=p,null==(i=m.cookies)||i.push({name:c.cookies.sessionToken.name,value:v,options:{...c.cookies.sessionToken.options,expires:f}}),await (null==(a=h.session)?void 0:a.call(h,{session:p}))}else v&&(null==(s=m.cookies)||s.push(...l.clean()))}catch(e){_.error("SESSION_ERROR",e)}return m}},71872:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.UnsecuredJWT=void 0;let n=r(68327),o=r(2594),i=r(99938),a=r(81822),s=r(2754);class c extends s.ProduceJWT{encode(){let e=n.encode(JSON.stringify({alg:"none"})),t=n.encode(JSON.stringify(this._payload));return`${e}.${t}.`}static decode(e,t){let r;if("string"!=typeof e)throw new i.JWTInvalid("Unsecured JWT must be a string");let{0:s,1:c,2:l,length:u}=e.split(".");if(3!==u||""!==l)throw new i.JWTInvalid("Invalid Unsecured JWT");try{if(r=JSON.parse(o.decoder.decode(n.decode(s))),"none"!==r.alg)throw Error()}catch{throw new i.JWTInvalid("Invalid Unsecured JWT")}return{payload:(0,a.default)(r,n.decode(c),t),header:r}}}t.UnsecuredJWT=c},72115:(e,t,r)=>{let n=r(78830);function o(e,t,...r){for(let i of r)if(n(i))for(let[r,a]of Object.entries(i))"__proto__"!==r&&"constructor"!==r&&(void 0===t[r]&&void 0!==a&&(t[r]=a),e&&n(t[r])&&n(a)&&o(!0,t[r],a));return t}e.exports=o.bind(void 0,!1),e.exports.deep=o.bind(void 0,!0)},72443:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});class r{constructor(e){if(48!==e[0]||(this.buffer=e,this.offset=1,this.decodeLength()!==e.length-this.offset))throw TypeError()}decodeLength(){let e=this.buffer[this.offset++];if(128&e){let t=-129&e;e=0;for(let r=0;r<t;r++)e=e<<8|this.buffer[this.offset+r];this.offset+=t}return e}unsignedInteger(){if(2!==this.buffer[this.offset++])throw TypeError();let e=this.decodeLength();0===this.buffer[this.offset]&&(this.offset++,e--);let t=this.buffer.slice(this.offset,this.offset+e);return this.offset+=e,t}end(){if(this.offset!==this.buffer.length)throw TypeError()}}t.default=r},72921:(e,t,r)=>{"use strict";let n,o;r.r(t),r.d(t,{NIL:()=>x,parse:()=>g,stringify:()=>p,v1:()=>_,v3:()=>m,v4:()=>v,v5:()=>w,validate:()=>d,version:()=>k});var i=r(55511),a=r.n(i);let s=new Uint8Array(256),c=s.length;function l(){return c>s.length-16&&(a().randomFillSync(s),c=0),s.slice(c,c+=16)}let u=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i,d=function(e){return"string"==typeof e&&u.test(e)},f=[];for(let e=0;e<256;++e)f.push((e+256).toString(16).substr(1));let p=function(e,t=0){let r=(f[e[t+0]]+f[e[t+1]]+f[e[t+2]]+f[e[t+3]]+"-"+f[e[t+4]]+f[e[t+5]]+"-"+f[e[t+6]]+f[e[t+7]]+"-"+f[e[t+8]]+f[e[t+9]]+"-"+f[e[t+10]]+f[e[t+11]]+f[e[t+12]]+f[e[t+13]]+f[e[t+14]]+f[e[t+15]]).toLowerCase();if(!d(r))throw TypeError("Stringified UUID is invalid");return r},h=0,y=0,_=function(e,t,r){let i=t&&r||0,a=t||Array(16),s=(e=e||{}).node||n,c=void 0!==e.clockseq?e.clockseq:o;if(null==s||null==c){let t=e.random||(e.rng||l)();null==s&&(s=n=[1|t[0],t[1],t[2],t[3],t[4],t[5]]),null==c&&(c=o=(t[6]<<8|t[7])&16383)}let u=void 0!==e.msecs?e.msecs:Date.now(),d=void 0!==e.nsecs?e.nsecs:y+1,f=u-h+(d-y)/1e4;if(f<0&&void 0===e.clockseq&&(c=c+1&16383),(f<0||u>h)&&void 0===e.nsecs&&(d=0),d>=1e4)throw Error("uuid.v1(): Can't create more than 10M uuids/sec");h=u,y=d,o=c;let _=((0xfffffff&(u+=122192928e5))*1e4+d)%0x100000000;a[i++]=_>>>24&255,a[i++]=_>>>16&255,a[i++]=_>>>8&255,a[i++]=255&_;let g=u/0x100000000*1e4&0xfffffff;a[i++]=g>>>8&255,a[i++]=255&g,a[i++]=g>>>24&15|16,a[i++]=g>>>16&255,a[i++]=c>>>8|128,a[i++]=255&c;for(let e=0;e<6;++e)a[i+e]=s[e];return t||p(a)},g=function(e){let t;if(!d(e))throw TypeError("Invalid UUID");let r=new Uint8Array(16);return r[0]=(t=parseInt(e.slice(0,8),16))>>>24,r[1]=t>>>16&255,r[2]=t>>>8&255,r[3]=255&t,r[4]=(t=parseInt(e.slice(9,13),16))>>>8,r[5]=255&t,r[6]=(t=parseInt(e.slice(14,18),16))>>>8,r[7]=255&t,r[8]=(t=parseInt(e.slice(19,23),16))>>>8,r[9]=255&t,r[10]=(t=parseInt(e.slice(24,36),16))/0x10000000000&255,r[11]=t/0x100000000&255,r[12]=t>>>24&255,r[13]=t>>>16&255,r[14]=t>>>8&255,r[15]=255&t,r};function b(e,t,r){function n(e,n,o,i){if("string"==typeof e&&(e=function(e){e=unescape(encodeURIComponent(e));let t=[];for(let r=0;r<e.length;++r)t.push(e.charCodeAt(r));return t}(e)),"string"==typeof n&&(n=g(n)),16!==n.length)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");let a=new Uint8Array(16+e.length);if(a.set(n),a.set(e,n.length),(a=r(a))[6]=15&a[6]|t,a[8]=63&a[8]|128,o){i=i||0;for(let e=0;e<16;++e)o[i+e]=a[e];return o}return p(a)}try{n.name=e}catch(e){}return n.DNS="6ba7b810-9dad-11d1-80b4-00c04fd430c8",n.URL="6ba7b811-9dad-11d1-80b4-00c04fd430c8",n}let m=b("v3",48,function(e){return Array.isArray(e)?e=Buffer.from(e):"string"==typeof e&&(e=Buffer.from(e,"utf8")),a().createHash("md5").update(e).digest()}),v=function(e,t,r){let n=(e=e||{}).random||(e.rng||l)();if(n[6]=15&n[6]|64,n[8]=63&n[8]|128,t){r=r||0;for(let e=0;e<16;++e)t[r+e]=n[e];return t}return p(n)},w=b("v5",80,function(e){return Array.isArray(e)?e=Buffer.from(e):"string"==typeof e&&(e=Buffer.from(e,"utf8")),a().createHash("sha1").update(e).digest()}),x="00000000-0000-0000-0000-000000000000",k=function(e){if(!d(e))throw TypeError("Invalid UUID");return parseInt(e.substr(14,1),16)}},73602:(e,t,r)=>{let n=r(67459),o=r(42142),{RPError:i}=r(61408),{assertIssuerConfiguration:a}=r(2279),s=r(28577),{keystores:c}=r(55132),l=r(48160),u=r(9552),d=new WeakMap,f=new WeakMap,p=e=>(f.has(e)||f.set(e,new o({max:100})),f.get(e));async function h(e=!1){a(this,"jwks_uri");let t=c.get(this),r=p(this);return e||!t?(d.has(this)||(r.reset(),d.set(this,(async()=>{let e=l(await u.call(this,{method:"GET",responseType:"json",url:this.jwks_uri,headers:{Accept:"application/json, application/jwk-set+json"}}).finally(()=>{d.delete(this)})),t=s.fromJWKS(e,{onlyPublic:!0});return r.set("throttle",!0,6e4),c.set(this,t),t})())),d.get(this)):t}async function y({kid:e,kty:t,alg:r,use:o},{allowMulti:a=!1}={}){let s=p(this),c={kid:e,kty:t,alg:r,use:o},l=n(c,{algorithm:"sha256",ignoreUnknown:!0,unorderedArrays:!0,unorderedSets:!0,respectType:!1}),u=s.get(l)||s.get("throttle"),d=await h.call(this,!u),f=d.all(c);if(delete c.use,0===f.length)throw new i({printf:["no valid key found in issuer's jwks_uri for key parameters %j",c],jwks:d});if(!a&&f.length>1&&!e)throw new i({printf:["multiple matching keys found in issuer's jwks_uri for key parameters %j, kid must be provided in this case",c],jwks:d});return s.set(l,!0),f}e.exports.queryKeyStore=y,e.exports.keystore=h},73913:(e,t,r)=>{"use strict";Object.defineProperty(t,"r",{enumerable:!0,get:function(){return l}});let n=r(63033),o=r(29294),i=r(84971),a=r(76926),s=r(80023),c=r(98479);function l(){let e=o.workAsyncStorage.getStore(),t=n.workUnitAsyncStorage.getStore();switch((!e||!t)&&(0,n.throwForMissingRequestStore)("draftMode"),t.type){case"request":return u(t.draftMode,e);case"cache":case"unstable-cache":let r=(0,n.getDraftModeProviderForCacheScope)(e,t);if(r)return u(r,e);case"prerender":case"prerender-ppr":case"prerender-legacy":return f(null);default:return t}}function u(e,t){let r,n=d.get(l);return n||(r=f(e),d.set(e,r),r)}let d=new WeakMap;function f(e){let t=new p(e),r=Promise.resolve(t);return Object.defineProperty(r,"isEnabled",{get:()=>t.isEnabled,set(e){Object.defineProperty(r,"isEnabled",{value:e,writable:!0,enumerable:!0})},enumerable:!0,configurable:!0}),r.enable=t.enable.bind(t),r.disable=t.disable.bind(t),r}class p{constructor(e){this._provider=e}get isEnabled(){return null!==this._provider&&this._provider.isEnabled}enable(){y("draftMode().enable()"),null!==this._provider&&this._provider.enable()}disable(){y("draftMode().disable()"),null!==this._provider&&this._provider.disable()}}let h=(0,a.createDedupedByCallsiteServerErrorLoggerDev)(function(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E377",enumerable:!1,configurable:!0})});function y(e){let t=o.workAsyncStorage.getStore(),r=n.workUnitAsyncStorage.getStore();if(t){if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside "use cache". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E246",enumerable:!1,configurable:!0});else if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside a function cached with "unstable_cache(...)". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E259",enumerable:!1,configurable:!0});else if("after"===r.phase)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside \`after\`. The enabled status of draftMode can be read inside \`after\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E348",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new s.StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(r){if("prerender"===r.type){let n=Object.defineProperty(Error(`Route ${t.route} used ${e} without first calling \`await connection()\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`),"__NEXT_ERROR_CODE",{value:"E126",enumerable:!1,configurable:!0});(0,i.abortAndThrowOnSynchronousRequestDataAccess)(t.route,e,n,r)}else if("prerender-ppr"===r.type)(0,i.postponeWithTracking)(t.route,e,r.dynamicTracking);else if("prerender-legacy"===r.type){r.revalidate=0;let n=Object.defineProperty(new c.DynamicServerError(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}}}}},74113:(e,t,r)=>{var n=r(66446),o=r(18040);e.exports=function e(t,r){var i;this.next||(o(e.prototype),o(e.prototype,"function"==typeof Symbol&&Symbol.asyncIterator||"@asyncIterator",function(){return this})),o(this,"_invoke",function(e,o,a){function s(){return new r(function(o,i){!function e(o,i,a,s){try{var c=t[o](i),l=c.value;return l instanceof n?r.resolve(l.v).then(function(t){e("next",t,a,s)},function(t){e("throw",t,a,s)}):r.resolve(l).then(function(e){c.value=e,a(c)},function(t){return e("throw",t,a,s)})}catch(e){s(e)}}(e,a,o,i)})}return i=i?i.then(s,s):s()},!0)},e.exports.__esModule=!0,e.exports.default=e.exports},75722:(e,t,r)=>{var n=r(24956).default,o=r(60105);e.exports=function(e,t){if(t&&("object"==n(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return o(e)},e.exports.__esModule=!0,e.exports.default=e.exports},76926:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return c}});let n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=o(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var s=i?Object.getOwnPropertyDescriptor(e,a):null;s&&(s.get||s.set)?Object.defineProperty(n,a,s):n[a]=e[a]}return n.default=e,r&&r.set(e,n),n}(r(61120));function o(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(o=function(e){return e?r:t})(e)}let i={current:null},a="function"==typeof n.cache?n.cache:e=>e,s=console.warn;function c(e){return function(...t){s(e(...t))}}a(e=>{try{s(i.current)}finally{i.current=null}})},76964:(e,t,r)=>{let n,{strict:o}=r(12412),{createHash:i}=r(55511),{format:a}=r(28354),s=r(7161);if(Buffer.isEncoding("base64url"))n=e=>e.toString("base64url");else{let e=e=>e.replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_");n=t=>e(t.toString("base64"))}function c(e,t,r){let o=(function(e,t){switch(e){case"HS256":case"RS256":case"PS256":case"ES256":case"ES256K":return i("sha256");case"HS384":case"RS384":case"PS384":case"ES384":return i("sha384");case"HS512":case"RS512":case"PS512":case"ES512":case"Ed25519":return i("sha512");case"Ed448":if(!s)throw TypeError("Ed448 *_hash calculation is not supported in your Node.js runtime version");return i("shake256",{outputLength:114});case"EdDSA":switch(t){case"Ed25519":return i("sha512");case"Ed448":if(!s)throw TypeError("Ed448 *_hash calculation is not supported in your Node.js runtime version");return i("shake256",{outputLength:114});default:throw TypeError("unrecognized or invalid EdDSA curve provided")}default:throw TypeError("unrecognized or invalid JWS algorithm provided")}})(t,r).update(e).digest();return n(o.slice(0,o.length/2))}e.exports={validate:function(e,t,r,n,i){let s,l;if("string"!=typeof e.claim||!e.claim)throw TypeError("names.claim must be a non-empty string");if("string"!=typeof e.source||!e.source)throw TypeError("names.source must be a non-empty string");o("string"==typeof t&&t,`${e.claim} must be a non-empty string`),o("string"==typeof r&&r,`${e.source} must be a non-empty string`);try{s=c(r,n,i)}catch(t){l=a("%s could not be validated (%s)",e.claim,t.message)}l=l||a("%s mismatch, expected %s, got: %s",e.claim,s,t),o.equal(s,t,l)},generate:c}},78328:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setModulusLength=t.weakMap=void 0,t.weakMap=new WeakMap;let r=(e,t)=>{let n=e.readUInt8(1);if((128&n)==0)return 0===t?n:r(e.subarray(2+n),t-1);let o=127&n;n=0;for(let t=0;t<o;t++)n<<=8,n|=e.readUInt8(2+t);return 0===t?n:r(e.subarray(2+n),t-1)},n=(e,t)=>{let n=e.readUInt8(1);return(128&n)==0?r(e.subarray(2),t):r(e.subarray(2+(127&n)),t)},o=e=>{var r,o;if(t.weakMap.has(e))return t.weakMap.get(e);let i=null!=(o=null==(r=e.asymmetricKeyDetails)?void 0:r.modulusLength)?o:n(e.export({format:"der",type:"pkcs1"}),+("private"===e.type))-1<<3;return t.weakMap.set(e,i),i};t.setModulusLength=(e,r)=>{t.weakMap.set(e,r)},t.default=(e,t)=>{if(2048>o(e))throw TypeError(`${t} requires key modulusLength to be 2048 bits or larger`)}},78680:(e,t)=>{"use strict";function r(e,t,...n){if(n.length>2){let t=n.pop();e+=`one of type ${n.join(", ")}, or ${t}.`}else 2===n.length?e+=`one of type ${n[0]} or ${n[1]}.`:e+=`of type ${n[0]}.`;return null==t?e+=` Received ${t}`:"function"==typeof t&&t.name?e+=` Received function ${t.name}`:"object"==typeof t&&null!=t&&t.constructor&&t.constructor.name&&(e+=` Received an instance of ${t.constructor.name}`),e}Object.defineProperty(t,"__esModule",{value:!0}),t.withAlg=void 0,t.default=(e,...t)=>r("Key must be ",e,...t),t.withAlg=function(e,t,...n){return r(`Key for the ${e} algorithm must be `,t,...n)}},78830:e=>{e.exports=e=>!!e&&e.constructor===Object},79963:(e,t,r)=>{"use strict";let n;Object.defineProperty(t,"__esModule",{value:!0});let o=r(55511);t.default=e=>(n||(n=new Set((0,o.getCiphers)())),n.has(e))},80028:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.jwkImport=t.jwkExport=t.rsaPssParams=t.oneShotCallback=void 0;let[r,n]=process.versions.node.split(".").map(e=>parseInt(e,10));t.oneShotCallback=r>=16||15===r&&n>=13,t.rsaPssParams=!("electron"in process.versions)&&(r>=17||16===r&&n>=9),t.jwkExport=r>=16||15===r&&n>=9,t.jwkImport=r>=16||15===r&&n>=12},80335:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(78680),o=r(37265),i=(e,t)=>{if(!(t instanceof Uint8Array)){if(!(0,o.default)(t))throw TypeError((0,n.withAlg)(e,t,...o.types,"Uint8Array"));if("secret"!==t.type)throw TypeError(`${o.types.join(" or ")} instances for symmetric algorithms must be of type "secret"`)}},a=(e,t,r)=>{if(!(0,o.default)(t))throw TypeError((0,n.withAlg)(e,t,...o.types));if("secret"===t.type)throw TypeError(`${o.types.join(" or ")} instances for asymmetric algorithms must not be of type "secret"`);if("sign"===r&&"public"===t.type)throw TypeError(`${o.types.join(" or ")} instances for asymmetric algorithm signing must be of type "private"`);if("decrypt"===r&&"public"===t.type)throw TypeError(`${o.types.join(" or ")} instances for asymmetric algorithm decryption must be of type "private"`);if(t.algorithm&&"verify"===r&&"private"===t.type)throw TypeError(`${o.types.join(" or ")} instances for asymmetric algorithm verifying must be of type "public"`);if(t.algorithm&&"encrypt"===r&&"private"===t.type)throw TypeError(`${o.types.join(" or ")} instances for asymmetric algorithm encryption must be of type "public"`)};t.default=(e,t,r)=>{e.startsWith("HS")||"dir"===e||e.startsWith("PBES2")||/^A\d{3}(?:GCM)?KW$/.test(e)?i(e,t):a(e,t,r)}},81454:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.compactDecrypt=void 0;let n=r(36736),o=r(99938),i=r(2594);t.compactDecrypt=async function(e,t,r){if(e instanceof Uint8Array&&(e=i.decoder.decode(e)),"string"!=typeof e)throw new o.JWEInvalid("Compact JWE must be a string or Uint8Array");let{0:a,1:s,2:c,3:l,4:u,length:d}=e.split(".");if(5!==d)throw new o.JWEInvalid("Invalid Compact JWE");let f=await (0,n.flattenedDecrypt)({ciphertext:l,iv:c||void 0,protected:a||void 0,tag:u||void 0,encrypted_key:s||void 0},t,r),p={plaintext:f.plaintext,protectedHeader:f.protectedHeader};return"function"==typeof t?{...p,key:f.key}:p}},81822:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(99938),o=r(2594),i=r(69717),a=r(86852),s=r(25110),c=e=>e.toLowerCase().replace(/^application\//,""),l=(e,t)=>"string"==typeof e?t.includes(e):!!Array.isArray(e)&&t.some(Set.prototype.has.bind(new Set(e)));t.default=(e,t,r={})=>{let u,d,{typ:f}=r;if(f&&("string"!=typeof e.typ||c(e.typ)!==c(f)))throw new n.JWTClaimValidationFailed('unexpected "typ" JWT header value',"typ","check_failed");try{u=JSON.parse(o.decoder.decode(t))}catch{}if(!(0,s.default)(u))throw new n.JWTInvalid("JWT Claims Set must be a top-level JSON object");let{requiredClaims:p=[],issuer:h,subject:y,audience:_,maxTokenAge:g}=r;for(let e of(void 0!==g&&p.push("iat"),void 0!==_&&p.push("aud"),void 0!==y&&p.push("sub"),void 0!==h&&p.push("iss"),new Set(p.reverse())))if(!(e in u))throw new n.JWTClaimValidationFailed(`missing required "${e}" claim`,e,"missing");if(h&&!(Array.isArray(h)?h:[h]).includes(u.iss))throw new n.JWTClaimValidationFailed('unexpected "iss" claim value',"iss","check_failed");if(y&&u.sub!==y)throw new n.JWTClaimValidationFailed('unexpected "sub" claim value',"sub","check_failed");if(_&&!l(u.aud,"string"==typeof _?[_]:_))throw new n.JWTClaimValidationFailed('unexpected "aud" claim value',"aud","check_failed");switch(typeof r.clockTolerance){case"string":d=(0,a.default)(r.clockTolerance);break;case"number":d=r.clockTolerance;break;case"undefined":d=0;break;default:throw TypeError("Invalid clockTolerance option type")}let{currentDate:b}=r,m=(0,i.default)(b||new Date);if((void 0!==u.iat||g)&&"number"!=typeof u.iat)throw new n.JWTClaimValidationFailed('"iat" claim must be a number',"iat","invalid");if(void 0!==u.nbf){if("number"!=typeof u.nbf)throw new n.JWTClaimValidationFailed('"nbf" claim must be a number',"nbf","invalid");if(u.nbf>m+d)throw new n.JWTClaimValidationFailed('"nbf" claim timestamp check failed',"nbf","check_failed")}if(void 0!==u.exp){if("number"!=typeof u.exp)throw new n.JWTClaimValidationFailed('"exp" claim must be a number',"exp","invalid");if(u.exp<=m-d)throw new n.JWTExpired('"exp" claim timestamp check failed',"exp","check_failed")}if(g){let e=m-u.iat;if(e-d>("number"==typeof g?g:(0,a.default)(g)))throw new n.JWTExpired('"iat" claim timestamp check failed (too far in the past)',"iat","check_failed");if(e<0-d)throw new n.JWTClaimValidationFailed('"iat" claim timestamp check failed (it should be in the past)',"iat","check_failed")}return u}},83872:(e,t,r)=>{"use strict";let n,{inspect:o}=r(28354),i=r(81630),a=r(55511),{strict:s}=r(12412),c=r(11723),l=r(79551),{URL:u,URLSearchParams:d}=r(79551),f=r(37544),p=r(76964),h=r(97843),y=r(913),_=r(70885),g=r(72115),b=r(38700),{assertSigningAlgValuesSupport:m,assertIssuerConfiguration:v}=r(2279),w=r(24740),x=r(78830),k=r(48160),E=r(24453),{OPError:S,RPError:A}=r(61408),O=r(16156),{random:P}=r(35571),T=r(9552),{CLOCK_TOLERANCE:C}=r(43325),{keystores:j}=r(55132),R=r(28577),I=r(93929),{authenticatedPost:J,resolveResponseType:W,resolveRedirectUri:H}=r(49450),{queryKeyStore:M}=r(73602),K=r(30641),[U,$]=process.version.slice(1).split(".").map(e=>parseInt(e,10)),D=U>=17||16===U&&$>=9,N=Symbol(),L=Symbol(),B=Symbol();function q(e){return w(e,"access_token","code","error_description","error_uri","error","expires_in","id_token","iss","response","session_state","state","token_type")}function z(e,t="Bearer"){return`${t} ${e}`}function F(e){let t=l.parse(e);return t.search?c.parse(t.search.substring(1)):{}}function G(e,t,r){if(void 0===e[r])throw new A({message:`missing required JWT property ${r}`,jwt:t})}function V(e){let t={client_id:this.client_id,scope:"openid",response_type:W.call(this),redirect_uri:H.call(this),...e};return Object.entries(t).forEach(([e,r])=>{null==r?delete t[e]:"claims"===e&&"object"==typeof r?t[e]=JSON.stringify(r):"resource"===e&&Array.isArray(r)?t[e]=r:"string"!=typeof r&&(t[e]=String(r))}),t}function X(e){if(!x(e)||!Array.isArray(e.keys)||e.keys.some(e=>!x(e)||!("kty"in e)))throw TypeError("jwks must be a JSON Web Key Set formatted object");return R.fromJWKS(e,{onlyPrivate:!0})}class Y{#e;#d;#f;#p;constructor(e,t,r={},n,o){if(this.#e=new Map,this.#d=e,this.#f=t,"string"!=typeof r.client_id||!r.client_id)throw TypeError("client_id is required");let i={grant_types:["authorization_code"],id_token_signed_response_alg:"RS256",authorization_signed_response_alg:"RS256",response_types:["code"],token_endpoint_auth_method:"client_secret_basic",...this.fapi1()?{grant_types:["authorization_code","implicit"],id_token_signed_response_alg:"PS256",authorization_signed_response_alg:"PS256",response_types:["code id_token"],tls_client_certificate_bound_access_tokens:!0,token_endpoint_auth_method:void 0}:void 0,...this.fapi2()?{id_token_signed_response_alg:"PS256",authorization_signed_response_alg:"PS256",token_endpoint_auth_method:void 0}:void 0,...r};if(this.fapi())switch(i.token_endpoint_auth_method){case"self_signed_tls_client_auth":case"tls_client_auth":break;case"private_key_jwt":if(!n)throw TypeError("jwks is required");break;case void 0:throw TypeError("token_endpoint_auth_method is required");default:throw TypeError("invalid or unsupported token_endpoint_auth_method")}if(this.fapi2()&&(i.tls_client_certificate_bound_access_tokens&&i.dpop_bound_access_tokens||!i.tls_client_certificate_bound_access_tokens&&!i.dpop_bound_access_tokens))throw TypeError("either tls_client_certificate_bound_access_tokens or dpop_bound_access_tokens must be set to true");if(!function(e,t,r){if(t.token_endpoint_auth_method||function(e,t){try{let r=e.issuer.token_endpoint_auth_methods_supported;!r.includes(t.token_endpoint_auth_method)&&r.includes("client_secret_post")&&(t.token_endpoint_auth_method="client_secret_post")}catch(e){}}(e,r),t.redirect_uri){if(t.redirect_uris)throw TypeError("provide a redirect_uri or redirect_uris, not both");r.redirect_uris=[t.redirect_uri],delete r.redirect_uri}if(t.response_type){if(t.response_types)throw TypeError("provide a response_type or response_types, not both");r.response_types=[t.response_type],delete r.response_type}}(this,r,i),m("token",this.issuer,i),["introspection","revocation"].forEach(e=>{!function(e,t,r){if(!t[`${e}_endpoint`])return;let n=r.token_endpoint_auth_method,o=r.token_endpoint_auth_signing_alg,i=`${e}_endpoint_auth_method`,a=`${e}_endpoint_auth_signing_alg`;void 0===r[i]&&void 0===r[a]&&(void 0!==n&&(r[i]=n),void 0!==o&&(r[a]=o))}(e,this.issuer,i),m(e,this.issuer,i)}),Object.entries(i).forEach(([e,t])=>{this.#e.set(e,t),this[e]||Object.defineProperty(this,e,{get(){return this.#e.get(e)},enumerable:!0})}),void 0!==n){let e=X.call(this,n);j.set(this,e)}null!=o&&o.additionalAuthorizedParties&&(this.#p=I(o.additionalAuthorizedParties)),this[C]=0}authorizationUrl(e={}){if(!x(e))throw TypeError("params must be a plain object");v(this.issuer,"authorization_endpoint");let t=new u(this.issuer.authorization_endpoint);for(let[r,n]of Object.entries(V.call(this,e)))if(Array.isArray(n))for(let e of(t.searchParams.delete(r),n))t.searchParams.append(r,e);else t.searchParams.set(r,n);return t.href.replace(/\+/g,"%20")}authorizationPost(e={}){if(!x(e))throw TypeError("params must be a plain object");let t=V.call(this,e),r=Object.keys(t).map(e=>`<input type="hidden" name="${e}" value="${t[e]}"/>`).join("\n");return`<!DOCTYPE html>
<head>
<title>Requesting Authorization</title>
</head>
<body onload="javascript:document.forms[0].submit()">
<form method="post" action="${this.issuer.authorization_endpoint}">
  ${r}
</form>
</body>
</html>`}endSessionUrl(e={}){let t;v(this.issuer,"end_session_endpoint");let{0:r,length:n}=this.post_logout_redirect_uris||[],{post_logout_redirect_uri:o=1===n?r:void 0}=e;if({id_token_hint:t,...e}=e,t instanceof E){if(!t.id_token)throw TypeError("id_token not present in TokenSet");t=t.id_token}let i=l.parse(this.issuer.end_session_endpoint),a=g(F(this.issuer.end_session_endpoint),e,{post_logout_redirect_uri:o,client_id:this.client_id},{id_token_hint:t});return Object.entries(a).forEach(([e,t])=>{null==t&&delete a[e]}),i.search=null,i.query=a,l.format(i)}callbackParams(e){let t=e instanceof i.IncomingMessage||e&&e.method&&e.url;if("string"!=typeof e&&!t)throw TypeError("#callbackParams only accepts string urls, http.IncomingMessage or a lookalike");if(!t)return q(F(e));switch(e.method){case"GET":return q(F(e.url));case"POST":if(void 0===e.body)throw TypeError("incoming message body missing, include a body parser prior to this method call");switch(typeof e.body){case"object":case"string":if(Buffer.isBuffer(e.body))return q(c.parse(e.body.toString("utf-8")));if("string"==typeof e.body)return q(c.parse(e.body));return q(e.body);default:throw TypeError("invalid IncomingMessage body object")}default:throw TypeError("invalid IncomingMessage method")}}async callback(e,t,r={},{exchangeBody:n,clientAssertionPayload:o,DPoP:i}={}){let a=q(t);if(!r.jarm||"response"in t){if("response"in t){let e=await this.decryptJARM(a.response);a=await this.validateJARM(e)}}else throw new A({message:"expected a JARM response",checks:r,params:a});if(this.default_max_age&&!r.max_age&&(r.max_age=this.default_max_age),a.state&&!r.state)throw TypeError("checks.state argument is missing");if(!a.state&&r.state)throw new A({message:"state missing from the response",checks:r,params:a});if(r.state!==a.state)throw new A({printf:["state mismatch, expected %s, got: %s",r.state,a.state],checks:r,params:a});if("iss"in a){if(v(this.issuer,"issuer"),a.iss!==this.issuer.issuer)throw new A({printf:["iss mismatch, expected %s, got: %s",this.issuer.issuer,a.iss],params:a})}else if(this.issuer.authorization_response_iss_parameter_supported&&!("id_token"in a)&&!("response"in t))throw new A({message:"iss missing from the response",params:a});if(a.error)throw new S(a);let s={code:["code"],id_token:["id_token"],token:["access_token","token_type"]};if(r.response_type){for(let e of r.response_type.split(" "))if("none"===e){if(a.code||a.id_token||a.access_token)throw new A({message:'unexpected params encountered for "none" response',checks:r,params:a})}else for(let t of s[e])if(!a[t])throw new A({message:`${t} missing from response`,checks:r,params:a})}if(a.id_token){let e=new E(a);if(await this.decryptIdToken(e),await this.validateIdToken(e,r.nonce,"authorization",r.max_age,r.state),!a.code)return e}if(a.code){let t=await this.grant({...n,grant_type:"authorization_code",code:a.code,redirect_uri:e,code_verifier:r.code_verifier},{clientAssertionPayload:o,DPoP:i});return await this.decryptIdToken(t),await this.validateIdToken(t,r.nonce,"token",r.max_age),a.session_state&&(t.session_state=a.session_state),t}return new E(a)}async oauthCallback(e,t,r={},{exchangeBody:n,clientAssertionPayload:o,DPoP:i}={}){let a=q(t);if(!r.jarm||"response"in t){if("response"in t){let e=await this.decryptJARM(a.response);a=await this.validateJARM(e)}}else throw new A({message:"expected a JARM response",checks:r,params:a});if(a.state&&!r.state)throw TypeError("checks.state argument is missing");if(!a.state&&r.state)throw new A({message:"state missing from the response",checks:r,params:a});if(r.state!==a.state)throw new A({printf:["state mismatch, expected %s, got: %s",r.state,a.state],checks:r,params:a});if("iss"in a){if(v(this.issuer,"issuer"),a.iss!==this.issuer.issuer)throw new A({printf:["iss mismatch, expected %s, got: %s",this.issuer.issuer,a.iss],params:a})}else if(this.issuer.authorization_response_iss_parameter_supported&&!("id_token"in a)&&!("response"in t))throw new A({message:"iss missing from the response",params:a});if(a.error)throw new S(a);if("string"==typeof a.id_token&&a.id_token.length)throw new A({message:"id_token detected in the response, you must use client.callback() instead of client.oauthCallback()",params:a});delete a.id_token;let s={code:["code"],token:["access_token","token_type"]};if(r.response_type)for(let e of r.response_type.split(" ")){if("none"===e&&(a.code||a.id_token||a.access_token))throw new A({message:'unexpected params encountered for "none" response',checks:r,params:a});if(s[e]){for(let t of s[e])if(!a[t])throw new A({message:`${t} missing from response`,checks:r,params:a})}}if(a.code){let t=await this.grant({...n,grant_type:"authorization_code",code:a.code,redirect_uri:e,code_verifier:r.code_verifier},{clientAssertionPayload:o,DPoP:i});if("string"==typeof t.id_token&&t.id_token.length)throw new A({message:"id_token detected in the response, you must use client.callback() instead of client.oauthCallback()",params:a});return delete t.id_token,t}return new E(a)}async decryptIdToken(e){if(!this.id_token_encrypted_response_alg)return e;let t=e;if(t instanceof E){if(!t.id_token)throw TypeError("id_token not present in TokenSet");t=t.id_token}let r=this.id_token_encrypted_response_alg,n=this.id_token_encrypted_response_enc,o=await this.decryptJWE(t,r,n);return e instanceof E?(e.id_token=o,e):o}async validateJWTUserinfo(e){let t=this.userinfo_signed_response_alg;return this.validateJWT(e,t,[])}async decryptJARM(e){if(!this.authorization_encrypted_response_alg)return e;let t=this.authorization_encrypted_response_alg,r=this.authorization_encrypted_response_enc;return this.decryptJWE(e,t,r)}async decryptJWTUserinfo(e){if(!this.userinfo_encrypted_response_alg)return e;let t=this.userinfo_encrypted_response_alg,r=this.userinfo_encrypted_response_enc;return this.decryptJWE(e,t,r)}async decryptJWE(e,t,r="A128CBC-HS256"){let n,o=JSON.parse(_.decode(e.split(".")[0]));if(o.alg!==t)throw new A({printf:["unexpected JWE alg received, expected %s, got: %s",t,o.alg],jwt:e});if(o.enc!==r)throw new A({printf:["unexpected JWE enc received, expected %s, got: %s",r,o.enc],jwt:e});let i=e=>new TextDecoder().decode(e.plaintext);if(t.match(/^(?:RSA|ECDH)/)){let t=await j.get(this),r=f.decodeProtectedHeader(e);for(let o of t.all({...r,use:"enc"}))if(n=await f.compactDecrypt(e,await o.keyObject(r.alg)).then(i,()=>{}))break}else n=await f.compactDecrypt(e,this.secretForAlg("dir"===t?r:t)).then(i,()=>{});if(!n)throw new A({message:"failed to decrypt JWE",jwt:e});return n}async validateIdToken(e,t,r,n,o){let i=e,a=this.id_token_signed_response_alg;if(i instanceof E){if(!i.id_token)throw TypeError("id_token not present in TokenSet");i=i.id_token}i=String(i);let s=O(),{protected:c,payload:l,key:u}=await this.validateJWT(i,a);if("number"==typeof n||n!==B&&this.require_auth_time){if(!l.auth_time)throw new A({message:"missing required JWT property auth_time",jwt:i});if("number"!=typeof l.auth_time)throw new A({message:"JWT auth_time claim must be a JSON numeric value",jwt:i})}if("number"==typeof n&&l.auth_time+n<s-this[C])throw new A({printf:["too much time has elapsed since the last End-User authentication, max_age %i, auth_time: %i, now %i",n,l.auth_time,s-this[C]],now:s,tolerance:this[C],auth_time:l.auth_time,jwt:i});if(t!==L&&(l.nonce||void 0!==t)&&l.nonce!==t)throw new A({printf:["nonce mismatch, expected %s, got: %s",t,l.nonce],jwt:i});if("authorization"===r){if(!l.at_hash&&e.access_token)throw new A({message:"missing required property at_hash",jwt:i});if(!l.c_hash&&e.code)throw new A({message:"missing required property c_hash",jwt:i});if(this.fapi1()&&!l.s_hash&&(e.state||o))throw new A({message:"missing required property s_hash",jwt:i});if(l.s_hash){if(!o)throw TypeError('cannot verify s_hash, "checks.state" property not provided');try{p.validate({claim:"s_hash",source:"state"},l.s_hash,o,c.alg,u.jwk&&u.jwk.crv)}catch(e){throw new A({message:e.message,jwt:i})}}}if(this.fapi()&&l.iat<s-3600)throw new A({printf:["JWT issued too far in the past, now %i, iat %i",s,l.iat],now:s,tolerance:this[C],iat:l.iat,jwt:i});if(e.access_token&&void 0!==l.at_hash)try{p.validate({claim:"at_hash",source:"access_token"},l.at_hash,e.access_token,c.alg,u.jwk&&u.jwk.crv)}catch(e){throw new A({message:e.message,jwt:i})}if(e.code&&void 0!==l.c_hash)try{p.validate({claim:"c_hash",source:"code"},l.c_hash,e.code,c.alg,u.jwk&&u.jwk.crv)}catch(e){throw new A({message:e.message,jwt:i})}return e}async validateJWT(e,t,r=["iss","sub","aud","exp","iat"]){let n,o,i,a="https://self-issued.me"===this.issuer.issuer,c=O();try{({header:n,payload:o}=y(e,{complete:!0}))}catch(t){throw new A({printf:["failed to decode JWT (%s: %s)",t.name,t.message],jwt:e})}if(n.alg!==t)throw new A({printf:["unexpected JWT alg received, expected %s, got: %s",t,n.alg],jwt:e});if(a&&(r=[...r,"sub_jwk"]),r.forEach(G.bind(void 0,o,e)),void 0!==o.iss){let t=this.issuer.issuer;if(this.#f&&(t=this.issuer.issuer.replace("{tenantid}",o.tid)),o.iss!==t)throw new A({printf:["unexpected iss value, expected %s, got: %s",t,o.iss],jwt:e})}if(void 0!==o.iat&&"number"!=typeof o.iat)throw new A({message:"JWT iat claim must be a JSON numeric value",jwt:e});if(void 0!==o.nbf){if("number"!=typeof o.nbf)throw new A({message:"JWT nbf claim must be a JSON numeric value",jwt:e});if(o.nbf>c+this[C])throw new A({printf:["JWT not active yet, now %i, nbf %i",c+this[C],o.nbf],now:c,tolerance:this[C],nbf:o.nbf,jwt:e})}if(void 0!==o.exp){if("number"!=typeof o.exp)throw new A({message:"JWT exp claim must be a JSON numeric value",jwt:e});if(c-this[C]>=o.exp)throw new A({printf:["JWT expired, now %i, exp %i",c-this[C],o.exp],now:c,tolerance:this[C],exp:o.exp,jwt:e})}if(void 0!==o.aud){if(Array.isArray(o.aud)){if(o.aud.length>1&&!o.azp)throw new A({message:"missing required JWT property azp",jwt:e});if(!o.aud.includes(this.client_id))throw new A({printf:["aud is missing the client_id, expected %s to be included in %j",this.client_id,o.aud],jwt:e})}else if(o.aud!==this.client_id)throw new A({printf:["aud mismatch, expected %s, got: %s",this.client_id,o.aud],jwt:e})}if(void 0!==o.azp){let t=this.#p;if(!(t="string"==typeof t?[this.client_id,t]:Array.isArray(t)?[this.client_id,...t]:[this.client_id]).includes(o.azp))throw new A({printf:["azp mismatch, got: %s",o.azp],jwt:e})}if(a){try{s(x(o.sub_jwk));let e=await f.importJWK(o.sub_jwk,n.alg);s.equal(e.type,"public"),i=[{keyObject:()=>e}]}catch(t){throw new A({message:"failed to use sub_jwk claim as an asymmetric JSON Web Key",jwt:e})}if(await f.calculateJwkThumbprint(o.sub_jwk)!==o.sub)throw new A({message:"failed to match the subject with sub_jwk",jwt:e})}else n.alg.startsWith("HS")?i=[this.secretForAlg(n.alg)]:"none"!==n.alg&&(i=await M.call(this.issuer,{...n,use:"sig"}));if(!i&&"none"===n.alg)return{protected:n,payload:o};for(let t of i){let r=await f.compactVerify(e,t instanceof Uint8Array?t:await t.keyObject(n.alg)).catch(()=>{});if(r)return{payload:o,protected:r.protectedHeader,key:t}}throw new A({message:"failed to validate JWT signature",jwt:e})}async refresh(e,{exchangeBody:t,clientAssertionPayload:r,DPoP:n}={}){let o=e;if(o instanceof E){if(!o.refresh_token)throw TypeError("refresh_token not present in TokenSet");o=o.refresh_token}let i=await this.grant({...t,grant_type:"refresh_token",refresh_token:String(o)},{clientAssertionPayload:r,DPoP:n});if(i.id_token&&(await this.decryptIdToken(i),await this.validateIdToken(i,L,"token",B),e instanceof E&&e.id_token)){let t=e.claims().sub,r=i.claims().sub;if(r!==t)throw new A({printf:["sub mismatch, expected %s, got: %s",t,r],jwt:i.id_token})}return i}async requestResource(e,t,{method:r,headers:n,body:o,DPoP:i,tokenType:a=i?"DPoP":t instanceof E?t.token_type:"Bearer"}={},s){if(t instanceof E){if(!t.access_token)throw TypeError("access_token not present in TokenSet");t=t.access_token}if(t){if("string"!=typeof t)throw TypeError("invalid access token provided")}else throw TypeError("no access token provided");let c={headers:{Authorization:z(t,a),...n},body:o},l=!!this.tls_client_certificate_bound_access_tokens,u=await T.call(this,{...c,responseType:"buffer",method:r,url:e},{accessToken:t,mTLS:l,DPoP:i}),d=u.headers["www-authenticate"];return s!==N&&d&&d.toLowerCase().startsWith("dpop ")&&"use_dpop_nonce"===b(d).error?this.requestResource(e,t,{method:r,headers:n,body:o,DPoP:i,tokenType:a}):u}async userinfo(e,{method:t="GET",via:r="header",tokenType:n,params:o,DPoP:i}={}){let a;v(this.issuer,"userinfo_endpoint");let c={tokenType:n,method:String(t).toUpperCase(),DPoP:i};if("GET"!==c.method&&"POST"!==c.method)throw TypeError("#userinfo() method can only be POST or a GET");if("body"===r&&"POST"!==c.method)throw TypeError("can only send body on POST");let l=!!(this.userinfo_signed_response_alg||this.userinfo_encrypted_response_alg);l?c.headers={Accept:"application/jwt"}:c.headers={Accept:"application/json"},this.tls_client_certificate_bound_access_tokens&&this.issuer.mtls_endpoint_aliases&&(a=this.issuer.mtls_endpoint_aliases.userinfo_endpoint),a=new u(a||this.issuer.userinfo_endpoint),"body"===r&&(c.headers.Authorization=void 0,c.headers["Content-Type"]="application/x-www-form-urlencoded",c.body=new d,c.body.append("access_token",e instanceof E?e.access_token:e)),o&&("GET"===c.method?Object.entries(o).forEach(([e,t])=>{a.searchParams.append(e,t)}):c.body?Object.entries(o).forEach(([e,t])=>{c.body.append(e,t)}):(c.body=new d,c.headers["Content-Type"]="application/x-www-form-urlencoded",Object.entries(o).forEach(([e,t])=>{c.body.append(e,t)}))),c.body&&(c.body=c.body.toString());let f=await this.requestResource(a,e,c),p=k(f,{bearer:!0});if(l){if(!/^application\/jwt/.test(f.headers["content-type"]))throw new A({message:"expected application/jwt response from the userinfo_endpoint",response:f});let e=f.body.toString(),t=await this.decryptJWTUserinfo(e);if(this.userinfo_signed_response_alg)({payload:p}=await this.validateJWTUserinfo(t));else try{p=JSON.parse(t),s(x(p))}catch(e){throw new A({message:"failed to parse userinfo JWE payload as JSON",jwt:t})}}else try{p=JSON.parse(f.body)}catch(e){throw Object.defineProperty(e,"response",{value:f}),e}if(e instanceof E&&e.id_token){let t=e.claims().sub;if(p.sub!==t)throw new A({printf:["userinfo sub mismatch, expected %s, got: %s",t,p.sub],body:p,jwt:e.id_token})}return p}encryptionSecret(e){let t=e<=256?"sha256":e<=384?"sha384":e<=512&&"sha512";if(!t)throw Error("unsupported symmetric encryption key derivation");return a.createHash(t).update(this.client_secret).digest().slice(0,e/8)}secretForAlg(e){if(!this.client_secret)throw TypeError("client_secret is required");return/^A(\d{3})(?:GCM)?KW$/.test(e)?this.encryptionSecret(parseInt(RegExp.$1,10)):/^A(\d{3})(?:GCM|CBC-HS(\d{3}))$/.test(e)?this.encryptionSecret(parseInt(RegExp.$2||RegExp.$1,10)):new TextEncoder().encode(this.client_secret)}async grant(e,{clientAssertionPayload:t,DPoP:r}={},n){let o;v(this.issuer,"token_endpoint");let i=await J.call(this,"token",{form:e,responseType:"json"},{clientAssertionPayload:t,DPoP:r});try{o=k(i)}catch(o){if(n!==N&&o instanceof S&&"use_dpop_nonce"===o.error)return this.grant(e,{clientAssertionPayload:t,DPoP:r},N);throw o}return new E(o)}async deviceAuthorization(e={},{exchangeBody:t,clientAssertionPayload:r,DPoP:n}={}){v(this.issuer,"device_authorization_endpoint"),v(this.issuer,"token_endpoint");let o=V.call(this,{client_id:this.client_id,redirect_uri:null,response_type:null,...e}),i=k(await J.call(this,"device_authorization",{responseType:"json",form:o},{clientAssertionPayload:r,endpointAuthMethod:"token"}));return new K({client:this,exchangeBody:t,clientAssertionPayload:r,response:i,maxAge:e.max_age,DPoP:n})}async revoke(e,t,{revokeBody:r,clientAssertionPayload:n}={}){if(v(this.issuer,"revocation_endpoint"),void 0!==t&&"string"!=typeof t)throw TypeError("hint must be a string");let o={...r,token:e};t&&(o.token_type_hint=t),k(await J.call(this,"revocation",{form:o},{clientAssertionPayload:n}),{body:!1})}async introspect(e,t,{introspectBody:r,clientAssertionPayload:n}={}){if(v(this.issuer,"introspection_endpoint"),void 0!==t&&"string"!=typeof t)throw TypeError("hint must be a string");let o={...r,token:e};return t&&(o.token_type_hint=t),k(await J.call(this,"introspection",{form:o,responseType:"json"},{clientAssertionPayload:n}))}static async register(e,t={}){let{initialAccessToken:r,jwks:n,...o}=t;return v(this.issuer,"registration_endpoint"),void 0===n||e.jwks||e.jwks_uri||(e.jwks=(await X.call(this,n)).toJWKS()),new this(k(await T.call(this,{headers:{Accept:"application/json",...r?{Authorization:z(r)}:void 0},responseType:"json",json:e,url:this.issuer.registration_endpoint,method:"POST"}),{statusCode:201,bearer:!0}),n,o)}get metadata(){return I(Object.fromEntries(this.#e.entries()))}static async fromUri(e,t,r,n){return new this(k(await T.call(this,{method:"GET",url:e,responseType:"json",headers:{Authorization:z(t),Accept:"application/json"}}),{bearer:!0}),r,n)}async requestObject(e={},{sign:t=this.request_object_signing_alg||"none",encrypt:{alg:r=this.request_object_encryption_alg,enc:n=this.request_object_encryption_enc||"A128CBC-HS256"}={}}={}){let o,i;if(!x(e))throw TypeError("requestObject must be a plain object");let a=O(),s={alg:t,typ:"oauth-authz-req+jwt"},c=JSON.stringify(g({},e,{iss:this.client_id,aud:this.issuer.issuer,client_id:this.client_id,jti:P(),iat:a,exp:a+300,...this.fapi()?{nbf:a}:void 0}));if("none"===t)o=[_.encode(JSON.stringify(s)),_.encode(c),""].join(".");else{let e=t.startsWith("HS");if(e)i=this.secretForAlg(t);else{let e=await j.get(this);if(!e)throw TypeError(`no keystore present for client, cannot sign using alg ${t}`);if(!(i=e.get({alg:t,use:"sig"})))throw TypeError(`no key to sign with found for alg ${t}`)}o=await new f.CompactSign(new TextEncoder().encode(c)).setProtectedHeader({...s,kid:e?void 0:i.jwk.kid}).sign(e?i:await i.keyObject(t))}if(!r)return o;let l={alg:r,enc:n,cty:"oauth-authz-req+jwt"};return l.alg.match(/^(RSA|ECDH)/)?[i]=await M.call(this.issuer,{alg:l.alg,use:"enc"},{allowMulti:!0}):i=this.secretForAlg("dir"===l.alg?l.enc:l.alg),new f.CompactEncrypt(new TextEncoder().encode(o)).setProtectedHeader({...l,kid:i instanceof Uint8Array?void 0:i.jwk.kid}).encrypt(i instanceof Uint8Array?i:await i.keyObject(l.alg))}async pushedAuthorizationRequest(e={},{clientAssertionPayload:t}={}){v(this.issuer,"pushed_authorization_request_endpoint");let r={..."request"in e?e:V.call(this,e),client_id:this.client_id},n=await J.call(this,"pushed_authorization_request",{responseType:"json",form:r},{clientAssertionPayload:t,endpointAuthMethod:"token"}),o=k(n,{statusCode:201});if(!("expires_in"in o))throw new A({message:"expected expires_in in Pushed Authorization Successful Response",response:n});if("number"!=typeof o.expires_in)throw new A({message:"invalid expires_in value in Pushed Authorization Successful Response",response:n});if(!("request_uri"in o))throw new A({message:"expected request_uri in Pushed Authorization Successful Response",response:n});if("string"!=typeof o.request_uri)throw new A({message:"invalid request_uri value in Pushed Authorization Successful Response",response:n});return o}get issuer(){return this.#d}[o.custom](){return`${this.constructor.name} ${o(this.metadata,{depth:1/0,colors:process.stdout.isTTY,compact:!1,sorted:!0})}`}fapi(){return this.fapi1()||this.fapi2()}fapi1(){return"FAPI1Client"===this.constructor.name}fapi2(){return"FAPI2Client"===this.constructor.name}async validateJARM(e){let t=this.authorization_signed_response_alg,{payload:r}=await this.validateJWT(e,t,["iss","exp","aud"]);return q(r)}async dpopProof(e,t,r){let o;if(!x(e))throw TypeError("payload must be a plain object");if(h(t))o=t;else if("CryptoKey"===t[Symbol.toStringTag])o=t;else if("node:crypto"===f.cryptoRuntime)o=a.createPrivateKey(t);else throw TypeError("unrecognized crypto runtime");if("private"!==o.type)throw TypeError('"DPoP" option must be a private key');let i=n.call(this,o,t);if(!i)throw TypeError("could not determine DPoP JWS Algorithm");return new f.SignJWT({ath:r?_.encode(a.createHash("sha256").update(r).digest()):void 0,...e}).setProtectedHeader({alg:i,typ:"dpop+jwt",jwk:await ee(o,t)}).setIssuedAt().setJti(P()).sign(o)}}function Z(e){switch(e.algorithm.name){case"Ed25519":case"Ed448":return"EdDSA";case"ECDSA":switch(e.algorithm.namedCurve){case"P-256":return"ES256";case"P-384":return"ES384";case"P-521":return"ES512"}break;case"RSASSA-PKCS1-v1_5":return`RS${e.algorithm.hash.name.slice(4)}`;case"RSA-PSS":return`PS${e.algorithm.hash.name.slice(4)}`;default:throw TypeError("unsupported DPoP private key")}}if("node:crypto"===f.cryptoRuntime){n=function(n,a){if("CryptoKey"===a[Symbol.toStringTag])return Z(n);switch(n.asymmetricKeyType){case"ed25519":case"ed448":return"EdDSA";case"ec":var s=n,c=a;switch("object"==typeof c&&"object"==typeof c.key&&c.key.crv){case"P-256":return"ES256";case"secp256k1":return"ES256K";case"P-384":return"ES384";case"P-512":return"ES512"}let l=s.export({format:"der",type:"pkcs8"}),u=l[1]<128?17:18,d=l[u],f=l.slice(u+1,u+1+d);if(f.equals(t))return"ES256";if(f.equals(r))return"ES384";if(f.equals(o))return"ES512";if(f.equals(i))return"ES256K";throw TypeError("unsupported DPoP private key curve");case"rsa":case D&&"rsa-pss":var p=n,h=a,y=this.issuer.dpop_signing_alg_values_supported;if("object"==typeof h&&"jwk"===h.format&&h.key&&h.key.alg)return h.key.alg;if(Array.isArray(y)){let t=y.filter(RegExp.prototype.test.bind(e));return"rsa-pss"===p.asymmetricKeyType&&(t=t.filter(e=>e.startsWith("PS"))),["PS256","PS384","PS512","RS256","RS384","RS384"].find(e=>t.includes(e))}return"PS256";default:throw TypeError("unsupported DPoP private key")}};let e=/^(?:RS|PS)(?:256|384|512)$/,t=Buffer.from([42,134,72,206,61,3,1,7]),r=Buffer.from([43,129,4,0,34]),o=Buffer.from([43,129,4,0,35]),i=Buffer.from([43,129,4,0,10])}else n=Z;let Q=new WeakMap;async function ee(e,t){if("node:crypto"===f.cryptoRuntime&&"object"==typeof t&&"object"==typeof t.key&&"jwk"===t.format)return w(t.key,"kty","crv","x","y","e","n");if(Q.has(t))return Q.get(t);let r=w(await f.exportJWK(e),"kty","crv","x","y","e","n");return(h(t)||"WebCryptoAPI"===f.cryptoRuntime)&&Q.set(t,r),r}e.exports=(e,t=!1)=>class extends Y{constructor(...r){super(e,t,...r)}static get issuer(){return e}},e.exports.BaseClient=Y},84838:e=>{e.exports=function(){return':root{--border-width:1px;--border-radius:0.5rem;--color-error:#c94b4b;--color-info:#157efb;--color-info-hover:#0f6ddb;--color-info-text:#fff}.__next-auth-theme-auto,.__next-auth-theme-light{--color-background:#ececec;--color-background-hover:hsla(0,0%,93%,.8);--color-background-card:#fff;--color-text:#000;--color-primary:#444;--color-control-border:#bbb;--color-button-active-background:#f9f9f9;--color-button-active-border:#aaa;--color-separator:#ccc}.__next-auth-theme-dark{--color-background:#161b22;--color-background-hover:rgba(22,27,34,.8);--color-background-card:#0d1117;--color-text:#fff;--color-primary:#ccc;--color-control-border:#555;--color-button-active-background:#060606;--color-button-active-border:#666;--color-separator:#444}@media (prefers-color-scheme:dark){.__next-auth-theme-auto{--color-background:#161b22;--color-background-hover:rgba(22,27,34,.8);--color-background-card:#0d1117;--color-text:#fff;--color-primary:#ccc;--color-control-border:#555;--color-button-active-background:#060606;--color-button-active-border:#666;--color-separator:#444}a.button,button{background-color:var(--provider-dark-bg,var(--color-background));color:var(--provider-dark-color,var(--color-primary))}a.button:hover,button:hover{background-color:var(--provider-dark-bg-hover,var(--color-background-hover))!important}#provider-logo{display:none!important}#provider-logo-dark{display:block!important;width:25px}}html{box-sizing:border-box}*,:after,:before{box-sizing:inherit;margin:0;padding:0}body{background-color:var(--color-background);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;margin:0;padding:0}h1{font-weight:400}h1,p{color:var(--color-text);margin-bottom:1.5rem;padding:0 1rem}form{margin:0;padding:0}label{font-weight:500;margin-bottom:.25rem;text-align:left}input[type],label{color:var(--color-text);display:block}input[type]{background:var(--color-background-card);border:var(--border-width) solid var(--color-control-border);border-radius:var(--border-radius);box-sizing:border-box;font-size:1rem;padding:.5rem 1rem;width:100%}input[type]:focus{box-shadow:none}p{font-size:1.1rem;line-height:2rem}a.button{line-height:1rem;text-decoration:none}a.button:link,a.button:visited{background-color:var(--color-background);color:var(--color-primary)}button span{flex-grow:1}a.button,button{align-items:center;background-color:var(--provider-bg);border-color:rgba(0,0,0,.1);border-radius:var(--border-radius);color:var(--provider-color,var(--color-primary));display:flex;font-size:1.1rem;font-weight:500;justify-content:center;min-height:62px;padding:.75rem 1rem;position:relative;transition:all .1s ease-in-out}a.button:hover,button:hover{background-color:var(--provider-bg-hover,var(--color-background-hover));cursor:pointer}a.button:active,button:active{cursor:pointer}a.button #provider-logo,button #provider-logo{display:block;width:25px}a.button #provider-logo-dark,button #provider-logo-dark{display:none}#submitButton{background-color:var(--brand-color,var(--color-info));color:var(--button-text-color,var(--color-info-text));width:100%}#submitButton:hover{background-color:var(--button-hover-bg,var(--color-info-hover))!important}a.site{color:var(--color-primary);font-size:1rem;line-height:2rem;text-decoration:none}a.site:hover{text-decoration:underline}.page{box-sizing:border-box;display:grid;height:100%;margin:0;padding:0;place-items:center;position:absolute;width:100%}.page>div{text-align:center}.error a.button{margin-top:.5rem;padding-left:2rem;padding-right:2rem}.error .message{margin-bottom:1.5rem}.signin input[type=text]{display:block;margin-left:auto;margin-right:auto}.signin hr{border:0;border-top:1px solid var(--color-separator);display:block;margin:2rem auto 1rem;overflow:visible}.signin hr:before{background:var(--color-background-card);color:#888;content:"or";padding:0 .4rem;position:relative;top:-.7rem}.signin .error{background:#f5f5f5;background:var(--color-error);border-radius:.3rem;font-weight:500}.signin .error p{color:var(--color-info-text);font-size:.9rem;line-height:1.2rem;padding:.5rem 1rem;text-align:left}.signin form,.signin>div{display:block}.signin form input[type],.signin>div input[type]{margin-bottom:.5rem}.signin form button,.signin>div button{width:100%}.signin .provider+.provider{margin-top:1rem}.logo{display:inline-block;margin:1.25rem 0;max-height:70px;max-width:150px}.card{background-color:var(--color-background-card);border-radius:2rem;padding:1.25rem 2rem}.card .header{color:var(--color-primary)}.section-header{color:var(--color-text)}@media screen and (min-width:450px){.card{margin:2rem 0;width:368px}}@media screen and (max-width:450px){.card{margin:1rem 0;width:343px}}'}},85634:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(55511),o=r(42891),i=r(51800),a=r(2594),s=r(99938),c=r(21606),l=r(34010),u=r(17996),d=r(50003),f=r(90061),p=r(78680),h=r(79963),y=r(37265);t.default=(e,t,r,_,g,b)=>{let m;if((0,u.isCryptoKey)(t))(0,d.checkEncCryptoKey)(t,e,"decrypt"),m=n.KeyObject.from(t);else if(t instanceof Uint8Array||(0,f.default)(t))m=t;else throw TypeError((0,p.default)(t,...y.types,"Uint8Array"));switch((0,i.default)(e,m),(0,o.default)(e,_),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return function(e,t,r,o,i,u){let d,p,y=parseInt(e.slice(1,4),10);(0,f.default)(t)&&(t=t.export());let _=t.subarray(y>>3),g=t.subarray(0,y>>3),b=parseInt(e.slice(-3),10),m=`aes-${y}-cbc`;if(!(0,h.default)(m))throw new s.JOSENotSupported(`alg ${e} is not supported by your javascript runtime`);let v=(0,l.default)(u,o,r,b,g,y);try{d=(0,c.default)(i,v)}catch{}if(!d)throw new s.JWEDecryptionFailed;try{let e=(0,n.createDecipheriv)(m,_,o);p=(0,a.concat)(e.update(r),e.final())}catch{}if(!p)throw new s.JWEDecryptionFailed;return p}(e,m,r,_,g,b);case"A128GCM":case"A192GCM":case"A256GCM":return function(e,t,r,o,i,a){let c=parseInt(e.slice(1,4),10),l=`aes-${c}-gcm`;if(!(0,h.default)(l))throw new s.JOSENotSupported(`alg ${e} is not supported by your javascript runtime`);try{let e=(0,n.createDecipheriv)(l,t,o,{authTagLength:16});e.setAuthTag(i),a.byteLength&&e.setAAD(a,{plaintextLength:r.length});let s=e.update(r);return e.final(),s}catch{throw new s.JWEDecryptionFailed}}(e,m,r,_,g,b);default:throw new s.JOSENotSupported("Unsupported JWE Content Encryption Algorithm")}}},85663:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>A});var n=r(55511),o=null;function i(e,t){if("number"!=typeof(e=e||g))throw Error("Illegal arguments: "+typeof e+", "+typeof t);e<4?e=4:e>31&&(e=31);var r=[];return r.push("$2b$"),e<10&&r.push("0"),r.push(e.toString()),r.push("$"),r.push(h(function(e){try{return crypto.getRandomValues(new Uint8Array(e))}catch{}try{return n.randomBytes(e)}catch{}if(!o)throw Error("Neither WebCryptoAPI nor a crypto module is available. Use bcrypt.setRandomFallback to set an alternative");return o(e)}(_),_)),r.join("")}function a(e,t,r){if("function"==typeof t&&(r=t,t=void 0),"function"==typeof e&&(r=e,e=void 0),void 0===e)e=g;else if("number"!=typeof e)throw Error("illegal arguments: "+typeof e);function n(t){u(function(){try{t(null,i(e))}catch(e){t(e)}})}if(!r)return new Promise(function(e,t){n(function(r,n){if(r)return void t(r);e(n)})});if("function"!=typeof r)throw Error("Illegal callback: "+typeof r);n(r)}function s(e,t){if(void 0===t&&(t=g),"number"==typeof t&&(t=i(t)),"string"!=typeof e||"string"!=typeof t)throw Error("Illegal arguments: "+typeof e+", "+typeof t);return S(e,t)}function c(e,t,r,n){function o(r){"string"==typeof e&&"number"==typeof t?a(t,function(t,o){S(e,o,r,n)}):"string"==typeof e&&"string"==typeof t?S(e,t,r,n):u(r.bind(this,Error("Illegal arguments: "+typeof e+", "+typeof t)))}if(!r)return new Promise(function(e,t){o(function(r,n){if(r)return void t(r);e(n)})});if("function"!=typeof r)throw Error("Illegal callback: "+typeof r);o(r)}function l(e,t){for(var r=e.length^t.length,n=0;n<e.length;++n)r|=e.charCodeAt(n)^t.charCodeAt(n);return 0===r}var u="undefined"!=typeof process&&process&&"function"==typeof process.nextTick?"function"==typeof setImmediate?setImmediate:process.nextTick:setTimeout;function d(e){for(var t=0,r=0,n=0;n<e.length;++n)(r=e.charCodeAt(n))<128?t+=1:r<2048?t+=2:(64512&r)==55296&&(64512&e.charCodeAt(n+1))==56320?(++n,t+=4):t+=3;return t}var f="./ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),p=[-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,1,54,55,56,57,58,59,60,61,62,63,-1,-1,-1,-1,-1,-1,-1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,-1,-1,-1,-1,-1,-1,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,-1,-1,-1,-1,-1];function h(e,t){var r,n,o=0,i=[];if(t<=0||t>e.length)throw Error("Illegal len: "+t);for(;o<t;){if(r=255&e[o++],i.push(f[r>>2&63]),r=(3&r)<<4,o>=t||(r|=(n=255&e[o++])>>4&15,i.push(f[63&r]),r=(15&n)<<2,o>=t)){i.push(f[63&r]);break}r|=(n=255&e[o++])>>6&3,i.push(f[63&r]),i.push(f[63&n])}return i.join("")}function y(e,t){var r,n,o,i,a,s=0,c=e.length,l=0,u=[];if(t<=0)throw Error("Illegal len: "+t);for(;s<c-1&&l<t&&(r=(a=e.charCodeAt(s++))<p.length?p[a]:-1,n=(a=e.charCodeAt(s++))<p.length?p[a]:-1,-1!=r&&-1!=n)&&(i=r<<2>>>0|(48&n)>>4,u.push(String.fromCharCode(i)),!(++l>=t||s>=c||-1==(o=(a=e.charCodeAt(s++))<p.length?p[a]:-1)||(i=(15&n)<<4>>>0|(60&o)>>2,u.push(String.fromCharCode(i)),++l>=t||s>=c)));){;i=(3&o)<<6>>>0|((a=e.charCodeAt(s++))<p.length?p[a]:-1),u.push(String.fromCharCode(i)),++l}var d=[];for(s=0;s<l;s++)d.push(u[s].charCodeAt(0));return d}var _=16,g=10,b=[0x243f6a88,0x85a308d3,0x13198a2e,0x3707344,0xa4093822,0x299f31d0,0x82efa98,0xec4e6c89,0x452821e6,0x38d01377,0xbe5466cf,0x34e90c6c,0xc0ac29b7,0xc97c50dd,0x3f84d5b5,0xb5470917,0x9216d5d9,0x8979fb1b],m=[0xd1310ba6,0x98dfb5ac,0x2ffd72db,0xd01adfb7,0xb8e1afed,0x6a267e96,0xba7c9045,0xf12c7f99,0x24a19947,0xb3916cf7,0x801f2e2,0x858efc16,0x636920d8,0x71574e69,0xa458fea3,0xf4933d7e,0xd95748f,0x728eb658,0x718bcd58,0x82154aee,0x7b54a41d,0xc25a59b5,0x9c30d539,0x2af26013,0xc5d1b023,0x286085f0,0xca417918,0xb8db38ef,0x8e79dcb0,0x603a180e,0x6c9e0e8b,0xb01e8a3e,0xd71577c1,0xbd314b27,0x78af2fda,0x55605c60,0xe65525f3,0xaa55ab94,0x57489862,0x63e81440,0x55ca396a,0x2aab10b6,0xb4cc5c34,0x1141e8ce,0xa15486af,0x7c72e993,0xb3ee1411,0x636fbc2a,0x2ba9c55d,0x741831f6,0xce5c3e16,0x9b87931e,0xafd6ba33,0x6c24cf5c,0x7a325381,0x28958677,0x3b8f4898,0x6b4bb9af,0xc4bfe81b,0x66282193,0x61d809cc,0xfb21a991,0x487cac60,0x5dec8032,0xef845d5d,0xe98575b1,0xdc262302,0xeb651b88,0x23893e81,0xd396acc5,0xf6d6ff3,0x83f44239,0x2e0b4482,0xa4842004,0x69c8f04a,0x9e1f9b5e,0x21c66842,0xf6e96c9a,0x670c9c61,0xabd388f0,0x6a51a0d2,0xd8542f68,0x960fa728,0xab5133a3,0x6eef0b6c,0x137a3be4,0xba3bf050,0x7efb2a98,0xa1f1651d,0x39af0176,0x66ca593e,0x82430e88,0x8cee8619,0x456f9fb4,0x7d84a5c3,0x3b8b5ebe,0xe06f75d8,0x85c12073,0x401a449f,0x56c16aa6,0x4ed3aa62,0x363f7706,0x1bfedf72,0x429b023d,0x37d0d724,0xd00a1248,0xdb0fead3,0x49f1c09b,0x75372c9,0x80991b7b,0x25d479d8,0xf6e8def7,0xe3fe501a,0xb6794c3b,0x976ce0bd,0x4c006ba,0xc1a94fb6,0x409f60c4,0x5e5c9ec2,0x196a2463,0x68fb6faf,0x3e6c53b5,0x1339b2eb,0x3b52ec6f,0x6dfc511f,0x9b30952c,0xcc814544,0xaf5ebd09,0xbee3d004,0xde334afd,0x660f2807,0x192e4bb3,0xc0cba857,0x45c8740f,0xd20b5f39,0xb9d3fbdb,0x5579c0bd,0x1a60320a,0xd6a100c6,0x402c7279,0x679f25fe,0xfb1fa3cc,0x8ea5e9f8,0xdb3222f8,0x3c7516df,0xfd616b15,0x2f501ec8,0xad0552ab,0x323db5fa,0xfd238760,0x53317b48,0x3e00df82,0x9e5c57bb,0xca6f8ca0,0x1a87562e,0xdf1769db,0xd542a8f6,0x287effc3,0xac6732c6,0x8c4f5573,0x695b27b0,0xbbca58c8,0xe1ffa35d,0xb8f011a0,0x10fa3d98,0xfd2183b8,0x4afcb56c,0x2dd1d35b,0x9a53e479,0xb6f84565,0xd28e49bc,0x4bfb9790,0xe1ddf2da,0xa4cb7e33,0x62fb1341,0xcee4c6e8,0xef20cada,0x36774c01,0xd07e9efe,0x2bf11fb4,0x95dbda4d,0xae909198,0xeaad8e71,0x6b93d5a0,0xd08ed1d0,0xafc725e0,0x8e3c5b2f,0x8e7594b7,0x8ff6e2fb,0xf2122b64,0x8888b812,0x900df01c,0x4fad5ea0,0x688fc31c,0xd1cff191,0xb3a8c1ad,0x2f2f2218,0xbe0e1777,0xea752dfe,0x8b021fa1,0xe5a0cc0f,0xb56f74e8,0x18acf3d6,0xce89e299,0xb4a84fe0,0xfd13e0b7,0x7cc43b81,0xd2ada8d9,0x165fa266,0x80957705,0x93cc7314,0x211a1477,0xe6ad2065,0x77b5fa86,0xc75442f5,0xfb9d35cf,0xebcdaf0c,0x7b3e89a0,0xd6411bd3,0xae1e7e49,2428461,0x2071b35e,0x226800bb,0x57b8e0af,0x2464369b,0xf009b91e,0x5563911d,0x59dfa6aa,0x78c14389,0xd95a537f,0x207d5ba2,0x2e5b9c5,0x83260376,0x6295cfa9,0x11c81968,0x4e734a41,0xb3472dca,0x7b14a94a,0x1b510052,0x9a532915,0xd60f573f,0xbc9bc6e4,0x2b60a476,0x81e67400,0x8ba6fb5,0x571be91f,0xf296ec6b,0x2a0dd915,0xb6636521,0xe7b9f9b6,0xff34052e,0xc5855664,0x53b02d5d,0xa99f8fa1,0x8ba4799,0x6e85076a,0x4b7a70e9,0xb5b32944,0xdb75092e,0xc4192623,290971e4,0x49a7df7d,0x9cee60b8,0x8fedb266,0xecaa8c71,0x699a17ff,0x5664526c,0xc2b19ee1,0x193602a5,0x75094c29,0xa0591340,0xe4183a3e,0x3f54989a,0x5b429d65,0x6b8fe4d6,0x99f73fd6,0xa1d29c07,0xefe830f5,0x4d2d38e6,0xf0255dc1,0x4cdd2086,0x8470eb26,0x6382e9c6,0x21ecc5e,0x9686b3f,0x3ebaefc9,0x3c971814,0x6b6a70a1,0x687f3584,0x52a0e286,0xb79c5305,0xaa500737,0x3e07841c,0x7fdeae5c,0x8e7d44ec,0x5716f2b8,0xb03ada37,0xf0500c0d,0xf01c1f04,0x200b3ff,0xae0cf51a,0x3cb574b2,0x25837a58,0xdc0921bd,0xd19113f9,0x7ca92ff6,0x94324773,0x22f54701,0x3ae5e581,0x37c2dadc,0xc8b57634,0x9af3dda7,0xa9446146,0xfd0030e,0xecc8c73e,0xa4751e41,0xe238cd99,0x3bea0e2f,0x3280bba1,0x183eb331,0x4e548b38,0x4f6db908,0x6f420d03,0xf60a04bf,0x2cb81290,0x24977c79,0x5679b072,0xbcaf89af,0xde9a771f,0xd9930810,0xb38bae12,0xdccf3f2e,0x5512721f,0x2e6b7124,0x501adde6,0x9f84cd87,0x7a584718,0x7408da17,0xbc9f9abc,0xe94b7d8c,0xec7aec3a,0xdb851dfa,0x63094366,0xc464c3d2,0xef1c1847,0x3215d908,0xdd433b37,0x24c2ba16,0x12a14d43,0x2a65c451,0x50940002,0x133ae4dd,0x71dff89e,0x10314e55,0x81ac77d6,0x5f11199b,0x43556f1,0xd7a3c76b,0x3c11183b,0x5924a509,0xf28fe6ed,0x97f1fbfa,0x9ebabf2c,0x1e153c6e,0x86e34570,0xeae96fb1,0x860e5e0a,0x5a3e2ab3,0x771fe71c,0x4e3d06fa,0x2965dcb9,0x99e71d0f,0x803e89d6,0x5266c825,0x2e4cc978,0x9c10b36a,0xc6150eba,0x94e2ea78,0xa5fc3c53,0x1e0a2df4,0xf2f74ea7,0x361d2b3d,0x1939260f,0x19c27960,0x5223a708,0xf71312b6,0xebadfe6e,0xeac31f66,0xe3bc4595,0xa67bc883,0xb17f37d1,0x18cff28,0xc332ddef,0xbe6c5aa5,0x65582185,0x68ab9802,0xeecea50f,0xdb2f953b,0x2aef7dad,0x5b6e2f84,0x1521b628,0x29076170,0xecdd4775,0x619f1510,0x13cca830,0xeb61bd96,0x334fe1e,0xaa0363cf,0xb5735c90,0x4c70a239,0xd59e9e0b,0xcbaade14,0xeecc86bc,0x60622ca7,0x9cab5cab,0xb2f3846e,0x648b1eaf,0x19bdf0ca,0xa02369b9,0x655abb50,0x40685a32,0x3c2ab4b3,0x319ee9d5,0xc021b8f7,0x9b540b19,0x875fa099,0x95f7997e,0x623d7da8,0xf837889a,0x97e32d77,0x11ed935f,0x16681281,0xe358829,0xc7e61fd6,0x96dedfa1,0x7858ba99,0x57f584a5,0x1b227263,0x9b83c3ff,0x1ac24696,0xcdb30aeb,0x532e3054,0x8fd948e4,0x6dbc3128,0x58ebf2ef,0x34c6ffea,0xfe28ed61,0xee7c3c73,0x5d4a14d9,0xe864b7e3,0x42105d14,0x203e13e0,0x45eee2b6,0xa3aaabea,0xdb6c4f15,0xfacb4fd0,0xc742f442,0xef6abbb5,0x654f3b1d,0x41cd2105,0xd81e799e,0x86854dc7,0xe44b476a,0x3d816250,0xcf62a1f2,0x5b8d2646,0xfc8883a0,0xc1c7b6a3,0x7f1524c3,0x69cb7492,0x47848a0b,0x5692b285,0x95bbf00,0xad19489d,0x1462b174,0x23820e00,0x58428d2a,0xc55f5ea,0x1dadf43e,0x233f7061,0x3372f092,0x8d937e41,0xd65fecf1,0x6c223bdb,0x7cde3759,0xcbee7460,0x4085f2a7,0xce77326e,0xa6078084,0x19f8509e,0xe8efd855,0x61d99735,0xa969a7aa,0xc50c06c2,0x5a04abfc,0x800bcadc,0x9e447a2e,0xc3453484,0xfdd56705,0xe1e9ec9,0xdb73dbd3,0x105588cd,0x675fda79,0xe3674340,0xc5c43465,0x713e38d8,0x3d28f89e,0xf16dff20,0x153e21e7,0x8fb03d4a,0xe6e39f2b,0xdb83adf7,0xe93d5a68,0x948140f7,0xf64c261c,0x94692934,0x411520f7,0x7602d4f7,0xbcf46b2e,0xd4a20068,0xd4082471,0x3320f46a,0x43b7d4b7,0x500061af,0x1e39f62e,0x97244546,0x14214f74,0xbf8b8840,0x4d95fc1d,0x96b591af,0x70f4ddd3,0x66a02f45,0xbfbc09ec,0x3bd9785,0x7fac6dd0,0x31cb8504,0x96eb27b3,0x55fd3941,0xda2547e6,0xabca0a9a,0x28507825,0x530429f4,0xa2c86da,0xe9b66dfb,0x68dc1462,0xd7486900,0x680ec0a4,0x27a18dee,0x4f3ffea2,0xe887ad8c,0xb58ce006,0x7af4d6b6,0xaace1e7c,0xd3375fec,0xce78a399,0x406b2a42,0x20fe9e35,0xd9f385b9,0xee39d7ab,0x3b124e8b,0x1dc9faf7,0x4b6d1856,0x26a36631,0xeae397b2,0x3a6efa74,0xdd5b4332,0x6841e7f7,0xca7820fb,0xfb0af54e,0xd8feb397,0x454056ac,0xba489527,0x55533a3a,0x20838d87,0xfe6ba9b7,0xd096954b,0x55a867bc,0xa1159a58,0xcca92963,0x99e1db33,0xa62a4a56,0x3f3125f9,0x5ef47e1c,0x9029317c,0xfdf8e802,0x4272f70,0x80bb155c,0x5282ce3,0x95c11548,0xe4c66d22,0x48c1133f,0xc70f86dc,0x7f9c9ee,0x41041f0f,0x404779a4,0x5d886e17,0x325f51eb,0xd59bc0d1,0xf2bcc18f,0x41113564,0x257b7834,0x602a9c60,0xdff8e8a3,0x1f636c1b,0xe12b4c2,0x2e1329e,0xaf664fd1,0xcad18115,0x6b2395e0,0x333e92e1,0x3b240b62,0xeebeb922,0x85b2a20e,0xe6ba0d99,0xde720c8c,0x2da2f728,0xd0127845,0x95b794fd,0x647d0862,0xe7ccf5f0,0x5449a36f,0x877d48fa,0xc39dfd27,0xf33e8d1e,0xa476341,0x992eff74,0x3a6f6eab,0xf4f8fd37,0xa812dc60,0xa1ebddf8,0x991be14c,0xdb6e6b0d,0xc67b5510,0x6d672c37,0x2765d43b,0xdcd0e804,0xf1290dc7,0xcc00ffa3,0xb5390f92,0x690fed0b,0x667b9ffb,0xcedb7d9c,0xa091cf0b,0xd9155ea3,0xbb132f88,0x515bad24,0x7b9479bf,0x763bd6eb,0x37392eb3,0xcc115979,0x8026e297,0xf42e312d,0x6842ada7,0xc66a2b3b,0x12754ccc,0x782ef11c,0x6a124237,0xb79251e7,0x6a1bbe6,0x4bfb6350,0x1a6b1018,0x11caedfa,0x3d25bdd8,0xe2e1c3c9,0x44421659,0xa121386,0xd90cec6e,0xd5abea2a,0x64af674e,0xda86a85f,0xbebfe988,0x64e4c3fe,0x9dbc8057,0xf0f7c086,0x60787bf8,0x6003604d,0xd1fd8346,0xf6381fb0,0x7745ae04,0xd736fccc,0x83426b33,0xf01eab71,0xb0804187,0x3c005e5f,0x77a057be,0xbde8ae24,0x55464299,0xbf582e61,0x4e58f48f,0xf2ddfda2,0xf474ef38,0x8789bdc2,0x5366f9c3,0xc8b38e74,0xb475f255,0x46fcd9b9,0x7aeb2661,0x8b1ddf84,0x846a0e79,0x915f95e2,0x466e598e,0x20b45770,0x8cd55591,0xc902de4c,0xb90bace1,0xbb8205d0,0x11a86248,0x7574a99e,0xb77f19b6,0xe0a9dc09,0x662d09a1,0xc4324633,0xe85a1f02,0x9f0be8c,0x4a99a025,0x1d6efe10,0x1ab93d1d,0xba5a4df,0xa186f20f,0x2868f169,0xdcb7da83,0x573906fe,0xa1e2ce9b,0x4fcd7f52,0x50115e01,0xa70683fa,0xa002b5c4,0xde6d027,0x9af88c27,0x773f8641,0xc3604c06,0x61a806b5,0xf0177a28,0xc0f586e0,6314154,0x30dc7d62,0x11e69ed7,0x2338ea63,0x53c2dd94,0xc2c21634,0xbbcbee56,0x90bcb6de,0xebfc7da1,0xce591d76,0x6f05e409,0x4b7c0188,0x39720a3d,0x7c927c24,0x86e3725f,0x724d9db9,0x1ac15bb4,0xd39eb8fc,0xed545578,0x8fca5b5,0xd83d7cd3,0x4dad0fc4,0x1e50ef5e,0xb161e6f8,0xa28514d9,0x6c51133c,0x6fd5c7e7,0x56e14ec4,0x362abfce,0xddc6c837,0xd79a3234,0x92638212,0x670efa8e,0x406000e0,0x3a39ce37,0xd3faf5cf,0xabc27737,0x5ac52d1b,0x5cb0679e,0x4fa33742,0xd3822740,0x99bc9bbe,0xd5118e9d,0xbf0f7315,0xd62d1c7e,0xc700c47b,0xb78c1b6b,0x21a19045,0xb26eb1be,0x6a366eb4,0x5748ab2f,0xbc946e79,0xc6a376d2,0x6549c2c8,0x530ff8ee,0x468dde7d,0xd5730a1d,0x4cd04dc6,0x2939bbdb,0xa9ba4650,0xac9526e8,0xbe5ee304,0xa1fad5f0,0x6a2d519a,0x63ef8ce2,0x9a86ee22,0xc089c2b8,0x43242ef6,0xa51e03aa,0x9cf2d0a4,0x83c061ba,0x9be96a4d,0x8fe51550,0xba645bd6,0x2826a2f9,0xa73a3ae1,0x4ba99586,0xef5562e9,0xc72fefd3,0xf752f7da,0x3f046f69,0x77fa0a59,0x80e4a915,0x87b08601,0x9b09e6ad,0x3b3ee593,0xe990fd5a,0x9e34d797,0x2cf0b7d9,0x22b8b51,0x96d5ac3a,0x17da67d,0xd1cf3ed6,0x7c7d2d28,0x1f9f25cf,0xadf2b89b,0x5ad6b472,0x5a88f54c,0xe029ac71,0xe019a5e6,0x47b0acfd,0xed93fa9b,0xe8d3c48d,0x283b57cc,0xf8d56629,0x79132e28,0x785f0191,0xed756055,0xf7960e44,0xe3d35e8c,0x15056dd4,0x88f46dba,0x3a16125,0x564f0bd,0xc3eb9e15,0x3c9057a2,0x97271aec,0xa93a072a,0x1b3f6d9b,0x1e6321f5,0xf59c66fb,0x26dcf319,0x7533d928,0xb155fdf5,0x3563482,0x8aba3cbb,0x28517711,0xc20ad9f8,0xabcc5167,0xccad925f,0x4de81751,0x3830dc8e,0x379d5862,0x9320f991,0xea7a90c2,0xfb3e7bce,0x5121ce64,0x774fbe32,0xa8b6e37e,0xc3293d46,0x48de5369,0x6413e680,0xa2ae0810,0xdd6db224,0x69852dfd,0x9072166,0xb39a460a,0x6445c0dd,0x586cdecf,0x1c20c8ae,0x5bbef7dd,0x1b588d40,0xccd2017f,0x6bb4e3bb,0xdda26a7e,0x3a59ff45,0x3e350a44,0xbcb4cdd5,0x72eacea8,0xfa6484bb,0x8d6612ae,0xbf3c6f47,0xd29be463,0x542f5d9e,0xaec2771b,0xf64e6370,0x740e0d8d,0xe75b1357,0xf8721671,0xaf537d5d,0x4040cb08,0x4eb4e2cc,0x34d2466a,0x115af84,3786409e3,0x95983a1d,0x6b89fb4,0xce6ea048,0x6f3f3b82,0x3520ab82,0x11a1d4b,0x277227f8,0x611560b1,0xe7933fdc,0xbb3a792b,0x344525bd,0xa08839e1,0x51ce794b,0x2f32c9b7,0xa01fbac9,0xe01cc87e,0xbcc7d1f6,0xcf0111c3,0xa1e8aac7,0x1a908749,0xd44fbd9a,0xd0dadecb,0xd50ada38,0x339c32a,0xc6913667,0x8df9317c,0xe0b12b4f,0xf79e59b7,0x43f5bb3a,0xf2d519ff,0x27d9459c,0xbf97222c,0x15e6fc2a,0xf91fc71,0x9b941525,0xfae59361,0xceb69ceb,0xc2a86459,0x12baa8d1,0xb6c1075e,0xe3056a0c,0x10d25065,0xcb03a442,0xe0ec6e0e,0x1698db3b,0x4c98a0be,0x3278e964,0x9f1f9532,0xe0d392df,0xd3a0342b,0x8971f21e,0x1b0a7441,0x4ba3348c,0xc5be7120,0xc37632d8,0xdf359f8d,0x9b992f2e,0xe60b6f47,0xfe3f11d,0xe54cda54,0x1edad891,0xce6279cf,0xcd3e7e6f,0x1618b166,0xfd2c1d05,0x848fd2c5,0xf6fb2299,0xf523f357,0xa6327623,0x93a83531,0x56cccd02,0xacf08162,0x5a75ebb5,0x6e163697,0x88d273cc,0xde966292,0x81b949d0,0x4c50901b,0x71c65614,0xe6c6c7bd,0x327a140a,0x45e1d006,0xc3f27b9a,0xc9aa53fd,0x62a80f00,0xbb25bfe2,0x35bdd2f6,0x71126905,0xb2040222,0xb6cbcf7c,0xcd769c2b,0x53113ec0,0x1640e3d3,0x38abbd60,0x2547adf0,0xba38209c,0xf746ce76,0x77afa1c5,0x20756060,0x85cbfe4e,0x8ae88dd8,0x7aaaf9b0,0x4cf9aa7e,0x1948c25c,0x2fb8a8c,0x1c36ae4,0xd6ebe1f9,0x90d4f869,0xa65cdea0,0x3f09252d,0xc208e69f,0xb74e6132,0xce77e25b,0x578fdfe3,0x3ac372e6],v=[0x4f727068,0x65616e42,0x65686f6c,0x64657253,0x63727944,0x6f756274];function w(e,t,r,n){var o,i=e[t],a=e[t+1];return i^=r[0],a^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[1],i^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[2],a^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[3],i^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[4],a^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[5],i^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[6],a^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[7],i^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[8],a^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[9],i^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[10],a^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[11],i^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[12],a^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[13],i^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[14],a^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[15],i^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[16],e[t]=a^r[17],e[t+1]=i,e}function x(e,t){for(var r=0,n=0;r<4;++r)n=n<<8|255&e[t],t=(t+1)%e.length;return{key:n,offp:t}}function k(e,t,r){for(var n,o=0,i=[0,0],a=t.length,s=r.length,c=0;c<a;c++)o=(n=x(e,o)).offp,t[c]=t[c]^n.key;for(c=0;c<a;c+=2)i=w(i,0,t,r),t[c]=i[0],t[c+1]=i[1];for(c=0;c<s;c+=2)i=w(i,0,t,r),r[c]=i[0],r[c+1]=i[1]}function E(e,t,r,n,o){var i,a,s=v.slice(),c=s.length;if(r<4||r>31){if(a=Error("Illegal number of rounds (4-31): "+r),n)return void u(n.bind(this,a));throw a}if(t.length!==_){if(a=Error("Illegal salt length: "+t.length+" != "+_),n)return void u(n.bind(this,a));throw a}r=1<<r>>>0;var l,d,f,p=0;function h(){if(o&&o(p/r),p<r)for(var i=Date.now();p<r&&(p+=1,k(e,l,d),k(t,l,d),!(Date.now()-i>100)););else{for(p=0;p<64;p++)for(f=0;f<c>>1;f++)w(s,f<<1,l,d);var a=[];for(p=0;p<c;p++)a.push((s[p]>>24&255)>>>0),a.push((s[p]>>16&255)>>>0),a.push((s[p]>>8&255)>>>0),a.push((255&s[p])>>>0);return n?void n(null,a):a}n&&u(h)}if("function"==typeof Int32Array?(l=new Int32Array(b),d=new Int32Array(m)):(l=b.slice(),d=m.slice()),!function(e,t,r,n){for(var o,i=0,a=[0,0],s=r.length,c=n.length,l=0;l<s;l++)i=(o=x(t,i)).offp,r[l]=r[l]^o.key;for(l=0,i=0;l<s;l+=2)i=(o=x(e,i)).offp,a[0]^=o.key,i=(o=x(e,i)).offp,a[1]^=o.key,a=w(a,0,r,n),r[l]=a[0],r[l+1]=a[1];for(l=0;l<c;l+=2)i=(o=x(e,i)).offp,a[0]^=o.key,i=(o=x(e,i)).offp,a[1]^=o.key,a=w(a,0,r,n),n[l]=a[0],n[l+1]=a[1]}(t,e,l,d),void 0!==n)h();else for(;;)if(void 0!==(i=h()))return i||[]}function S(e,t,r,n){if("string"!=typeof e||"string"!=typeof t){if(o=Error("Invalid string / salt: Not a string"),r)return void u(r.bind(this,o));throw o}if("$"!==t.charAt(0)||"2"!==t.charAt(1)){if(o=Error("Invalid salt version: "+t.substring(0,2)),r)return void u(r.bind(this,o));throw o}if("$"===t.charAt(2))i="\0",a=3;else{if("a"!==(i=t.charAt(2))&&"b"!==i&&"y"!==i||"$"!==t.charAt(3)){if(o=Error("Invalid salt revision: "+t.substring(2,4)),r)return void u(r.bind(this,o));throw o}a=4}if(t.charAt(a+2)>"$"){if(o=Error("Missing salt rounds"),r)return void u(r.bind(this,o));throw o}var o,i,a,s=10*parseInt(t.substring(a,a+1),10)+parseInt(t.substring(a+1,a+2),10),c=t.substring(a+3,a+25),l=function(e){for(var t,r,n=0,o=Array(d(e)),i=0,a=e.length;i<a;++i)(t=e.charCodeAt(i))<128?o[n++]=t:(t<2048?o[n++]=t>>6|192:((64512&t)==55296&&(64512&(r=e.charCodeAt(i+1)))==56320?(t=65536+((1023&t)<<10)+(1023&r),++i,o[n++]=t>>18|240,o[n++]=t>>12&63|128):o[n++]=t>>12|224,o[n++]=t>>6&63|128),o[n++]=63&t|128);return o}(e+=i>="a"?"\0":""),f=y(c,_);function p(e){var t=[];return t.push("$2"),i>="a"&&t.push(i),t.push("$"),s<10&&t.push("0"),t.push(s.toString()),t.push("$"),t.push(h(f,f.length)),t.push(h(e,4*v.length-1)),t.join("")}if(void 0===r)return p(E(l,f,s));E(l,f,s,function(e,t){e?r(e,null):r(null,p(t))},n)}let A={setRandomFallback:function(e){o=e},genSaltSync:i,genSalt:a,hashSync:s,hash:c,compareSync:function(e,t){if("string"!=typeof e||"string"!=typeof t)throw Error("Illegal arguments: "+typeof e+", "+typeof t);return 60===t.length&&l(s(e,t.substring(0,t.length-31)),t)},compare:function(e,t,r,n){function o(r){return"string"!=typeof e||"string"!=typeof t?void u(r.bind(this,Error("Illegal arguments: "+typeof e+", "+typeof t))):60!==t.length?void u(r.bind(this,null,!1)):void c(e,t.substring(0,29),function(e,n){e?r(e):r(null,l(n,t))},n)}if(!r)return new Promise(function(e,t){o(function(r,n){if(r)return void t(r);e(n)})});if("function"!=typeof r)throw Error("Illegal callback: "+typeof r);o(r)},getRounds:function(e){if("string"!=typeof e)throw Error("Illegal arguments: "+typeof e);return parseInt(e.split("$")[2],10)},getSalt:function(e){if("string"!=typeof e)throw Error("Illegal arguments: "+typeof e);if(60!==e.length)throw Error("Illegal hash length: "+e.length+" != 60");return e.substring(0,29)},truncates:function(e){if("string"!=typeof e)throw Error("Illegal arguments: "+typeof e);return d(e)>72},encodeBase64:function(e,t){return h(e,t)},decodeBase64:function(e,t){return y(e,t)}}},86280:(e,t,r)=>{"use strict";Object.defineProperty(t,"b",{enumerable:!0,get:function(){return d}});let n=r(92584),o=r(29294),i=r(63033),a=r(84971),s=r(80023),c=r(68388),l=r(76926),u=(r(44523),r(8719));function d(){let e=o.workAsyncStorage.getStore(),t=i.workUnitAsyncStorage.getStore();if(e){if(t&&"after"===t.phase&&!(0,u.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E367",enumerable:!1,configurable:!0});if(e.forceStatic)return p(n.HeadersAdapter.seal(new Headers({})));if(t){if("cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E304",enumerable:!1,configurable:!0});else if("unstable-cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E127",enumerable:!1,configurable:!0})}if(e.dynamicShouldError)throw Object.defineProperty(new s.StaticGenBailoutError(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E525",enumerable:!1,configurable:!0});if(t)if("prerender"===t.type){var r=e.route,l=t;let n=f.get(l);if(n)return n;let o=(0,c.makeHangingPromise)(l.renderSignal,"`headers()`");return f.set(l,o),Object.defineProperties(o,{append:{value:function(){let e=`\`headers().append(${h(arguments[0])}, ...)\``,t=_(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},delete:{value:function(){let e=`\`headers().delete(${h(arguments[0])})\``,t=_(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},get:{value:function(){let e=`\`headers().get(${h(arguments[0])})\``,t=_(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},has:{value:function(){let e=`\`headers().has(${h(arguments[0])})\``,t=_(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},set:{value:function(){let e=`\`headers().set(${h(arguments[0])}, ...)\``,t=_(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},getSetCookie:{value:function(){let e="`headers().getSetCookie()`",t=_(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},forEach:{value:function(){let e="`headers().forEach(...)`",t=_(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},keys:{value:function(){let e="`headers().keys()`",t=_(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},values:{value:function(){let e="`headers().values()`",t=_(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},entries:{value:function(){let e="`headers().entries()`",t=_(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},[Symbol.iterator]:{value:function(){let e="`headers()[Symbol.iterator]()`",t=_(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}}}),o}else"prerender-ppr"===t.type?(0,a.postponeWithTracking)(e.route,"headers",t.dynamicTracking):"prerender-legacy"===t.type&&(0,a.throwToInterruptStaticGeneration)("headers",e,t);(0,a.trackDynamicDataInDynamicRender)(e,t)}return p((0,i.getExpectedRequestStore)("headers").headers)}let f=new WeakMap;function p(e){let t=f.get(e);if(t)return t;let r=Promise.resolve(e);return f.set(e,r),Object.defineProperties(r,{append:{value:e.append.bind(e)},delete:{value:e.delete.bind(e)},get:{value:e.get.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},getSetCookie:{value:e.getSetCookie.bind(e)},forEach:{value:e.forEach.bind(e)},keys:{value:e.keys.bind(e)},values:{value:e.values.bind(e)},entries:{value:e.entries.bind(e)},[Symbol.iterator]:{value:e[Symbol.iterator].bind(e)}}),r}function h(e){return"string"==typeof e?`'${e}'`:"..."}let y=(0,l.createDedupedByCallsiteServerErrorLoggerDev)(_);function _(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E277",enumerable:!1,configurable:!0})}},86305:(e,t,r)=>{var n=r(28339),o=r(74113);e.exports=function(e,t,r,i,a){return new o(n().w(e,t,r,i),a||Promise)},e.exports.__esModule=!0,e.exports.default=e.exports},86505:(e,t,r)=>{let n=r(78830);e.exports=function e(t,...r){for(let o of r)if(n(o))for(let[r,i]of Object.entries(o))"__proto__"!==r&&"constructor"!==r&&(n(t[r])&&n(i)?t[r]=e(t[r],i):void 0!==i&&(t[r]=i));return t}},86852:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let r=/^(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)$/i;t.default=e=>{let t=r.exec(e);if(!t)throw TypeError("Invalid time period format");let n=parseFloat(t[1]);switch(t[2].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":return Math.round(n);case"minute":case"minutes":case"min":case"mins":case"m":return Math.round(60*n);case"hour":case"hours":case"hr":case"hrs":case"h":return Math.round(3600*n);case"day":case"days":case"d":return Math.round(86400*n);case"week":case"weeks":case"w":return Math.round(604800*n);default:return Math.round(0x1e187e0*n)}}},87451:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getBody=o,t.setCookie=function(e,t){var r;let o=null!=(r=e.getHeader("Set-Cookie"))?r:[];Array.isArray(o)||(o=[o]);let{name:i,value:a,options:s}=t,c=(0,n.serialize)(i,a,s);o.push(c),e.setHeader("Set-Cookie",o)},t.toResponse=function(e){var t,r,o;let i=new Headers(null==(t=e.headers)?void 0:t.reduce((e,{key:t,value:r})=>(e[t]=r,e),{}));null==(r=e.cookies)||r.forEach(e=>{let{name:t,value:r,options:o}=e,a=(0,n.serialize)(t,r,o);i.has("Set-Cookie")?i.append("Set-Cookie",a):i.set("Set-Cookie",a)});let a=e.body;"application/json"===i.get("content-type")?a=JSON.stringify(e.body):"application/x-www-form-urlencoded"===i.get("content-type")&&(a=new URLSearchParams(e.body).toString());let s=new Response(a,{headers:i,status:e.redirect?302:null!=(o=e.status)?o:200});return e.redirect&&s.headers.set("Location",e.redirect),s};var n=r(25538);async function o(e){if(!("body"in e)||!e.body||"POST"!==e.method)return;let t=e.headers.get("content-type");return null!=t&&t.includes("application/json")?await e.json():null!=t&&t.includes("application/x-www-form-urlencoded")?Object.fromEntries(new URLSearchParams(await e.text())):void 0}},88062:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.unwrap=t.wrap=void 0;let n=r(79428),o=r(55511),i=r(99938),a=r(2594),s=r(17996),c=r(50003),l=r(90061),u=r(78680),d=r(79963),f=r(37265);function p(e,t){if(e.symmetricKeySize<<3!==parseInt(t.slice(1,4),10))throw TypeError(`Invalid key size for alg: ${t}`)}function h(e,t,r){if((0,l.default)(e))return e;if(e instanceof Uint8Array)return(0,o.createSecretKey)(e);if((0,s.isCryptoKey)(e))return(0,c.checkEncCryptoKey)(e,t,r),o.KeyObject.from(e);throw TypeError((0,u.default)(e,...f.types,"Uint8Array"))}t.wrap=(e,t,r)=>{let s=parseInt(e.slice(1,4),10),c=`aes${s}-wrap`;if(!(0,d.default)(c))throw new i.JOSENotSupported(`alg ${e} is not supported either by JOSE or your javascript runtime`);let l=h(t,e,"wrapKey");p(l,e);let u=(0,o.createCipheriv)(c,l,n.Buffer.alloc(8,166));return(0,a.concat)(u.update(r),u.final())},t.unwrap=(e,t,r)=>{let s=parseInt(e.slice(1,4),10),c=`aes${s}-wrap`;if(!(0,d.default)(c))throw new i.JOSENotSupported(`alg ${e} is not supported either by JOSE or your javascript runtime`);let l=h(t,e,"unwrapKey");p(l,e);let u=(0,o.createDecipheriv)(c,l,n.Buffer.alloc(8,166));return(0,a.concat)(u.update(r),u.final())}},89227:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(99938);t.default=function(e){switch(e){case"HS256":return"sha256";case"HS384":return"sha384";case"HS512":return"sha512";default:throw new n.JOSENotSupported(`alg ${e} is not supported either by JOSE or your javascript runtime`)}}},89412:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createCSRFToken=function({options:e,cookieValue:t,isPost:r,bodyValue:o}){if(t){let[i,a]=t.split("|");if(a===(0,n.createHash)("sha256").update(`${i}${e.secret}`).digest("hex"))return{csrfTokenVerified:r&&i===o,csrfToken:i}}let i=(0,n.randomBytes)(32).toString("hex"),a=(0,n.createHash)("sha256").update(`${i}${e.secret}`).digest("hex");return{cookie:`${i}|${a}`,csrfToken:i}};var n=r(55511)},89831:(e,t,r)=>{var n=r(86305);e.exports=function(e,t,r,o,i){var a=n(e,t,r,o,i);return a.next().then(function(e){return e.done?e.value:a.next()})},e.exports.__esModule=!0,e.exports.default=e.exports},90061:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(55511),o=r(28354);t.default=o.types.isKeyObject?e=>o.types.isKeyObject(e):e=>null!=e&&e instanceof n.KeyObject},90253:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},91428:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createRemoteJWKSet=void 0;let n=r(95339),o=r(99938),i=r(2055);class a extends i.LocalJWKSet{constructor(e,t){if(super({keys:[]}),this._jwks=void 0,!(e instanceof URL))throw TypeError("url must be an instance of URL");this._url=new URL(e.href),this._options={agent:null==t?void 0:t.agent,headers:null==t?void 0:t.headers},this._timeoutDuration="number"==typeof(null==t?void 0:t.timeoutDuration)?null==t?void 0:t.timeoutDuration:5e3,this._cooldownDuration="number"==typeof(null==t?void 0:t.cooldownDuration)?null==t?void 0:t.cooldownDuration:3e4,this._cacheMaxAge="number"==typeof(null==t?void 0:t.cacheMaxAge)?null==t?void 0:t.cacheMaxAge:6e5}coolingDown(){return"number"==typeof this._jwksTimestamp&&Date.now()<this._jwksTimestamp+this._cooldownDuration}fresh(){return"number"==typeof this._jwksTimestamp&&Date.now()<this._jwksTimestamp+this._cacheMaxAge}async getKey(e,t){this._jwks&&this.fresh()||await this.reload();try{return await super.getKey(e,t)}catch(r){if(r instanceof o.JWKSNoMatchingKey&&!1===this.coolingDown())return await this.reload(),super.getKey(e,t);throw r}}async reload(){this._pendingFetch&&("undefined"!=typeof WebSocketPair||"undefined"!=typeof navigator&&"Cloudflare-Workers"===navigator.userAgent||"undefined"!=typeof EdgeRuntime&&"vercel"===EdgeRuntime)&&(this._pendingFetch=void 0),this._pendingFetch||(this._pendingFetch=(0,n.default)(this._url,this._timeoutDuration,this._options).then(e=>{if(!(0,i.isJWKSLike)(e))throw new o.JWKSInvalid("JSON Web Key Set malformed");this._jwks={keys:e.keys},this._jwksTimestamp=Date.now(),this._pendingFetch=void 0}).catch(e=>{throw this._pendingFetch=void 0,e})),await this._pendingFetch}}t.createRemoteJWKSet=function(e,t){let r=new a(e,t);return async function(e,t){return r.getKey(e,t)}}},92296:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(99938);t.default=function(e){if(!(e instanceof Uint8Array)||e.length<8)throw new n.JWEInvalid("PBES2 Salt Input must be 8 or more octets")}},92562:(e,t,r)=>{"use strict";let n;Object.defineProperty(t,"__esModule",{value:!0});let o=r(55511),i=r(28354),a=r(63898),s=r(89227),c=r(42567),l=r(15055);n=o.sign.length>3?(0,i.promisify)(o.sign):o.sign,t.default=async(e,t,r)=>{let i=(0,l.default)(e,t,"sign");if(e.startsWith("HS")){let t=o.createHmac((0,s.default)(e),i);return t.update(r),t.digest()}return n((0,a.default)(e),r,(0,c.default)(e,i))}},92584:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HeadersAdapter:function(){return i},ReadonlyHeadersError:function(){return o}});let n=r(43763);class o extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new o}}class i extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,o){if("symbol"==typeof r)return n.ReflectAdapter.get(t,r,o);let i=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===i);if(void 0!==a)return n.ReflectAdapter.get(t,a,o)},set(t,r,o,i){if("symbol"==typeof r)return n.ReflectAdapter.set(t,r,o,i);let a=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===a);return n.ReflectAdapter.set(t,s??r,o,i)},has(t,r){if("symbol"==typeof r)return n.ReflectAdapter.has(t,r);let o=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===o);return void 0!==i&&n.ReflectAdapter.has(t,i)},deleteProperty(t,r){if("symbol"==typeof r)return n.ReflectAdapter.deleteProperty(t,r);let o=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===o);return void 0===i||n.ReflectAdapter.deleteProperty(t,i)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return o.callable;default:return n.ReflectAdapter.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new i(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},93069:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=(e,t)=>{if(void 0!==t&&(!Array.isArray(t)||t.some(e=>"string"!=typeof e)))throw TypeError(`"${e}" option must be an array of strings`);if(t)return new Set(t)}},93346:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.fromX509=t.fromSPKI=t.fromPKCS8=t.toPKCS8=t.toSPKI=void 0;let n=r(55511),o=r(79428),i=r(17996),a=r(90061),s=r(78680),c=r(37265),l=(e,t,r)=>{let o;if((0,i.isCryptoKey)(r)){if(!r.extractable)throw TypeError("CryptoKey is not extractable");o=n.KeyObject.from(r)}else if((0,a.default)(r))o=r;else throw TypeError((0,s.default)(r,...c.types));if(o.type!==e)throw TypeError(`key is not a ${e} key`);return o.export({format:"pem",type:t})};t.toSPKI=e=>l("public","spki",e),t.toPKCS8=e=>l("private","pkcs8",e),t.fromPKCS8=e=>(0,n.createPrivateKey)({key:o.Buffer.from(e.replace(/(?:-----(?:BEGIN|END) PRIVATE KEY-----|\s)/g,""),"base64"),type:"pkcs8",format:"der"}),t.fromSPKI=e=>(0,n.createPublicKey)({key:o.Buffer.from(e.replace(/(?:-----(?:BEGIN|END) PUBLIC KEY-----|\s)/g,""),"base64"),type:"spki",format:"der"}),t.fromX509=e=>(0,n.createPublicKey)({key:e,type:"spki",format:"pem"})},93466:(e,t,r)=>{"use strict";function n(e){var t=this;if(t instanceof n||(t=new n),t.tail=null,t.head=null,t.length=0,e&&"function"==typeof e.forEach)e.forEach(function(e){t.push(e)});else if(arguments.length>0)for(var r=0,o=arguments.length;r<o;r++)t.push(arguments[r]);return t}function o(e,t,r,n){if(!(this instanceof o))return new o(e,t,r,n);this.list=n,this.value=e,t?(t.next=this,this.prev=t):this.prev=null,r?(r.prev=this,this.next=r):this.next=null}e.exports=n,n.Node=o,n.create=n,n.prototype.removeNode=function(e){if(e.list!==this)throw Error("removing node which does not belong to this list");var t=e.next,r=e.prev;return t&&(t.prev=r),r&&(r.next=t),e===this.head&&(this.head=t),e===this.tail&&(this.tail=r),e.list.length--,e.next=null,e.prev=null,e.list=null,t},n.prototype.unshiftNode=function(e){if(e!==this.head){e.list&&e.list.removeNode(e);var t=this.head;e.list=this,e.next=t,t&&(t.prev=e),this.head=e,this.tail||(this.tail=e),this.length++}},n.prototype.pushNode=function(e){if(e!==this.tail){e.list&&e.list.removeNode(e);var t=this.tail;e.list=this,e.prev=t,t&&(t.next=e),this.tail=e,this.head||(this.head=e),this.length++}},n.prototype.push=function(){for(var e,t,r=0,n=arguments.length;r<n;r++){e=this,t=arguments[r],e.tail=new o(t,e.tail,null,e),e.head||(e.head=e.tail),e.length++}return this.length},n.prototype.unshift=function(){for(var e,t,r=0,n=arguments.length;r<n;r++){e=this,t=arguments[r],e.head=new o(t,null,e.head,e),e.tail||(e.tail=e.head),e.length++}return this.length},n.prototype.pop=function(){if(this.tail){var e=this.tail.value;return this.tail=this.tail.prev,this.tail?this.tail.next=null:this.head=null,this.length--,e}},n.prototype.shift=function(){if(this.head){var e=this.head.value;return this.head=this.head.next,this.head?this.head.prev=null:this.tail=null,this.length--,e}},n.prototype.forEach=function(e,t){t=t||this;for(var r=this.head,n=0;null!==r;n++)e.call(t,r.value,n,this),r=r.next},n.prototype.forEachReverse=function(e,t){t=t||this;for(var r=this.tail,n=this.length-1;null!==r;n--)e.call(t,r.value,n,this),r=r.prev},n.prototype.get=function(e){for(var t=0,r=this.head;null!==r&&t<e;t++)r=r.next;if(t===e&&null!==r)return r.value},n.prototype.getReverse=function(e){for(var t=0,r=this.tail;null!==r&&t<e;t++)r=r.prev;if(t===e&&null!==r)return r.value},n.prototype.map=function(e,t){t=t||this;for(var r=new n,o=this.head;null!==o;)r.push(e.call(t,o.value,this)),o=o.next;return r},n.prototype.mapReverse=function(e,t){t=t||this;for(var r=new n,o=this.tail;null!==o;)r.push(e.call(t,o.value,this)),o=o.prev;return r},n.prototype.reduce=function(e,t){var r,n=this.head;if(arguments.length>1)r=t;else if(this.head)n=this.head.next,r=this.head.value;else throw TypeError("Reduce of empty list with no initial value");for(var o=0;null!==n;o++)r=e(r,n.value,o),n=n.next;return r},n.prototype.reduceReverse=function(e,t){var r,n=this.tail;if(arguments.length>1)r=t;else if(this.tail)n=this.tail.prev,r=this.tail.value;else throw TypeError("Reduce of empty list with no initial value");for(var o=this.length-1;null!==n;o--)r=e(r,n.value,o),n=n.prev;return r},n.prototype.toArray=function(){for(var e=Array(this.length),t=0,r=this.head;null!==r;t++)e[t]=r.value,r=r.next;return e},n.prototype.toArrayReverse=function(){for(var e=Array(this.length),t=0,r=this.tail;null!==r;t++)e[t]=r.value,r=r.prev;return e},n.prototype.slice=function(e,t){(t=t||this.length)<0&&(t+=this.length),(e=e||0)<0&&(e+=this.length);var r=new n;if(t<e||t<0)return r;e<0&&(e=0),t>this.length&&(t=this.length);for(var o=0,i=this.head;null!==i&&o<e;o++)i=i.next;for(;null!==i&&o<t;o++,i=i.next)r.push(i.value);return r},n.prototype.sliceReverse=function(e,t){(t=t||this.length)<0&&(t+=this.length),(e=e||0)<0&&(e+=this.length);var r=new n;if(t<e||t<0)return r;e<0&&(e=0),t>this.length&&(t=this.length);for(var o=this.length,i=this.tail;null!==i&&o>t;o--)i=i.prev;for(;null!==i&&o>e;o--,i=i.prev)r.push(i.value);return r},n.prototype.splice=function(e,t,...r){e>this.length&&(e=this.length-1),e<0&&(e=this.length+e);for(var n=0,i=this.head;null!==i&&n<e;n++)i=i.next;for(var a=[],n=0;i&&n<t;n++)a.push(i.value),i=this.removeNode(i);null===i&&(i=this.tail),i!==this.head&&i!==this.tail&&(i=i.prev);for(var n=0;n<r.length;n++)i=function(e,t,r){var n=t===e.head?new o(r,null,t,e):new o(r,t,t.next,e);return null===n.next&&(e.tail=n),null===n.prev&&(e.head=n),e.length++,n}(this,i,r[n]);return a},n.prototype.reverse=function(){for(var e=this.head,t=this.tail,r=e;null!==r;r=r.prev){var n=r.prev;r.prev=r.next,r.next=n}return this.head=t,this.tail=e,this};try{r(63580)(n)}catch(e){}},93929:e=>{e.exports=globalThis.structuredClone||(e=>JSON.parse(JSON.stringify(e)))},94069:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{MutableRequestCookiesAdapter:function(){return f},ReadonlyRequestCookiesError:function(){return s},RequestCookiesAdapter:function(){return c},appendMutableCookies:function(){return d},areCookiesMutableInCurrentPhase:function(){return h},getModifiedCookieValues:function(){return u},responseCookiesToRequestCookies:function(){return _},wrapWithMutableAccessCheck:function(){return p}});let n=r(23158),o=r(43763),i=r(29294),a=r(63033);class s extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new s}}class c{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return s.callable;default:return o.ReflectAdapter.get(e,t,r)}}})}}let l=Symbol.for("next.mutated.cookies");function u(e){let t=e[l];return t&&Array.isArray(t)&&0!==t.length?t:[]}function d(e,t){let r=u(t);if(0===r.length)return!1;let o=new n.ResponseCookies(e),i=o.getAll();for(let e of r)o.set(e);for(let e of i)o.set(e);return!0}class f{static wrap(e,t){let r=new n.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let a=[],s=new Set,c=()=>{let e=i.workAsyncStorage.getStore();if(e&&(e.pathWasRevalidated=!0),a=r.getAll().filter(e=>s.has(e.name)),t){let e=[];for(let t of a){let r=new n.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}},u=new Proxy(r,{get(e,t,r){switch(t){case l:return a;case"delete":return function(...t){s.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.delete(...t),u}finally{c()}};case"set":return function(...t){s.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t),u}finally{c()}};default:return o.ReflectAdapter.get(e,t,r)}}});return u}}function p(e){let t=new Proxy(e,{get(e,r,n){switch(r){case"delete":return function(...r){return y("cookies().delete"),e.delete(...r),t};case"set":return function(...r){return y("cookies().set"),e.set(...r),t};default:return o.ReflectAdapter.get(e,r,n)}}});return t}function h(e){return"action"===e.phase}function y(e){if(!h((0,a.getExpectedRequestStore)(e)))throw new s}function _(e){let t=new n.RequestCookies(new Headers);for(let r of e.getAll())t.set(r);return t}},94387:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.hkdf=void 0;let n=r(46821);function o(e,t){if("string"==typeof e)return new TextEncoder().encode(e);if(!(e instanceof Uint8Array))throw TypeError(`"${t}"" must be an instance of Uint8Array or a string`);return e}async function i(e,t,r,i,a){return(0,n.default)(function(e){switch(e){case"sha256":case"sha384":case"sha512":case"sha1":return e;default:throw TypeError('unsupported "digest" value')}}(e),function(e){let t=o(e,"ikm");if(!t.byteLength)throw TypeError('"ikm" must be at least one byte in length');return t}(t),o(r,"salt"),function(e){let t=o(e,"info");if(t.byteLength>1024)throw TypeError('"info" must not contain more than 1024 bytes');return t}(i),function(e,t){if("number"!=typeof e||!Number.isInteger(e)||e<1)throw TypeError('"keylen" must be a positive integer');if(e>255*(parseInt(t.substr(3),10)>>3||20))throw TypeError('"keylen" too large');return e}(a,e))}t.hkdf=i,t.default=i},95339:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(81630),o=r(55591),i=r(94735),a=r(99938),s=r(2594);t.default=async(e,t,r)=>{let c;switch(e.protocol){case"https:":c=o.get;break;case"http:":c=n.get;break;default:throw TypeError("Unsupported URL protocol.")}let{agent:l,headers:u}=r,d=c(e.href,{agent:l,timeout:t,headers:u}),[f]=await Promise.race([(0,i.once)(d,"response"),(0,i.once)(d,"timeout")]);if(!f)throw d.destroy(),new a.JWKSTimeout;if(200!==f.statusCode)throw new a.JOSEError("Expected 200 OK from the JSON Web Key Set HTTP response");let p=[];for await(let e of f)p.push(e);try{return JSON.parse(s.decoder.decode((0,s.concat)(...p)))}catch{throw new a.JOSEError("Failed to parse the JSON Web Key Set HTTP response as JSON")}}},96376:e=>{function t(){try{var r=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(e.exports=t=function(){return!!r},e.exports.__esModule=!0,e.exports.default=e.exports)()}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},96559:(e,t,r)=>{"use strict";e.exports=r(44870)},97528:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.unwrap=t.wrap=void 0;let n=r(22122),o=r(85634),i=r(35749),a=r(68327);t.wrap=async function(e,t,r,o){let s=e.slice(0,7);o||(o=(0,i.default)(s));let{ciphertext:c,tag:l}=await (0,n.default)(s,r,t,o,new Uint8Array(0));return{encryptedKey:c,iv:(0,a.encode)(o),tag:(0,a.encode)(l)}},t.unwrap=async function(e,t,r,n,i){let a=e.slice(0,7);return(0,o.default)(a,t,r,n,i,new Uint8Array(0))}},97843:(e,t,r)=>{let n=r(28354),o=r(55511);e.exports=n.types.isKeyObject||(e=>e&&e instanceof o.KeyObject)},97920:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.importJWK=t.importPKCS8=t.importX509=t.importSPKI=void 0;let n=r(68327),o=r(93346),i=r(53687),a=r(99938),s=r(25110);t.importSPKI=async function(e,t,r){if("string"!=typeof e||0!==e.indexOf("-----BEGIN PUBLIC KEY-----"))throw TypeError('"spki" must be SPKI formatted string');return(0,o.fromSPKI)(e,t,r)},t.importX509=async function(e,t,r){if("string"!=typeof e||0!==e.indexOf("-----BEGIN CERTIFICATE-----"))throw TypeError('"x509" must be X.509 formatted string');return(0,o.fromX509)(e,t,r)},t.importPKCS8=async function(e,t,r){if("string"!=typeof e||0!==e.indexOf("-----BEGIN PRIVATE KEY-----"))throw TypeError('"pkcs8" must be PKCS#8 formatted string');return(0,o.fromPKCS8)(e,t,r)},t.importJWK=async function(e,t,r){var o;if(!(0,s.default)(e))throw TypeError("JWK must be an object");switch(t||(t=e.alg),e.kty){case"oct":if("string"!=typeof e.k||!e.k)throw TypeError('missing "k" (Key Value) Parameter value');if(null!=r||(r=!0!==e.ext),r)return(0,i.default)({...e,alg:t,ext:null!=(o=e.ext)&&o});return(0,n.decode)(e.k);case"RSA":if(void 0!==e.oth)throw new a.JOSENotSupported('RSA JWK "oth" (Other Primes Info) Parameter value is not supported');case"EC":case"OKP":return(0,i.default)({...e,alg:t});default:throw new a.JOSENotSupported('Unsupported "kty" (Key Type) Parameter value')}}},98905:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.jwtVerify=void 0;let n=r(29132),o=r(81822),i=r(99938);t.jwtVerify=async function(e,t,r){var a;let s=await (0,n.compactVerify)(e,t,r);if((null==(a=s.protectedHeader.crit)?void 0:a.includes("b64"))&&!1===s.protectedHeader.b64)throw new i.JWTInvalid("JWTs MUST NOT use unencoded payload");let c={payload:(0,o.default)(s.protectedHeader,s.payload,r),protectedHeader:s.protectedHeader};return"function"==typeof t?{...c,key:s.key}:c}},99296:(e,t,r)=>{"use strict";var n=r(9168);Object.defineProperty(t,"__esModule",{value:!0}),t.default=c;var o=n(r(53873)),i=n(r(59496)),a=r(29837),s=n(r(11714));async function c(e){var t,r,n,c,l,u;let{options:d,query:f,body:p,method:h,headers:y,sessionStore:_}=e,{provider:g,adapter:b,url:m,callbackUrl:v,pages:w,jwt:x,events:k,callbacks:E,session:{strategy:S,maxAge:A},logger:O}=d,P=[],T="jwt"===S;if("oauth"===g.type)try{let{profile:n,account:a,OAuthProfile:s,cookies:c}=await (0,o.default)({query:f,body:p,method:h,options:d,cookies:e.cookies});c.length&&P.push(...c);try{if(O.debug("OAUTH_CALLBACK_RESPONSE",{profile:n,account:a,OAuthProfile:s}),!n||!a||!s)return{redirect:`${m}/signin`,cookies:P};let e=n;if(b){let{getUserByAccount:t}=b,r=await t({providerAccountId:a.providerAccountId,provider:g.id});r&&(e=r)}try{let t=await E.signIn({user:e,account:a,profile:s});if(!t)return{redirect:`${m}/error?error=AccessDenied`,cookies:P};if("string"==typeof t)return{redirect:t,cookies:P}}catch(e){return{redirect:`${m}/error?error=${encodeURIComponent(e.message)}`,cookies:P}}let{user:o,session:c,isNewUser:l}=await (0,i.default)({sessionToken:_.value,profile:n,account:a,options:d});if(T){let e={name:o.name,email:o.email,picture:o.image,sub:null==(r=o.id)?void 0:r.toString()},t=await E.jwt({token:e,user:o,account:a,profile:s,isNewUser:l,trigger:l?"signUp":"signIn"}),n=await x.encode({...x,token:t}),i=new Date;i.setTime(i.getTime()+1e3*A);let c=_.chunk(n,{expires:i});P.push(...c)}else P.push({name:d.cookies.sessionToken.name,value:c.sessionToken,options:{...d.cookies.sessionToken.options,expires:c.expires}});if(await (null==(t=k.signIn)?void 0:t.call(k,{user:o,account:a,profile:n,isNewUser:l})),l&&w.newUser)return{redirect:`${w.newUser}${w.newUser.includes("?")?"&":"?"}callbackUrl=${encodeURIComponent(v)}`,cookies:P};return{redirect:v,cookies:P}}catch(e){if("AccountNotLinkedError"===e.name)return{redirect:`${m}/error?error=OAuthAccountNotLinked`,cookies:P};if("CreateUserError"===e.name)return{redirect:`${m}/error?error=OAuthCreateAccount`,cookies:P};return O.error("OAUTH_CALLBACK_HANDLER_ERROR",e),{redirect:`${m}/error?error=Callback`,cookies:P}}}catch(e){if("OAuthCallbackError"===e.name)return O.error("OAUTH_CALLBACK_ERROR",{error:e,providerId:g.id}),{redirect:`${m}/error?error=OAuthCallback`,cookies:P};return O.error("OAUTH_CALLBACK_ERROR",e),{redirect:`${m}/error?error=Callback`,cookies:P}}if("email"===g.type)try{let e=null==f?void 0:f.token,t=null==f?void 0:f.email;if(!e)return{redirect:`${m}/error?error=configuration`,cookies:P};let r=await b.useVerificationToken({identifier:t,token:(0,a.hashToken)(e,d)});if(!r||r.expires.valueOf()<Date.now()||t&&r.identifier!==t)return{redirect:`${m}/error?error=Verification`,cookies:P};let o=await (0,s.default)({email:r.identifier,adapter:b}),l={providerAccountId:o.email,type:"email",provider:g.id};try{let e=await E.signIn({user:o,account:l});if(!e)return{redirect:`${m}/error?error=AccessDenied`,cookies:P};if("string"==typeof e)return{redirect:e,cookies:P}}catch(e){return{redirect:`${m}/error?error=${encodeURIComponent(e.message)}`,cookies:P}}let{user:u,session:p,isNewUser:h}=await (0,i.default)({sessionToken:_.value,profile:o,account:l,options:d});if(T){let e={name:u.name,email:u.email,picture:u.image,sub:null==(c=u.id)?void 0:c.toString()},t=await E.jwt({token:e,user:u,account:l,isNewUser:h,trigger:h?"signUp":"signIn"}),r=await x.encode({...x,token:t}),n=new Date;n.setTime(n.getTime()+1e3*A);let o=_.chunk(r,{expires:n});P.push(...o)}else P.push({name:d.cookies.sessionToken.name,value:p.sessionToken,options:{...d.cookies.sessionToken.options,expires:p.expires}});if(await (null==(n=k.signIn)?void 0:n.call(k,{user:u,account:l,isNewUser:h})),h&&w.newUser)return{redirect:`${w.newUser}${w.newUser.includes("?")?"&":"?"}callbackUrl=${encodeURIComponent(v)}`,cookies:P};return{redirect:v,cookies:P}}catch(e){if("CreateUserError"===e.name)return{redirect:`${m}/error?error=EmailCreateAccount`,cookies:P};return O.error("CALLBACK_EMAIL_ERROR",e),{redirect:`${m}/error?error=Callback`,cookies:P}}if("credentials"===g.type&&"POST"===h){let e;try{if(!(e=await g.authorize(p,{query:f,body:p,headers:y,method:h})))return{status:401,redirect:`${m}/error?${new URLSearchParams({error:"CredentialsSignin",provider:g.id})}`,cookies:P}}catch(e){return{status:401,redirect:`${m}/error?error=${encodeURIComponent(e.message)}`,cookies:P}}let t={providerAccountId:e.id,type:"credentials",provider:g.id};try{let r=await E.signIn({user:e,account:t,credentials:p});if(!r)return{status:403,redirect:`${m}/error?error=AccessDenied`,cookies:P};if("string"==typeof r)return{redirect:r,cookies:P}}catch(e){return{redirect:`${m}/error?error=${encodeURIComponent(e.message)}`,cookies:P}}let r={name:e.name,email:e.email,picture:e.image,sub:null==(l=e.id)?void 0:l.toString()},n=await E.jwt({token:r,user:e,account:t,isNewUser:!1,trigger:"signIn"}),o=await x.encode({...x,token:n}),i=new Date;i.setTime(i.getTime()+1e3*A);let a=_.chunk(o,{expires:i});return P.push(...a),await (null==(u=k.signIn)?void 0:u.call(k,{user:e,account:t})),{redirect:v,cookies:P}}return{status:500,body:`Error: Callback for provider type ${g.type} not supported`,cookies:P}}},99312:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CompactSign=void 0;let n=r(59310);class o{constructor(e){this._flattened=new n.FlattenedSign(e)}setProtectedHeader(e){return this._flattened.setProtectedHeader(e),this}async sign(e,t){let r=await this._flattened.sign(e,t);if(void 0===r.payload)throw TypeError("use the flattened module for creating JWS with b64: false");return`${r.protected}.${r.payload}.${r.signature}`}}t.CompactSign=o},99607:(e,t,r)=>{var n=r(66446),o=r(28339),i=r(89831),a=r(86305),s=r(74113),c=r(67135),l=r(49305);function u(){"use strict";var t=o(),r=t.m(u),d=(Object.getPrototypeOf?Object.getPrototypeOf(r):r.__proto__).constructor;function f(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===d||"GeneratorFunction"===(t.displayName||t.name))}var p={throw:1,return:2,break:3,continue:3};function h(e){var t,r;return function(n){t||(t={stop:function(){return r(n.a,2)},catch:function(){return n.v},abrupt:function(e,t){return r(n.a,p[e],t)},delegateYield:function(e,o,i){return t.resultName=o,r(n.d,l(e),i)},finish:function(e){return r(n.f,e)}},r=function(e,r,o){n.p=t.prev,n.n=t.next;try{return e(r,o)}finally{t.next=n.n}}),t.resultName&&(t[t.resultName]=n.v,t.resultName=void 0),t.sent=n.v,t.next=n.n;try{return e.call(this,t)}finally{n.p=t.prev,n.n=t.next}}}return(e.exports=u=function(){return{wrap:function(e,r,n,o){return t.w(h(e),r,n,o&&o.reverse())},isGeneratorFunction:f,mark:t.m,awrap:function(e,t){return new n(e,t)},AsyncIterator:s,async:function(e,t,r,n,o){return(f(t)?a:i)(h(e),t,r,n,o)},keys:c,values:l}},e.exports.__esModule=!0,e.exports.default=e.exports)()}e.exports=u,e.exports.__esModule=!0,e.exports.default=e.exports},99632:(e,t)=>{"use strict";function r(e,t,r){n(e,t),t.set(e,r)}function n(e,t){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object")}function o(e,t){return e.get(a(e,t))}function i(e,t,r){return e.set(a(e,t),r),r}function a(e,t,r){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:r;throw TypeError("Private element is not present on this object")}Object.defineProperty(t,"__esModule",{value:!0}),t.SessionStore=void 0,t.defaultCookies=function(e){let t=e?"__Secure-":"";return{sessionToken:{name:`${t}next-auth.session-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},callbackUrl:{name:`${t}next-auth.callback-url`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},csrfToken:{name:`${e?"__Host-":""}next-auth.csrf-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},pkceCodeVerifier:{name:`${t}next-auth.pkce.code_verifier`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},state:{name:`${t}next-auth.state`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},nonce:{name:`${t}next-auth.nonce`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}}}};var s=new WeakMap,c=new WeakMap,l=new WeakMap,u=new WeakSet;class d{constructor(e,t,a){!function(e,t){n(e,t),t.add(e)}(this,u),r(this,s,{}),r(this,c,void 0),r(this,l,void 0),i(l,this,a),i(c,this,e);let{cookies:d}=t,{name:f}=e;if("function"==typeof(null==d?void 0:d.getAll))for(let{name:e,value:t}of d.getAll())e.startsWith(f)&&(o(s,this)[e]=t);else if(d instanceof Map)for(let e of d.keys())e.startsWith(f)&&(o(s,this)[e]=d.get(e));else for(let e in d)e.startsWith(f)&&(o(s,this)[e]=d[e])}get value(){return Object.keys(o(s,this)).sort((e,t)=>{var r,n;return parseInt(null!=(r=e.split(".").pop())?r:"0")-parseInt(null!=(n=t.split(".").pop())?n:"0")}).map(e=>o(s,this)[e]).join("")}chunk(e,t){let r=a(u,this,p).call(this);for(let n of a(u,this,f).call(this,{name:o(c,this).name,value:e,options:{...o(c,this).options,...t}}))r[n.name]=n;return Object.values(r)}clean(){return Object.values(a(u,this,p).call(this))}}function f(e){let t=Math.ceil(e.value.length/3933);if(1===t)return o(s,this)[e.name]=e.value,[e];let r=[];for(let n=0;n<t;n++){let t=`${e.name}.${n}`,i=e.value.substr(3933*n,3933);r.push({...e,name:t,value:i}),o(s,this)[t]=i}return o(l,this).debug("CHUNKING_SESSION_COOKIE",{message:"Session cookie exceeds allowed 4096 bytes.",emptyCookieSize:163,valueSize:e.value.length,chunks:r.map(e=>e.value.length+163)}),r}function p(){let e={};for(let r in o(s,this)){var t;null==(t=o(s,this))||delete t[r],e[r]={name:r,value:"",options:{...o(c,this).options,maxAge:0}}}return e}t.SessionStore=d},99933:(e,t,r)=>{"use strict";Object.defineProperty(t,"B",{value:!0}),Object.defineProperty(t,"U",{enumerable:!0,get:function(){return f}});let n=r(94069),o=r(23158),i=r(29294),a=r(63033),s=r(84971),c=r(80023),l=r(68388),u=r(76926),d=(r(44523),r(8719));function f(){let e="cookies",t=i.workAsyncStorage.getStore(),r=a.workUnitAsyncStorage.getStore();if(t){if(r&&"after"===r.phase&&!(0,d.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "cookies" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E88",enumerable:!1,configurable:!0});if(t.forceStatic)return h(n.RequestCookiesAdapter.seal(new o.RequestCookies(new Headers({}))));if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E398",enumerable:!1,configurable:!0});else if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E157",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new c.StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`cookies\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E549",enumerable:!1,configurable:!0});if(r)if("prerender"===r.type){var u=t.route,f=r;let e=p.get(f);if(e)return e;let n=(0,l.makeHangingPromise)(f.renderSignal,"`cookies()`");return p.set(f,n),Object.defineProperties(n,{[Symbol.iterator]:{value:function(){let e="`cookies()[Symbol.iterator]()`",t=g(u,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,f)}},size:{get(){let e="`cookies().size`",t=g(u,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,f)}},get:{value:function(){let e;e=0==arguments.length?"`cookies().get()`":`\`cookies().get(${y(arguments[0])})\``;let t=g(u,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,f)}},getAll:{value:function(){let e;e=0==arguments.length?"`cookies().getAll()`":`\`cookies().getAll(${y(arguments[0])})\``;let t=g(u,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,f)}},has:{value:function(){let e;e=0==arguments.length?"`cookies().has()`":`\`cookies().has(${y(arguments[0])})\``;let t=g(u,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,f)}},set:{value:function(){let e;if(0==arguments.length)e="`cookies().set()`";else{let t=arguments[0];e=t?`\`cookies().set(${y(t)}, ...)\``:"`cookies().set(...)`"}let t=g(u,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,f)}},delete:{value:function(){let e;e=0==arguments.length?"`cookies().delete()`":1==arguments.length?`\`cookies().delete(${y(arguments[0])})\``:`\`cookies().delete(${y(arguments[0])}, ...)\``;let t=g(u,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,f)}},clear:{value:function(){let e="`cookies().clear()`",t=g(u,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,f)}},toString:{value:function(){let e="`cookies().toString()`",t=g(u,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,f)}}}),n}else"prerender-ppr"===r.type?(0,s.postponeWithTracking)(t.route,e,r.dynamicTracking):"prerender-legacy"===r.type&&(0,s.throwToInterruptStaticGeneration)(e,t,r);(0,s.trackDynamicDataInDynamicRender)(t,r)}let _=(0,a.getExpectedRequestStore)(e);return h((0,n.areCookiesMutableInCurrentPhase)(_)?_.userspaceMutableCookies:_.cookies)}let p=new WeakMap;function h(e){let t=p.get(e);if(t)return t;let r=Promise.resolve(e);return p.set(e,r),Object.defineProperties(r,{[Symbol.iterator]:{value:e[Symbol.iterator]?e[Symbol.iterator].bind(e):b.bind(e)},size:{get:()=>e.size},get:{value:e.get.bind(e)},getAll:{value:e.getAll.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},delete:{value:e.delete.bind(e)},clear:{value:"function"==typeof e.clear?e.clear.bind(e):m.bind(e,r)},toString:{value:e.toString.bind(e)}}),r}function y(e){return"object"==typeof e&&null!==e&&"string"==typeof e.name?`'${e.name}'`:"string"==typeof e?`'${e}'`:"..."}let _=(0,u.createDedupedByCallsiteServerErrorLoggerDev)(g);function g(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E223",enumerable:!1,configurable:!0})}function b(){return this.getAll().map(e=>[e.name,e]).values()}function m(e){for(let e of this.getAll())this.delete(e.name);return e}},99938:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.JWSSignatureVerificationFailed=t.JWKSTimeout=t.JWKSMultipleMatchingKeys=t.JWKSNoMatchingKey=t.JWKSInvalid=t.JWKInvalid=t.JWTInvalid=t.JWSInvalid=t.JWEInvalid=t.JWEDecompressionFailed=t.JWEDecryptionFailed=t.JOSENotSupported=t.JOSEAlgNotAllowed=t.JWTExpired=t.JWTClaimValidationFailed=t.JOSEError=void 0;class r extends Error{static get code(){return"ERR_JOSE_GENERIC"}constructor(e){var t;super(e),this.code="ERR_JOSE_GENERIC",this.name=this.constructor.name,null==(t=Error.captureStackTrace)||t.call(Error,this,this.constructor)}}t.JOSEError=r;class n extends r{static get code(){return"ERR_JWT_CLAIM_VALIDATION_FAILED"}constructor(e,t="unspecified",r="unspecified"){super(e),this.code="ERR_JWT_CLAIM_VALIDATION_FAILED",this.claim=t,this.reason=r}}t.JWTClaimValidationFailed=n;class o extends r{static get code(){return"ERR_JWT_EXPIRED"}constructor(e,t="unspecified",r="unspecified"){super(e),this.code="ERR_JWT_EXPIRED",this.claim=t,this.reason=r}}t.JWTExpired=o;class i extends r{constructor(){super(...arguments),this.code="ERR_JOSE_ALG_NOT_ALLOWED"}static get code(){return"ERR_JOSE_ALG_NOT_ALLOWED"}}t.JOSEAlgNotAllowed=i;class a extends r{constructor(){super(...arguments),this.code="ERR_JOSE_NOT_SUPPORTED"}static get code(){return"ERR_JOSE_NOT_SUPPORTED"}}t.JOSENotSupported=a;class s extends r{constructor(){super(...arguments),this.code="ERR_JWE_DECRYPTION_FAILED",this.message="decryption operation failed"}static get code(){return"ERR_JWE_DECRYPTION_FAILED"}}t.JWEDecryptionFailed=s;class c extends r{constructor(){super(...arguments),this.code="ERR_JWE_DECOMPRESSION_FAILED",this.message="decompression operation failed"}static get code(){return"ERR_JWE_DECOMPRESSION_FAILED"}}t.JWEDecompressionFailed=c;class l extends r{constructor(){super(...arguments),this.code="ERR_JWE_INVALID"}static get code(){return"ERR_JWE_INVALID"}}t.JWEInvalid=l;class u extends r{constructor(){super(...arguments),this.code="ERR_JWS_INVALID"}static get code(){return"ERR_JWS_INVALID"}}t.JWSInvalid=u;class d extends r{constructor(){super(...arguments),this.code="ERR_JWT_INVALID"}static get code(){return"ERR_JWT_INVALID"}}t.JWTInvalid=d;class f extends r{constructor(){super(...arguments),this.code="ERR_JWK_INVALID"}static get code(){return"ERR_JWK_INVALID"}}t.JWKInvalid=f;class p extends r{constructor(){super(...arguments),this.code="ERR_JWKS_INVALID"}static get code(){return"ERR_JWKS_INVALID"}}t.JWKSInvalid=p;class h extends r{constructor(){super(...arguments),this.code="ERR_JWKS_NO_MATCHING_KEY",this.message="no applicable key found in the JSON Web Key Set"}static get code(){return"ERR_JWKS_NO_MATCHING_KEY"}}t.JWKSNoMatchingKey=h;class y extends r{constructor(){super(...arguments),this.code="ERR_JWKS_MULTIPLE_MATCHING_KEYS",this.message="multiple matching keys found in the JSON Web Key Set"}static get code(){return"ERR_JWKS_MULTIPLE_MATCHING_KEYS"}}t.JWKSMultipleMatchingKeys=y,Symbol.asyncIterator;class _ extends r{constructor(){super(...arguments),this.code="ERR_JWKS_TIMEOUT",this.message="request timed out"}static get code(){return"ERR_JWKS_TIMEOUT"}}t.JWKSTimeout=_;class g extends r{constructor(){super(...arguments),this.code="ERR_JWS_SIGNATURE_VERIFICATION_FAILED",this.message="signature verification failed"}static get code(){return"ERR_JWS_SIGNATURE_VERIFICATION_FAILED"}}t.JWSSignatureVerificationFailed=g}};