{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/dashboard(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\.json)?[\\/#\\?]?$", "originalSource": "/dashboard/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\.json)?[\\/#\\?]?$", "originalSource": "/api/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "6zE8LwofqrRMBqPBqkoCG", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "GmR490q78K+bBIm3nmrLlRQO8rHHbnjcdhaBgWmTT64=", "__NEXT_PREVIEW_MODE_ID": "ff78d7d0b9eaf9eb8d9557d080de6bc4", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "87cc7e8443d1734d974efd5e32ef33815c0fdc99f8174522668eefe3653a6192", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d97f1576d1eb819401a3ba4320bc2cfdbbd89c542709176257c49ecfb149b1a6"}}}, "functions": {}, "sortedMiddleware": ["/"]}