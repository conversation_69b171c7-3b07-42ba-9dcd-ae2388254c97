(()=>{var e={};e.id=252,e.ids=[252],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,s)=>{"use strict";s.d(r,{N:()=>n});var t=s(13581),i=s(16467),a=s(94747),o=s(85663);let n={adapter:(0,i.y)(a.z),providers:[(0,t.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let r=await a.z.user.findUnique({where:{email:e.email}});return r&&await o.Ay.compare(e.password,r.password)?{id:r.id,email:r.email,name:r.name,role:r.role}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(e.role=r.role),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role),e)},pages:{signIn:"/auth/signin"},secret:process.env.NEXTAUTH_SECRET}},14606:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>f,routeModule:()=>v,serverHooks:()=>h,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>m});var t={};s.r(t),s.d(t,{DELETE:()=>x,GET:()=>l,PUT:()=>d});var i=s(96559),a=s(48088),o=s(37719),n=s(32190),u=s(19854),c=s(12909),p=s(94747);async function l(e,{params:r}){try{let{id:e}=await r,s=await p.z.service.findUnique({where:{id:e}});if(!s)return n.NextResponse.json({error:"Service not found"},{status:404});return n.NextResponse.json(s)}catch(e){return console.error("Error fetching service:",e),n.NextResponse.json({error:"Failed to fetch service"},{status:500})}}async function d(e,{params:r}){try{let s=await (0,u.getServerSession)(c.N);if(!s?.user)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{id:t}=await r,{title:i,description:a,features:o,icon:l,color:d,bgColor:x,order:v,published:w}=await e.json(),m=await p.z.service.update({where:{id:t},data:{title:i,description:a,features:o,icon:l,color:d,bgColor:x,order:v,published:w}});return n.NextResponse.json(m)}catch(e){return console.error("Error updating service:",e),n.NextResponse.json({error:"Failed to update service"},{status:500})}}async function x(e,{params:r}){try{let e=await (0,u.getServerSession)(c.N);if(!e?.user)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{id:s}=await r;return await p.z.service.delete({where:{id:s}}),n.NextResponse.json({message:"Service deleted successfully"})}catch(e){return console.error("Error deleting service:",e),n.NextResponse.json({error:"Failed to delete service"},{status:500})}}let v=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/services/[id]/route",pathname:"/api/services/[id]",filename:"route",bundlePath:"app/api/services/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\api\\services\\[id]\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:w,workUnitAsyncStorage:m,serverHooks:h}=v;function f(){return(0,o.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:m})}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},94747:(e,r,s)=>{"use strict";s.d(r,{z:()=>i});let t=require("@prisma/client"),i=globalThis.prisma??new t.PrismaClient},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[243,542,190],()=>s(14606));module.exports=t})();