(()=>{var e={};e.id=640,e.ids=[640],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{N:()=>n});var s=r(13581),i=r(16467),a=r(94747),o=r(85663);let n={adapter:(0,i.y)(a.z),providers:[(0,s.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let t=await a.z.user.findUnique({where:{email:e.email}});return t&&await o.Ay.compare(e.password,t.password)?{id:t.id,email:t.email,name:t.name,role:t.role}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(e.role=t.role),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role),e)},pages:{signIn:"/auth/signin"},secret:process.env.NEXTAUTH_SECRET}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71718:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>x,serverHooks:()=>f,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>h});var s={};r.r(s),r.d(s,{DELETE:()=>m,GET:()=>c,PUT:()=>d});var i=r(96559),a=r(48088),o=r(37719),n=r(32190),u=r(19854),l=r(12909),p=r(94747);async function c(e,{params:t}){try{let{id:e}=await t,r=await p.z.testimonial.findUnique({where:{id:e},include:{author:{select:{id:!0,name:!0,email:!0}}}});if(!r)return n.NextResponse.json({error:"Testimonial not found"},{status:404});return n.NextResponse.json(r)}catch(e){return console.error("Error fetching testimonial:",e),n.NextResponse.json({error:"Failed to fetch testimonial"},{status:500})}}async function d(e,{params:t}){try{let r=await (0,u.getServerSession)(l.N);if(!r?.user)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{id:s}=await t,{name:i,role:a,company:o,content:c,avatar:d,rating:m,featured:x,published:w,order:h}=await e.json(),f=await p.z.testimonial.update({where:{id:s},data:{name:i,role:a,company:o,content:c,avatar:d,rating:m,featured:x,published:w,order:h},include:{author:{select:{id:!0,name:!0,email:!0}}}});return n.NextResponse.json(f)}catch(e){return console.error("Error updating testimonial:",e),n.NextResponse.json({error:"Failed to update testimonial"},{status:500})}}async function m(e,{params:t}){try{let e=await (0,u.getServerSession)(l.N);if(!e?.user)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{id:r}=await t;return await p.z.testimonial.delete({where:{id:r}}),n.NextResponse.json({message:"Testimonial deleted successfully"})}catch(e){return console.error("Error deleting testimonial:",e),n.NextResponse.json({error:"Failed to delete testimonial"},{status:500})}}let x=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/testimonials/[id]/route",pathname:"/api/testimonials/[id]",filename:"route",bundlePath:"app/api/testimonials/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\api\\testimonials\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:w,workUnitAsyncStorage:h,serverHooks:f}=x;function y(){return(0,o.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:h})}},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},94747:(e,t,r)=>{"use strict";r.d(t,{z:()=>i});let s=require("@prisma/client"),i=globalThis.prisma??new s.PrismaClient},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[243,542,190],()=>r(71718));module.exports=s})();