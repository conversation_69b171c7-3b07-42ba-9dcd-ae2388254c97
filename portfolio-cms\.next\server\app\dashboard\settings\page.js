(()=>{var e={};e.id=631,e.ids=[631],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6888:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>o});var a=t(65239),r=t(48088),i=t(88170),n=t.n(i),l=t(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let o={children:["",{children:["dashboard",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,62623)),"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\settings\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\settings\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/dashboard/settings/page",pathname:"/dashboard/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16023:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35809:(e,s,t)=>{Promise.resolve().then(t.bind(t,62623))},48961:(e,s,t)=>{Promise.resolve().then(t.bind(t,55122))},55122:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>y});var a=t(60687),r=t(43210),i=t(82136),n=t(62280),l=t(29523),d=t(44493),o=t(89667),c=t(80013),u=t(96834),p=t(62688);let x=(0,p.A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);var m=t(16023),h=t(53411);let v=(0,p.A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]]),f=(0,p.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),g=(0,p.A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);var j=t(58869);let b=(0,p.A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);function y(){let{data:e}=(0,i.useSession)(),[s,t]=(0,r.useState)(!1),p=[{title:"Database Configuration",description:"PostgreSQL database connection",icon:x,status:"connected",items:[{label:"Database URL",value:"postgresql://***@neon.tech/***",masked:!0},{label:"Connection Pool",value:"Active",status:"success"},{label:"Last Migration",value:"2025-07-01 07:42:52",status:"success"}]},{title:"Cloudinary Integration",description:"Image upload and management service",icon:m.A,status:"configured",items:[{label:"Cloud Name",value:process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME||"Not configured"},{label:"Upload Preset",value:"portfolio",status:"success"},{label:"Storage Used",value:"0 MB / 25 GB",status:"success"}]},{title:"Google Analytics",description:"Website analytics and tracking",icon:h.A,status:"pending",items:[{label:"Property ID",value:"Not configured",status:"warning"},{label:"Tracking Status",value:"Inactive",status:"warning"},{label:"Data Collection",value:"Disabled",status:"warning"}]},{title:"Authentication",description:"User authentication and security",icon:v,status:"active",items:[{label:"NextAuth Secret",value:"Configured",status:"success"},{label:"Session Strategy",value:"JWT",status:"success"},{label:"Active Sessions",value:"1",status:"success"}]}],y=e=>{switch(e){case"success":case"connected":case"active":case"configured":return(0,a.jsx)(f,{className:"h-4 w-4 text-green-500"});case"warning":case"pending":return(0,a.jsx)(g,{className:"h-4 w-4 text-yellow-500"});default:return(0,a.jsx)(g,{className:"h-4 w-4 text-red-500"})}},N=e=>{switch(e){case"connected":case"active":case"configured":return(0,a.jsx)(u.E,{variant:"default",className:"bg-green-500",children:"Connected"});case"pending":return(0,a.jsx)(u.E,{variant:"secondary",children:"Pending Setup"});default:return(0,a.jsx)(u.E,{variant:"destructive",children:"Disconnected"})}};return(0,a.jsx)(n.N,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Settings"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Manage your CMS configuration and integrations"})]}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsxs)(d.aR,{children:[(0,a.jsxs)(d.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(j.A,{className:"h-5 w-5"}),"User Information"]}),(0,a.jsx)(d.BT,{children:"Your account details and preferences"})]}),(0,a.jsxs)(d.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(c.J,{children:"Name"}),(0,a.jsx)(o.p,{value:e?.user?.name||"Admin User",disabled:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c.J,{children:"Email"}),(0,a.jsx)(o.p,{value:e?.user?.email||"<EMAIL>",disabled:!0})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c.J,{children:"Role"}),(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)(u.E,{variant:"default",children:e?.user?.role||"ADMIN"})})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold tracking-tight",children:"System Configuration"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Overview of your CMS integrations and services"})]}),(0,a.jsx)("div",{className:"grid gap-6 md:grid-cols-2",children:p.map(e=>(0,a.jsxs)(d.Zp,{children:[(0,a.jsxs)(d.aR,{children:[(0,a.jsxs)(d.ZB,{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(e.icon,{className:"h-5 w-5"}),e.title]}),N(e.status)]}),(0,a.jsx)(d.BT,{children:e.description})]}),(0,a.jsx)(d.Wu,{children:(0,a.jsx)("div",{className:"space-y-3",children:e.items.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[e.status&&y(e.status),(0,a.jsx)("span",{className:"text-sm font-medium",children:e.label})]}),(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:e.masked?"••••••••••••":e.value})]},s))})})]},e.title))})]}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsxs)(d.aR,{children:[(0,a.jsxs)(d.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(b,{className:"h-5 w-5"}),"Environment Configuration"]}),(0,a.jsx)(d.BT,{children:"Required environment variables for CMS functionality"})]}),(0,a.jsx)(d.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{className:"grid gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:"DATABASE_URL"}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"PostgreSQL connection string"})]}),(0,a.jsx)(f,{className:"h-5 w-5 text-green-500"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:"NEXTAUTH_SECRET"}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Authentication secret key"})]}),(0,a.jsx)(f,{className:"h-5 w-5 text-green-500"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:"CLOUDINARY_*"}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Image upload configuration"})]}),(0,a.jsx)(g,{className:"h-5 w-5 text-yellow-500"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:"GOOGLE_ANALYTICS_*"}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Analytics integration"})]}),(0,a.jsx)(g,{className:"h-5 w-5 text-yellow-500"})]})]})})})]}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsxs)(d.aR,{children:[(0,a.jsx)(d.ZB,{children:"System Actions"}),(0,a.jsx)(d.BT,{children:"Maintenance and administrative actions"})]}),(0,a.jsx)(d.Wu,{children:(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)(l.$,{variant:"outline",disabled:s,children:"Backup Database"}),(0,a.jsx)(l.$,{variant:"outline",disabled:s,children:"Clear Cache"}),(0,a.jsx)(l.$,{variant:"outline",disabled:s,children:"Export Data"})]})})]})]})})}},62623:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\portfolio\\\\portfolio-cms\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\settings\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78148:(e,s,t)=>{"use strict";t.d(s,{b:()=>l});var a=t(43210),r=t(14163),i=t(60687),n=a.forwardRef((e,s)=>(0,i.jsx)(r.sG.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));n.displayName="Label";var l=n},79551:e=>{"use strict";e.exports=require("url")},80013:(e,s,t)=>{"use strict";t.d(s,{J:()=>n});var a=t(60687);t(43210);var r=t(78148),i=t(4780);function n({className:e,...s}){return(0,a.jsx)(r.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...s})}},89667:(e,s,t)=>{"use strict";t.d(s,{p:()=>i});var a=t(60687);t(43210);var r=t(4780);function i({className:e,type:s,...t}){return(0,a.jsx)("input",{type:s,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[243,310,934,671,360,726],()=>t(6888));module.exports=a})();