"use strict";exports.id=658,exports.ids=[658],exports.modules={15079:(e,t,s)=>{s.d(t,{bq:()=>u,eb:()=>h,gC:()=>p,l6:()=>o,yv:()=>c});var a=s(60687);s(43210);var r=s(97822),i=s(78272),l=s(13964),n=s(3589),d=s(4780);function o({...e}){return(0,a.jsx)(r.bL,{"data-slot":"select",...e})}function c({...e}){return(0,a.jsx)(r.WT,{"data-slot":"select-value",...e})}function u({className:e,size:t="default",children:s,...l}){return(0,a.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":t,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...l,children:[s,(0,a.jsx)(r.In,{asChild:!0,children:(0,a.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function p({className:e,children:t,position:s="popper",...i}){return(0,a.jsx)(r.ZL,{children:(0,a.jsxs)(r.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...i,children:[(0,a.jsx)(x,{}),(0,a.jsx)(r.LM,{className:(0,d.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,a.jsx)(g,{})]})})}function h({className:e,children:t,...s}){return(0,a.jsxs)(r.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...s,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(l.A,{className:"size-4"})})}),(0,a.jsx)(r.p4,{children:t})]})}function x({className:e,...t}){return(0,a.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(n.A,{className:"size-4"})})}function g({className:e,...t}){return(0,a.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(i.A,{className:"size-4"})})}},17658:(e,t,s)=>{s.d(t,{o:()=>v});var a=s(60687),r=s(43210),i=s(16189),l=s(29523),n=s(89667),d=s(80013),o=s(34729),c=s(56896),u=s(15079),p=s(44493),h=s(19312),x=s(96834),g=s(11860);let m=["Full Stack","Frontend","Backend","Mobile","AI/ML","DevOps","Design"];function v({projectId:e,onSuccess:t}){let s=(0,i.useRouter)(),[v,j]=(0,r.useState)(!1),[f,b]=(0,r.useState)(""),[y,w]=(0,r.useState)({title:"",description:"",longDescription:"",image:"",category:"",technologies:[],liveUrl:"",githubUrl:"",featured:!1,published:!0,order:0}),N=async a=>{a.preventDefault(),j(!0);try{let a=e?`/api/projects/${e}`:"/api/projects";if((await fetch(a,{method:e?"PUT":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(y)})).ok)t?.(),e||s.push("/dashboard/projects");else throw Error("Failed to save project")}catch(e){console.error("Error saving project:",e),alert("Failed to save project. Please try again.")}finally{j(!1)}},k=()=>{f.trim()&&!y.technologies.includes(f.trim())&&(w(e=>({...e,technologies:[...e.technologies,f.trim()]})),b(""))},C=e=>{w(t=>({...t,technologies:t.technologies.filter(t=>t!==e)}))};return(0,a.jsxs)("form",{onSubmit:N,className:"space-y-6",children:[(0,a.jsxs)(p.Zp,{children:[(0,a.jsxs)(p.aR,{children:[(0,a.jsx)(p.ZB,{children:"Basic Information"}),(0,a.jsx)(p.BT,{children:"Enter the basic details about your project"})]}),(0,a.jsxs)(p.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"title",children:"Project Title *"}),(0,a.jsx)(n.p,{id:"title",value:y.title,onChange:e=>w(t=>({...t,title:e.target.value})),required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"description",children:"Short Description *"}),(0,a.jsx)(o.T,{id:"description",value:y.description,onChange:e=>w(t=>({...t,description:e.target.value})),rows:3,required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"longDescription",children:"Detailed Description"}),(0,a.jsx)(o.T,{id:"longDescription",value:y.longDescription,onChange:e=>w(t=>({...t,longDescription:e.target.value})),rows:5})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"category",children:"Category *"}),(0,a.jsxs)(u.l6,{value:y.category,onValueChange:e=>w(t=>({...t,category:e})),children:[(0,a.jsx)(u.bq,{children:(0,a.jsx)(u.yv,{placeholder:"Select a category"})}),(0,a.jsx)(u.gC,{children:m.map(e=>(0,a.jsx)(u.eb,{value:e,children:e},e))})]})]})]})]}),(0,a.jsxs)(p.Zp,{children:[(0,a.jsxs)(p.aR,{children:[(0,a.jsx)(p.ZB,{children:"Project Image"}),(0,a.jsx)(p.BT,{children:"Upload a screenshot or preview image of your project"})]}),(0,a.jsx)(p.Wu,{children:(0,a.jsx)(h.B,{value:y.image,onChange:e=>w(t=>({...t,image:e})),onRemove:()=>w(e=>({...e,image:""})),disabled:v})})]}),(0,a.jsxs)(p.Zp,{children:[(0,a.jsxs)(p.aR,{children:[(0,a.jsx)(p.ZB,{children:"Technologies"}),(0,a.jsx)(p.BT,{children:"Add the technologies and tools used in this project"})]}),(0,a.jsxs)(p.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(n.p,{placeholder:"Enter technology (e.g., React, Node.js)",value:f,onChange:e=>b(e.target.value),onKeyPress:e=>{"Enter"===e.key&&(e.preventDefault(),k())}}),(0,a.jsx)(l.$,{type:"button",onClick:k,children:"Add"})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:y.technologies.map(e=>(0,a.jsxs)(x.E,{variant:"secondary",className:"flex items-center gap-1",children:[e,(0,a.jsx)(g.A,{className:"h-3 w-3 cursor-pointer",onClick:()=>C(e)})]},e))})]})]}),(0,a.jsxs)(p.Zp,{children:[(0,a.jsxs)(p.aR,{children:[(0,a.jsx)(p.ZB,{children:"Links"}),(0,a.jsx)(p.BT,{children:"Add links to the live project and source code"})]}),(0,a.jsxs)(p.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"liveUrl",children:"Live URL"}),(0,a.jsx)(n.p,{id:"liveUrl",type:"url",placeholder:"https://example.com",value:y.liveUrl,onChange:e=>w(t=>({...t,liveUrl:e.target.value}))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"githubUrl",children:"GitHub URL"}),(0,a.jsx)(n.p,{id:"githubUrl",type:"url",placeholder:"https://github.com/username/repo",value:y.githubUrl,onChange:e=>w(t=>({...t,githubUrl:e.target.value}))})]})]})]}),(0,a.jsxs)(p.Zp,{children:[(0,a.jsxs)(p.aR,{children:[(0,a.jsx)(p.ZB,{children:"Settings"}),(0,a.jsx)(p.BT,{children:"Configure project visibility and display options"})]}),(0,a.jsxs)(p.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(c.S,{id:"featured",checked:y.featured,onCheckedChange:e=>w(t=>({...t,featured:e}))}),(0,a.jsx)(d.J,{htmlFor:"featured",children:"Featured Project"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(c.S,{id:"published",checked:y.published,onCheckedChange:e=>w(t=>({...t,published:e}))}),(0,a.jsx)(d.J,{htmlFor:"published",children:"Published"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"order",children:"Display Order"}),(0,a.jsx)(n.p,{id:"order",type:"number",min:"0",value:y.order,onChange:e=>w(t=>({...t,order:parseInt(e.target.value)||0}))})]})]})]}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)(l.$,{type:"submit",disabled:v,children:v?"Saving...":e?"Update Project":"Create Project"}),(0,a.jsx)(l.$,{type:"button",variant:"outline",onClick:()=>s.back(),children:"Cancel"})]})]})}},19312:(e,t,s)=>{s.d(t,{B:()=>p});var a=s(60687),r=s(43210),i=s(29523),l=s(89667),n=s(80013),d=s(11860),o=s(9005),c=s(16023),u=s(30474);function p({value:e,onChange:t,onRemove:s,disabled:p,label:h="Upload Image",className:x=""}){let[g,m]=(0,r.useState)(!1),v=(0,r.useRef)(null),j=async e=>{let s=e.target.files?.[0];if(s){m(!0);try{let e=new FormData;e.append("file",s);let a=await fetch("/api/upload",{method:"POST",body:e});if(!a.ok)throw Error("Upload failed");let r=await a.json();t(r.url)}catch(e){console.error("Error uploading image:",e),alert("Failed to upload image. Please try again.")}finally{m(!1)}}};return(0,a.jsxs)("div",{className:`space-y-2 ${x}`,children:[(0,a.jsx)(n.J,{children:h}),e?(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"relative w-full h-48 rounded-lg overflow-hidden border",children:(0,a.jsx)(u.default,{src:e,alt:"Uploaded image",fill:!0,className:"object-cover"})}),(0,a.jsx)(i.$,{type:"button",variant:"destructive",size:"sm",className:"absolute top-2 right-2",onClick:s,disabled:p,children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})]}):(0,a.jsx)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(o.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsxs)(i.$,{type:"button",variant:"outline",onClick:()=>{v.current?.click()},disabled:p||g,children:[(0,a.jsx)(c.A,{className:"mr-2 h-4 w-4"}),g?"Uploading...":"Choose Image"]})}),(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"PNG, JPG, GIF up to 10MB"})]})}),(0,a.jsx)(l.p,{ref:v,type:"file",accept:"image/*",onChange:j,className:"hidden",disabled:p||g})]})}},34729:(e,t,s)=>{s.d(t,{T:()=>i});var a=s(60687);s(43210);var r=s(4780);function i({className:e,...t}){return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t})}},56896:(e,t,s)=>{s.d(t,{S:()=>n});var a=s(60687);s(43210);var r=s(40211),i=s(13964),l=s(4780);function n({className:e,...t}){return(0,a.jsx)(r.bL,{"data-slot":"checkbox",className:(0,l.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,a.jsx)(r.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,a.jsx)(i.A,{className:"size-3.5"})})})}},80013:(e,t,s)=>{s.d(t,{J:()=>l});var a=s(60687);s(43210);var r=s(78148),i=s(4780);function l({className:e,...t}){return(0,a.jsx)(r.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},89667:(e,t,s)=>{s.d(t,{p:()=>i});var a=s(60687);s(43210);var r=s(4780);function i({className:e,type:t,...s}){return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...s})}}};