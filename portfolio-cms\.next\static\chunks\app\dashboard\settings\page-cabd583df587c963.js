(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4631],{685:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>N});var t=a(5155),i=a(2115),n=a(2108),l=a(3930),r=a(285),c=a(6695),d=a(2523),o=a(5057),u=a(6126),x=a(9946);let m=(0,x.A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);var h=a(9869),v=a(2713);let p=(0,x.A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]]),g=(0,x.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),j=(0,x.A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);var f=a(1007);let b=(0,x.A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);var y=a(9509);function N(){var e,s,a;let{data:x}=(0,n.useSession)(),[N,w]=(0,i.useState)(!1),k=[{title:"Database Configuration",description:"PostgreSQL database connection",icon:m,status:"connected",items:[{label:"Database URL",value:"postgresql://***@neon.tech/***",masked:!0},{label:"Connection Pool",value:"Active",status:"success"},{label:"Last Migration",value:"2025-07-01 07:42:52",status:"success"}]},{title:"Cloudinary Integration",description:"Image upload and management service",icon:h.A,status:"configured",items:[{label:"Cloud Name",value:y.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME||"Not configured"},{label:"Upload Preset",value:"portfolio",status:"success"},{label:"Storage Used",value:"0 MB / 25 GB",status:"success"}]},{title:"Google Analytics",description:"Website analytics and tracking",icon:v.A,status:"pending",items:[{label:"Property ID",value:"Not configured",status:"warning"},{label:"Tracking Status",value:"Inactive",status:"warning"},{label:"Data Collection",value:"Disabled",status:"warning"}]},{title:"Authentication",description:"User authentication and security",icon:p,status:"active",items:[{label:"NextAuth Secret",value:"Configured",status:"success"},{label:"Session Strategy",value:"JWT",status:"success"},{label:"Active Sessions",value:"1",status:"success"}]}],A=e=>{switch(e){case"success":case"connected":case"active":case"configured":return(0,t.jsx)(g,{className:"h-4 w-4 text-green-500"});case"warning":case"pending":return(0,t.jsx)(j,{className:"h-4 w-4 text-yellow-500"});default:return(0,t.jsx)(j,{className:"h-4 w-4 text-red-500"})}},C=e=>{switch(e){case"connected":case"active":case"configured":return(0,t.jsx)(u.E,{variant:"default",className:"bg-green-500",children:"Connected"});case"pending":return(0,t.jsx)(u.E,{variant:"secondary",children:"Pending Setup"});default:return(0,t.jsx)(u.E,{variant:"destructive",children:"Disconnected"})}};return(0,t.jsx)(l.N,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Settings"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Manage your CMS configuration and integrations"})]}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(f.A,{className:"h-5 w-5"}),"User Information"]}),(0,t.jsx)(c.BT,{children:"Your account details and preferences"})]}),(0,t.jsxs)(c.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(o.J,{children:"Name"}),(0,t.jsx)(d.p,{value:(null==x||null==(e=x.user)?void 0:e.name)||"Admin User",disabled:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(o.J,{children:"Email"}),(0,t.jsx)(d.p,{value:(null==x||null==(s=x.user)?void 0:s.email)||"<EMAIL>",disabled:!0})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(o.J,{children:"Role"}),(0,t.jsx)("div",{className:"mt-1",children:(0,t.jsx)(u.E,{variant:"default",children:(null==x||null==(a=x.user)?void 0:a.role)||"ADMIN"})})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold tracking-tight",children:"System Configuration"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Overview of your CMS integrations and services"})]}),(0,t.jsx)("div",{className:"grid gap-6 md:grid-cols-2",children:k.map(e=>(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsxs)(c.ZB,{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(e.icon,{className:"h-5 w-5"}),e.title]}),C(e.status)]}),(0,t.jsx)(c.BT,{children:e.description})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsx)("div",{className:"space-y-3",children:e.items.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[e.status&&A(e.status),(0,t.jsx)("span",{className:"text-sm font-medium",children:e.label})]}),(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"masked"in e&&e.masked?"••••••••••••":e.value})]},s))})})]},e.title))})]}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(b,{className:"h-5 w-5"}),"Environment Configuration"]}),(0,t.jsx)(c.BT,{children:"Required environment variables for CMS functionality"})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid gap-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:"DATABASE_URL"}),(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:"PostgreSQL connection string"})]}),(0,t.jsx)(g,{className:"h-5 w-5 text-green-500"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:"NEXTAUTH_SECRET"}),(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:"Authentication secret key"})]}),(0,t.jsx)(g,{className:"h-5 w-5 text-green-500"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:"CLOUDINARY_*"}),(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:"Image upload configuration"})]}),(0,t.jsx)(j,{className:"h-5 w-5 text-yellow-500"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:"GOOGLE_ANALYTICS_*"}),(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:"Analytics integration"})]}),(0,t.jsx)(j,{className:"h-5 w-5 text-yellow-500"})]})]})})})]}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{children:"System Actions"}),(0,t.jsx)(c.BT,{children:"Maintenance and administrative actions"})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsx)(r.$,{variant:"outline",disabled:N,children:"Backup Database"}),(0,t.jsx)(r.$,{variant:"outline",disabled:N,children:"Clear Cache"}),(0,t.jsx)(r.$,{variant:"outline",disabled:N,children:"Export Data"})]})})]})]})})}},968:(e,s,a)=>{"use strict";a.d(s,{b:()=>r});var t=a(2115),i=a(3655),n=a(5155),l=t.forwardRef((e,s)=>(0,n.jsx)(i.sG.label,{...e,ref:s,onMouseDown:s=>{var a;s.target.closest("button, input, select, textarea")||(null==(a=e.onMouseDown)||a.call(e,s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));l.displayName="Label";var r=l},2523:(e,s,a)=>{"use strict";a.d(s,{p:()=>n});var t=a(5155);a(2115);var i=a(9434);function n(e){let{className:s,type:a,...n}=e;return(0,t.jsx)("input",{type:a,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...n})}},3007:(e,s,a)=>{Promise.resolve().then(a.bind(a,685))},5057:(e,s,a)=>{"use strict";a.d(s,{J:()=>l});var t=a(5155);a(2115);var i=a(968),n=a(9434);function l(e){let{className:s,...a}=e;return(0,t.jsx)(i.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...a})}},9869:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[2108,6321,7658,6985,8441,1684,7358],()=>s(3007)),_N_E=e.O()}]);