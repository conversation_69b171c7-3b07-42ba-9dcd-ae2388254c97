(()=>{var e={};e.id=514,e.ids=[514],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{N:()=>n});var s=t(13581),o=t(16467),i=t(94747),a=t(85663);let n={adapter:(0,o.y)(i.z),providers:[(0,s.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let r=await i.z.user.findUnique({where:{email:e.email}});return r&&await a.Ay.compare(e.password,r.password)?{id:r.id,email:r.email,name:r.name,role:r.role}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(e.role=r.role),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role),e)},pages:{signIn:"/auth/signin"},secret:process.env.NEXTAUTH_SECRET}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},92747:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>j,serverHooks:()=>h,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>w});var s={};t.r(s),t.d(s,{DELETE:()=>x,GET:()=>l,PUT:()=>d});var o=t(96559),i=t(48088),a=t(37719),n=t(32190),u=t(19854),p=t(12909),c=t(94747);async function l(e,{params:r}){try{let{id:e}=await r,t=await c.z.project.findUnique({where:{id:e},include:{author:{select:{id:!0,name:!0,email:!0}}}});if(!t)return n.NextResponse.json({error:"Project not found"},{status:404});return n.NextResponse.json(t)}catch(e){return console.error("Error fetching project:",e),n.NextResponse.json({error:"Failed to fetch project"},{status:500})}}async function d(e,{params:r}){try{let t=await (0,u.getServerSession)(p.N);if(!t?.user)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{id:s}=await r,{title:o,description:i,longDescription:a,image:l,category:d,technologies:x,liveUrl:j,githubUrl:m,featured:w,published:h,order:f}=await e.json(),y=await c.z.project.update({where:{id:s},data:{title:o,description:i,longDescription:a,image:l,category:d,technologies:x,liveUrl:j,githubUrl:m,featured:w,published:h,order:f},include:{author:{select:{id:!0,name:!0,email:!0}}}});return n.NextResponse.json(y)}catch(e){return console.error("Error updating project:",e),n.NextResponse.json({error:"Failed to update project"},{status:500})}}async function x(e,{params:r}){try{let e=await (0,u.getServerSession)(p.N);if(!e?.user)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{id:t}=await r;return await c.z.project.delete({where:{id:t}}),n.NextResponse.json({message:"Project deleted successfully"})}catch(e){return console.error("Error deleting project:",e),n.NextResponse.json({error:"Failed to delete project"},{status:500})}}let j=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/projects/[id]/route",pathname:"/api/projects/[id]",filename:"route",bundlePath:"app/api/projects/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\api\\projects\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:w,serverHooks:h}=j;function f(){return(0,a.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:w})}},94735:e=>{"use strict";e.exports=require("events")},94747:(e,r,t)=>{"use strict";t.d(r,{z:()=>o});let s=require("@prisma/client"),o=globalThis.prisma??new s.PrismaClient},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,542,190],()=>t(92747));module.exports=s})();