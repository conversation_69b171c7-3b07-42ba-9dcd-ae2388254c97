(()=>{var e={};e.id=501,e.ids=[501],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{N:()=>n});var s=t(13581),a=t(16467),o=t(94747),i=t(85663);let n={adapter:(0,a.y)(o.z),providers:[(0,s.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let r=await o.z.user.findUnique({where:{email:e.email}});return r&&await i.Ay.compare(e.password,r.password)?{id:r.id,email:r.email,name:r.name,role:r.role}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(e.role=r.role),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role),e)},pages:{signIn:"/auth/signin"},secret:process.env.NEXTAUTH_SECRET}},27746:(e,r,t)=>{"use strict";t.d(r,{JB:()=>i,gx:()=>o});var s=t(32190);function a(){return{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, POST, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}}function o(e){return Object.entries(a()).forEach(([r,t])=>{e.headers.set(r,t)}),e}function i(){return new s.NextResponse(null,{status:200,headers:a()})}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55521:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>m,serverHooks:()=>y,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{GET:()=>d,OPTIONS:()=>h,POST:()=>x});var a=t(96559),o=t(48088),i=t(37719),n=t(32190),u=t(19854),c=t(12909),p=t(94747),l=t(27746);async function d(){try{let e=await p.z.techStack.findMany({orderBy:[{category:"asc"},{order:"asc"}]});return(0,l.gx)(n.NextResponse.json(e))}catch(e){return console.error("Error fetching tech stack:",e),(0,l.gx)(n.NextResponse.json({error:"Failed to fetch tech stack"},{status:500}))}}async function x(e){try{let r=await (0,u.getServerSession)(c.N);if(!r?.user)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{name:t,logo:s,color:a,category:o,order:i,published:d}=await e.json(),x=await p.z.techStack.create({data:{name:t,logo:s,color:a,category:o,order:i||0,published:void 0===d||d}});return(0,l.gx)(n.NextResponse.json(x,{status:201}))}catch(e){return console.error("Error creating tech:",e),(0,l.gx)(n.NextResponse.json({error:"Failed to create tech"},{status:500}))}}async function h(){return(0,l.JB)()}let m=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/tech-stack/route",pathname:"/api/tech-stack",filename:"route",bundlePath:"app/api/tech-stack/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\api\\tech-stack\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:w,workUnitAsyncStorage:g,serverHooks:y}=m;function f(){return(0,i.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:g})}},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},94747:(e,r,t)=>{"use strict";t.d(r,{z:()=>a});let s=require("@prisma/client"),a=globalThis.prisma??new s.PrismaClient},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,542,190],()=>t(55521));module.exports=s})();